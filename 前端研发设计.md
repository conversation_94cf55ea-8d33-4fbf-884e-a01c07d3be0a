# 运输管理系统前端研发设计文档

## 1. 项目概述

### 1.1 项目背景
开发独立的运输管理系统前端应用，专门用于油品运输业务的全流程管理界面，包括车辆管理、司机管理、运输单管理、任务调度、运价计算和对账管理等功能模块的用户界面。

### 1.2 技术架构
- **前端框架**: Vue.js 2.6+
- **UI组件库**: Element UI 2.15+
- **状态管理**: Vuex 3.6+
- **路由管理**: Vue Router 3.5+
- **HTTP客户端**: Axios 0.27+
- **国际化**: Vue I18n 8.27+
- **构建工具**: Vue CLI 4.5+ / Webpack 4+
- **代码规范**: ESLint + Prettier
- **包管理**: npm / yarn

### 1.3 系统特点
- 响应式设计，支持PC端和移动端
- 组件化开发，提高代码复用性
- 统一的UI风格和交互体验
- 完善的权限控制和路由守卫
- 多语言国际化支持，默认支持中文和葡萄牙语

## 2. 项目结构设计

### 2.1 目录结构
```
transport-frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── api/                 # API接口
│   │   ├── transport.js
│   │   ├── vehicle.js
│   │   ├── driver.js
│   │   └── region.js
│   ├── assets/              # 静态资源
│   │   ├── images/
│   │   ├── icons/
│   │   └── styles/
│   ├── components/          # 公共组件
│   │   ├── common/
│   │   ├── form/
│   │   └── table/
│   ├── i18n/                # 国际化配置
│   │   ├── index.js
│   │   ├── zh-CN.js
│   │   └── pt-BR.js
│   ├── layout/              # 布局组件
│   │   ├── index.vue
│   │   ├── sidebar.vue
│   │   └── navbar.vue
│   ├── router/              # 路由配置
│   │   └── index.js
│   ├── store/               # Vuex状态管理
│   │   ├── modules/
│   │   └── index.js
│   ├── utils/               # 工具函数
│   │   ├── request.js
│   │   ├── auth.js
│   │   └── validate.js
│   ├── views/               # 页面组件
│   │   ├── transport/
│   │   ├── vehicle/
│   │   ├── driver/
│   │   └── dashboard/
│   ├── App.vue
│   └── main.js
├── package.json
└── vue.config.js
```

### 2.2 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 公共组件抽象化，提高复用率
- **可维护性**: 清晰的组件结构和命名规范
- **性能优化**: 合理使用组件懒加载和缓存

## 3. 国际化配置

### 3.1 Vue I18n配置
```javascript
// i18n/index.js
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zhCN from './zh-CN'
import ptBR from './pt-BR'
import elementZhCN from 'element-ui/lib/locale/lang/zh-CN'
import elementPtBR from 'element-ui/lib/locale/lang/pt-br'

Vue.use(VueI18n)

const messages = {
  'zh-CN': {
    ...zhCN,
    ...elementZhCN
  },
  'pt-BR': {
    ...ptBR,
    ...elementPtBR
  }
}

const i18n = new VueI18n({
  locale: localStorage.getItem('language') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages
})

export default i18n
```

### 3.2 中文语言包
```javascript
// i18n/zh-CN.js
export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '新增',
    search: '搜索',
    reset: '重置',
    export: '导出',
    import: '导入',
    submit: '提交',
    back: '返回',
    view: '查看',
    operation: '操作',
    status: '状态',
    remark: '备注',
    createTime: '创建时间',
    updateTime: '更新时间',
    pleaseSelect: '请选择',
    pleaseInput: '请输入'
  },

  // 导航菜单
  menu: {
    dashboard: '首页',
    transport: '运输管理',
    transportOrder: '运输单管理',
    vehicle: '车辆管理',
    driver: '司机管理',
    customer: '客户管理',
    warehouse: '仓库管理',
    inventory: '库存管理',
    billing: '对账管理'
  },

  // 运输单
  transportOrder: {
    title: '运输单管理',
    orderNo: '运输单号',
    internalCode: '内部编号',
    customer: '委托客户',
    consignee: '收货方',
    loadingPoint: '装货点',
    productName: '油品名称',
    productQuantity: '运输数量(吨)',
    totalVolume: '总体积(升)',
    transportDistance: '运输距离(公里)',
    shippingCost: '运输费用',
    otherExpenses: '其他费用',
    taxIncluded: '含税单价',
    plannedLoadingTime: '计划装货时间',
    plannedDeliveryTime: '计划送达时间',
    deliveryRequirements: '配送要求',
    specialInstructions: '特殊说明',
    createOrder: '新增运输单',
    editOrder: '编辑运输单',
    orderDetail: '运输单详情',
    assignVehicle: '指派车辆',
    calculatePrice: '计算运费',
    saveDraft: '保存草稿',
    status: {
      pending: '待指派',
      assigned: '已指派',
      loading: '装货中',
      transporting: '运输中',
      delivered: '已送达',
      billed: '已对账'
    }
  },

  // 车辆管理
  vehicle: {
    title: '车辆管理',
    licensePlate: '车牌号',
    vehicleType: '车辆类型',
    loadCapacity: '载重吨位',
    fuelTankCapacity: '油箱容量',
    vehicleStatus: '车辆状态',
    purchaseDate: '购买日期',
    annualInspectionDate: '年检日期',
    insuranceExpiryDate: '保险到期日期',
    status: {
      idle: '空闲',
      transporting: '运输中',
      maintenance: '维修中',
      scrapped: '报废'
    }
  },

  // 司机管理
  driver: {
    title: '司机管理',
    driverName: '司机姓名',
    driverPhone: '联系电话',
    idCard: '身份证号',
    licenseType: '驾照类型',
    licenseNumber: '驾照号码',
    drivingYears: '驾龄(年)',
    driverStatus: '司机状态',
    hireDate: '入职日期',
    emergencyContact: '紧急联系人',
    emergencyPhone: '紧急联系电话',
    status: {
      active: '在职',
      vacation: '休假',
      resigned: '离职'
    }
  },

  // 客户管理
  customer: {
    title: '客户管理',
    customerName: '客户名称',
    customerCode: '客户编码',
    companyType: '公司类型',
    contactPerson: '联系人',
    contactPhone: '联系电话',
    contactEmail: '联系邮箱',
    address: '地址',
    province: '省份',
    city: '城市',
    district: '区县',
    detailAddress: '详细地址'
  },

  // 表单验证
  validation: {
    required: '此字段为必填项',
    minLength: '长度不能少于{min}个字符',
    maxLength: '长度不能超过{max}个字符',
    invalidFormat: '格式不正确',
    invalidPhone: '手机号格式不正确',
    invalidEmail: '邮箱格式不正确',
    mustBeNumber: '必须为数字',
    mustBePositive: '必须为正数'
  },

  // 消息提示
  message: {
    success: '操作成功',
    error: '操作失败',
    deleteConfirm: '确定要删除这条记录吗？',
    saveSuccess: '保存成功',
    deleteSuccess: '删除成功',
    updateSuccess: '更新成功',
    createSuccess: '创建成功',
    networkError: '网络错误，请稍后重试',
    serverError: '服务器错误',
    unauthorized: '未授权，请重新登录',
    forbidden: '权限不足'
  }
}
```

### 3.3 葡萄牙语语言包
```javascript
// i18n/pt-BR.js
export default {
  // 通用
  common: {
    confirm: 'Confirmar',
    cancel: 'Cancelar',
    save: 'Salvar',
    delete: 'Excluir',
    edit: 'Editar',
    add: 'Adicionar',
    search: 'Pesquisar',
    reset: 'Redefinir',
    export: 'Exportar',
    import: 'Importar',
    submit: 'Enviar',
    back: 'Voltar',
    view: 'Visualizar',
    operation: 'Operação',
    status: 'Status',
    remark: 'Observação',
    createTime: 'Data de Criação',
    updateTime: 'Data de Atualização',
    pleaseSelect: 'Por favor, selecione',
    pleaseInput: 'Por favor, digite'
  },

  // 导航菜单
  menu: {
    dashboard: 'Painel',
    transport: 'Gestão de Transporte',
    transportOrder: 'Gestão de Ordens de Transporte',
    vehicle: 'Gestão de Veículos',
    driver: 'Gestão de Motoristas',
    customer: 'Gestão de Clientes',
    warehouse: 'Gestão de Armazém',
    inventory: 'Gestão de Estoque',
    billing: 'Gestão de Faturamento'
  },

  // 运输单
  transportOrder: {
    title: 'Gestão de Ordens de Transporte',
    orderNo: 'Número da Ordem',
    internalCode: 'Código Interno',
    customer: 'Cliente Contratante',
    consignee: 'Destinatário',
    loadingPoint: 'Ponto de Carregamento',
    productName: 'Nome do Produto',
    productQuantity: 'Quantidade de Transporte (ton)',
    totalVolume: 'Volume Total (L)',
    transportDistance: 'Distância de Transporte (km)',
    shippingCost: 'Custo de Transporte',
    otherExpenses: 'Outras Despesas',
    taxIncluded: 'Preço com Impostos',
    plannedLoadingTime: 'Horário Planejado de Carregamento',
    plannedDeliveryTime: 'Horário Planejado de Entrega',
    deliveryRequirements: 'Requisitos de Entrega',
    specialInstructions: 'Instruções Especiais',
    createOrder: 'Criar Ordem de Transporte',
    editOrder: 'Editar Ordem de Transporte',
    orderDetail: 'Detalhes da Ordem',
    assignVehicle: 'Atribuir Veículo',
    calculatePrice: 'Calcular Frete',
    saveDraft: 'Salvar Rascunho',
    status: {
      pending: 'Pendente',
      assigned: 'Atribuído',
      loading: 'Carregando',
      transporting: 'Transportando',
      delivered: 'Entregue',
      billed: 'Faturado'
    }
  },

  // 车辆管理
  vehicle: {
    title: 'Gestão de Veículos',
    licensePlate: 'Placa do Veículo',
    vehicleType: 'Tipo de Veículo',
    loadCapacity: 'Capacidade de Carga',
    fuelTankCapacity: 'Capacidade do Tanque',
    vehicleStatus: 'Status do Veículo',
    purchaseDate: 'Data de Compra',
    annualInspectionDate: 'Data da Inspeção Anual',
    insuranceExpiryDate: 'Data de Vencimento do Seguro',
    status: {
      idle: 'Disponível',
      transporting: 'Transportando',
      maintenance: 'Em Manutenção',
      scrapped: 'Sucateado'
    }
  },

  // 司机管理
  driver: {
    title: 'Gestão de Motoristas',
    driverName: 'Nome do Motorista',
    driverPhone: 'Telefone de Contato',
    idCard: 'Número do RG',
    licenseType: 'Tipo de Carteira',
    licenseNumber: 'Número da Carteira',
    drivingYears: 'Anos de Experiência',
    driverStatus: 'Status do Motorista',
    hireDate: 'Data de Contratação',
    emergencyContact: 'Contato de Emergência',
    emergencyPhone: 'Telefone de Emergência',
    status: {
      active: 'Ativo',
      vacation: 'Férias',
      resigned: 'Demitido'
    }
  },

  // 客户管理
  customer: {
    title: 'Gestão de Clientes',
    customerName: 'Nome do Cliente',
    customerCode: 'Código do Cliente',
    companyType: 'Tipo de Empresa',
    contactPerson: 'Pessoa de Contato',
    contactPhone: 'Telefone de Contato',
    contactEmail: 'E-mail de Contato',
    address: 'Endereço',
    province: 'Estado',
    city: 'Cidade',
    district: 'Distrito',
    detailAddress: 'Endereço Detalhado'
  },

  // 表单验证
  validation: {
    required: 'Este campo é obrigatório',
    minLength: 'O comprimento não pode ser menor que {min} caracteres',
    maxLength: 'O comprimento não pode exceder {max} caracteres',
    invalidFormat: 'Formato incorreto',
    invalidPhone: 'Formato de telefone incorreto',
    invalidEmail: 'Formato de e-mail incorreto',
    mustBeNumber: 'Deve ser um número',
    mustBePositive: 'Deve ser um número positivo'
  },

  // 消息提示
  message: {
    success: 'Operação realizada com sucesso',
    error: 'Falha na operação',
    deleteConfirm: 'Tem certeza de que deseja excluir este registro?',
    saveSuccess: 'Salvo com sucesso',
    deleteSuccess: 'Excluído com sucesso',
    updateSuccess: 'Atualizado com sucesso',
    createSuccess: 'Criado com sucesso',
    networkError: 'Erro de rede, tente novamente mais tarde',
    serverError: 'Erro do servidor',
    unauthorized: 'Não autorizado, faça login novamente',
    forbidden: 'Permissões insuficientes'
  }
}
```

### 3.4 语言切换组件
```vue
<template>
  <el-dropdown @command="changeLanguage" class="language-selector">
    <span class="el-dropdown-link">
      <i class="el-icon-s-grid"></i>
      {{ currentLanguageText }}
      <i class="el-icon-arrow-down el-icon--right"></i>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item command="zh-CN" :class="{ active: currentLanguage === 'zh-CN' }">
        <span class="flag-icon flag-icon-cn"></span>
        中文
      </el-dropdown-item>
      <el-dropdown-item command="pt-BR" :class="{ active: currentLanguage === 'pt-BR' }">
        <span class="flag-icon flag-icon-br"></span>
        Português
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'LanguageSelector',
  computed: {
    currentLanguage() {
      return this.$i18n.locale
    },
    currentLanguageText() {
      const languageMap = {
        'zh-CN': '中文',
        'pt-BR': 'Português'
      }
      return languageMap[this.currentLanguage] || '中文'
    }
  },
  methods: {
    changeLanguage(lang) {
      this.$i18n.locale = lang
      localStorage.setItem('language', lang)

      // 更新Element UI语言
      this.$ELEMENT.locale = this.$i18n.messages[lang]

      // 更新页面标题
      document.title = this.$t('menu.transport')

      // 通知其他组件语言已切换
      this.$emit('language-changed', lang)

      // 可选：刷新页面以确保所有组件都使用新语言
      // location.reload()
    }
  }
}
</script>

<style scoped>
.language-selector {
  cursor: pointer;
}

.flag-icon {
  margin-right: 8px;
}

.active {
  color: #409EFF;
  font-weight: bold;
}
</style>
```

### 3.5 主应用配置
```javascript
// main.js
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import i18n from './i18n'
import ElementUI from 'element-ui'

// 根据当前语言设置Element UI语言
import zhCN from 'element-ui/lib/locale/lang/zh-CN'
import ptBR from 'element-ui/lib/locale/lang/pt-br'

const elementLocaleMap = {
  'zh-CN': zhCN,
  'pt-BR': ptBR
}

const currentLang = localStorage.getItem('language') || 'zh-CN'
Vue.use(ElementUI, {
  locale: elementLocaleMap[currentLang]
})

// 全局过滤器
Vue.filter('numberFormat', function (value) {
  if (!value) return '0.00'
  return Number(value).toLocaleString('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
})

// 全局混入
Vue.mixin({
  methods: {
    // 国际化消息提示
    $successMsg(key, params = {}) {
      this.$message.success(this.$t(key, params))
    },
    $errorMsg(key, params = {}) {
      this.$message.error(this.$t(key, params))
    },
    $warningMsg(key, params = {}) {
      this.$message.warning(this.$t(key, params))
    },
    // 国际化确认对话框
    $confirmMsg(key, params = {}) {
      return this.$confirm(this.$t(key, params), this.$t('common.confirm'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      })
    }
  }
})

new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')
```

## 4. 核心功能模块

### 4.1 运输单管理模块

#### 4.1.1 运输单列表页面
```vue
<template>
  <div class="transport-order-list">
    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="queryForm">
        <el-form-item :label="$t('transportOrder.orderNo')">
          <el-input v-model="queryForm.orderNo" :placeholder="$t('common.pleaseInput') + $t('transportOrder.orderNo')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('transportOrder.customer')">
          <el-select v-model="queryForm.customerId" :placeholder="$t('common.pleaseSelect') + $t('transportOrder.customer')">
            <el-option v-for="customer in customerList"
                       :key="customer.id"
                       :label="customer.customerName"
                       :value="customer.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.status')">
          <el-select v-model="queryForm.orderStatus" :placeholder="$t('common.pleaseSelect') + $t('common.status')">
            <el-option :label="$t('transportOrder.status.pending')" :value="1"></el-option>
            <el-option :label="$t('transportOrder.status.assigned')" :value="2"></el-option>
            <el-option :label="$t('transportOrder.status.transporting')" :value="5"></el-option>
            <el-option :label="$t('transportOrder.status.delivered')" :value="6"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
          <el-button @click="resetQuery">{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">{{ $t('transportOrder.createOrder') }}</el-button>
      <el-button type="success" @click="handleExport">{{ $t('common.export') }}</el-button>
    </div>
    
    <!-- 数据表格 -->
    <el-table :data="orderList" border stripe>
      <el-table-column prop="orderNo" :label="$t('transportOrder.orderNo')" width="150"></el-table-column>
      <el-table-column prop="customerName" :label="$t('transportOrder.customer')" width="120"></el-table-column>
      <el-table-column prop="consigneeName" :label="$t('transportOrder.consignee')" width="120"></el-table-column>
      <el-table-column prop="productName" :label="$t('transportOrder.productName')" width="100"></el-table-column>
      <el-table-column prop="productQuantity" :label="$t('transportOrder.productQuantity')" width="80" align="right"></el-table-column>
      <el-table-column prop="orderStatus" :label="$t('common.status')" width="80">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.orderStatus)">
            {{ getStatusText(scope.row.orderStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="shippingCost" :label="$t('transportOrder.shippingCost')" width="100" align="right">
        <template slot-scope="scope">
          ¥{{ scope.row.shippingCost | numberFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="$t('common.createTime')" width="150"></el-table-column>
      <el-table-column :label="$t('common.operation')" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleView(scope.row)">{{ $t('common.view') }}</el-button>
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)"
                     v-if="scope.row.orderStatus === 1">{{ $t('common.edit') }}</el-button>
          <el-button size="mini" type="warning" @click="handleAssign(scope.row)"
                     v-if="scope.row.orderStatus === 1">{{ $t('transportOrder.assignVehicle') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页组件 -->
    <pagination 
      :total="total" 
      :page.sync="queryForm.pageNum" 
      :limit.sync="queryForm.pageSize" 
      @pagination="getList">
    </pagination>
  </div>
</template>

<script>
import { getOrderList, deleteOrder } from '@/api/transport'
import { getCustomerList } from '@/api/customer'

export default {
  name: 'TransportOrderList',
  data() {
    return {
      orderList: [],
      customerList: [],
      total: 0,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        orderNo: '',
        customerId: null,
        orderStatus: null
      }
    }
  },
  created() {
    this.getList()
    this.getCustomerList()
  },
  methods: {
    async getList() {
      try {
        const response = await getOrderList(this.queryForm)
        this.orderList = response.data.list
        this.total = response.data.total
      } catch (error) {
        this.$message.error('获取运输单列表失败')
      }
    },
    
    async getCustomerList() {
      try {
        const response = await getCustomerList()
        this.customerList = response.data
      } catch (error) {
        this.$message.error('获取客户列表失败')
      }
    },
    
    handleQuery() {
      this.queryForm.pageNum = 1
      this.getList()
    },
    
    resetQuery() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        orderNo: '',
        customerId: null,
        orderStatus: null
      }
      this.getList()
    },
    
    handleAdd() {
      this.$router.push('/transport/order/add')
    },
    
    handleEdit(row) {
      this.$router.push(`/transport/order/edit/${row.id}`)
    },
    
    handleView(row) {
      this.$router.push(`/transport/order/detail/${row.id}`)
    },
    
    handleAssign(row) {
      this.$router.push(`/transport/order/assign/${row.id}`)
    },
    
    getStatusType(status) {
      const statusMap = {
        1: 'info',    // 待指派
        2: 'warning', // 已指派
        3: 'warning', // 前往装货
        4: 'warning', // 装货中
        5: 'primary', // 运输中
        6: 'success', // 已送达
        7: 'success'  // 已对账
      }
      return statusMap[status] || 'info'
    },
    
    getStatusText(status) {
      const statusMap = {
        1: this.$t('transportOrder.status.pending'),
        2: this.$t('transportOrder.status.assigned'),
        3: this.$t('transportOrder.status.loading'),
        4: this.$t('transportOrder.status.loading'),
        5: this.$t('transportOrder.status.transporting'),
        6: this.$t('transportOrder.status.delivered'),
        7: this.$t('transportOrder.status.billed')
      }
      return statusMap[status] || this.$t('common.unknown')
    }
  }
}
</script>
```

#### 3.1.2 运输单新增页面
```vue
<template>
  <div class="transport-order-form">
    <div class="page-header">
      <h2>新增运输单</h2>
      <div class="action-buttons">
        <el-button @click="saveDraft">保存草稿</el-button>
        <el-button type="primary" @click="submitOrder">提交</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>
    
    <el-form :model="orderForm" :rules="formRules" ref="orderForm" label-width="120px">
      <!-- 委托信息区域 -->
      <el-card title="委托信息" class="form-card">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="委托客户" prop="customerId">
              <el-select v-model="orderForm.customerId" 
                         filterable 
                         @change="onCustomerChange"
                         placeholder="请选择委托客户">
                <el-option v-for="customer in customerList" 
                           :key="customer.id" 
                           :label="customer.customerName" 
                           :value="customer.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="油品名称" prop="productName">
              <el-input v-model="orderForm.productName" placeholder="请输入油品名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运输数量(吨)" prop="productQuantity">
              <el-input-number v-model="orderForm.productQuantity" 
                             :precision="2" 
                             :min="0" 
                             placeholder="请输入运输数量">
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 装货信息区域 -->
      <el-card title="装货信息" class="form-card">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="装货点" prop="loadingPointId">
              <el-select v-model="orderForm.loadingPointId" 
                         filterable 
                         @change="onLoadingPointChange"
                         placeholder="请选择装货点">
                <el-option v-for="point in loadingPointList" 
                           :key="point.id" 
                           :label="point.pointName" 
                           :value="point.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装货地址">
              <el-input v-model="orderForm.loadingAddress" 
                        type="textarea" 
                        :readonly="true"
                        placeholder="选择装货点后自动填充">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 收货信息区域 -->
      <el-card title="收货信息" class="form-card">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="收货方" prop="consigneeId">
              <el-select v-model="orderForm.consigneeId" 
                         filterable 
                         @change="onConsigneeChange"
                         placeholder="请选择收货方">
                <el-option v-for="consignee in consigneeList" 
                           :key="consignee.id" 
                           :label="consignee.consigneeName" 
                           :value="consignee.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人">
              <el-input v-model="orderForm.consigneeContact" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话">
              <el-input v-model="orderForm.consigneePhone" :readonly="true"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 地址信息 -->
        <region-selector 
          v-model="orderForm.consigneeAddress"
          :province-code.sync="orderForm.consigneeProvinceCode"
          :city-code.sync="orderForm.consigneeCityCode"
          :district-code.sync="orderForm.consigneeDistrictCode"
          :readonly="true">
        </region-selector>
      </el-card>
      
      <!-- 费用信息区域 -->
      <el-card title="费用信息" class="form-card">
        <div class="price-calculation">
          <el-button @click="calculatePrice">重新计算运费</el-button>
          <div class="price-details">
            <p>基础运费: ¥{{ priceDetails.basePrice || 0 }}</p>
            <p>附加费用: ¥{{ priceDetails.additionalFees || 0 }}</p>
            <p class="total-price">总计: ¥{{ priceDetails.totalPrice || 0 }}</p>
          </div>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="运输费用" prop="shippingCost">
              <el-input-number v-model="orderForm.shippingCost" 
                             :precision="2" 
                             :min="0"
                             placeholder="运输费用">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他费用">
              <el-input-number v-model="orderForm.otherExpenses" 
                             :precision="2" 
                             :min="0"
                             placeholder="其他费用">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="含税单价">
              <el-input-number v-model="orderForm.taxIncluded" 
                             :precision="2" 
                             :min="0"
                             placeholder="含税单价">
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { createOrder, calculatePrice } from '@/api/transport'
import { getCustomerList } from '@/api/customer'
import { getLoadingPointList } from '@/api/loading-point'
import { getConsigneeList } from '@/api/consignee'
import RegionSelector from '@/components/RegionSelector'

export default {
  name: 'TransportOrderForm',
  components: {
    RegionSelector
  },
  data() {
    return {
      orderForm: {
        customerId: null,
        customerName: '',
        consigneeId: null,
        consigneeName: '',
        consigneeContact: '',
        consigneePhone: '',
        consigneeProvinceCode: '',
        consigneeCityCode: '',
        consigneeDistrictCode: '',
        consigneeAddress: '',
        loadingPointId: null,
        loadingPointName: '',
        loadingAddress: '',
        productName: '',
        productQuantity: null,
        totalVolume: null,
        transportDistance: null,
        plannedLoadingTime: null,
        plannedDeliveryTime: null,
        shippingCost: null,
        otherExpenses: 0,
        taxIncluded: null,
        deliveryRequirements: '',
        specialInstructions: '',
        remark: ''
      },
      customerList: [],
      loadingPointList: [],
      consigneeList: [],
      priceDetails: {},
      formRules: {
        customerId: [
          { required: true, message: '请选择委托客户', trigger: 'change' }
        ],
        loadingPointId: [
          { required: true, message: '请选择装货点', trigger: 'change' }
        ],
        consigneeId: [
          { required: true, message: '请选择收货方', trigger: 'change' }
        ],
        productName: [
          { required: true, message: '请输入油品名称', trigger: 'blur' }
        ],
        productQuantity: [
          { required: true, message: '请输入运输数量', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '运输数量必须大于0', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadInitData()
  },
  methods: {
    async loadInitData() {
      await Promise.all([
        this.getCustomerList(),
        this.getLoadingPointList()
      ])
    },
    
    // 客户变更时加载对应的收货方列表
    async onCustomerChange(customerId) {
      this.orderForm.consigneeId = null
      this.orderForm.consigneeName = ''
      this.orderForm.consigneeAddress = ''
      this.orderForm.consigneeContact = ''
      this.orderForm.consigneePhone = ''
      
      const selectedCustomer = this.customerList.find(c => c.id === customerId)
      if (selectedCustomer) {
        this.orderForm.customerName = selectedCustomer.customerName
        await this.getConsigneeList(customerId)
      }
    },
    
    // 装货点变更时自动填充地址
    onLoadingPointChange(loadingPointId) {
      const selectedPoint = this.loadingPointList.find(p => p.id === loadingPointId)
      if (selectedPoint) {
        this.orderForm.loadingPointName = selectedPoint.pointName
        this.orderForm.loadingAddress = selectedPoint.fullAddress
      }
    },
    
    // 收货方变更时自动填充信息
    onConsigneeChange(consigneeId) {
      const selectedConsignee = this.consigneeList.find(c => c.id === consigneeId)
      if (selectedConsignee) {
        this.orderForm.consigneeName = selectedConsignee.consigneeName
        this.orderForm.consigneeContact = selectedConsignee.contactPerson
        this.orderForm.consigneePhone = selectedConsignee.contactPhone
        this.orderForm.consigneeProvinceCode = selectedConsignee.provinceCode
        this.orderForm.consigneeCityCode = selectedConsignee.cityCode
        this.orderForm.consigneeDistrictCode = selectedConsignee.districtCode
        this.orderForm.consigneeAddress = selectedConsignee.detailAddress
      }
    },
    
    // 计算运费
    async calculatePrice() {
      if (!this.orderForm.loadingPointId || !this.orderForm.consigneeId) {
        this.$message.warning('请先选择装货点和收货方')
        return
      }
      
      try {
        const response = await calculatePrice({
          loadingPointId: this.orderForm.loadingPointId,
          consigneeId: this.orderForm.consigneeId,
          productQuantity: this.orderForm.productQuantity,
          totalVolume: this.orderForm.totalVolume,
          transportDistance: this.orderForm.transportDistance
        })
        
        this.priceDetails = response.data
        this.orderForm.shippingCost = response.data.totalPrice
      } catch (error) {
        this.$message.error('运费计算失败')
      }
    },
    
    // 提交表单
    async submitOrder() {
      try {
        await this.$refs.orderForm.validate()
        
        const response = await createOrder(this.orderForm)
        this.$message.success('运输单创建成功')
        this.$router.push('/transport/order')
      } catch (error) {
        if (error.message) {
          this.$message.error(error.message)
        }
      }
    },
    
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.form-card {
  margin-bottom: 20px;
}

.price-calculation {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.price-details {
  margin-left: 20px;
}

.total-price {
  font-weight: bold;
  color: #409EFF;
}
</style>
```

## 4. 公共组件设计

### 4.1 省市区选择组件
```vue
<template>
  <div class="region-selector">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="省份" prop="provinceCode">
          <el-select v-model="provinceCode" 
                     @change="onProvinceChange" 
                     filterable
                     :disabled="readonly"
                     placeholder="请选择省份">
            <el-option v-for="province in provinceList" 
                       :key="province.provinceCode" 
                       :label="province.provinceName" 
                       :value="province.provinceCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="城市" prop="cityCode">
          <el-select v-model="cityCode" 
                     @change="onCityChange" 
                     filterable
                     :disabled="readonly"
                     placeholder="请选择城市">
            <el-option v-for="city in cityList" 
                       :key="city.cityCode" 
                       :label="city.cityName" 
                       :value="city.cityCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="区县" prop="districtCode">
          <el-select v-model="districtCode" 
                     @change="onDistrictChange" 
                     filterable
                     :disabled="readonly"
                     placeholder="请选择区县">
            <el-option v-for="district in districtList" 
                       :key="district.districtCode" 
                       :label="district.districtName" 
                       :value="district.districtCode">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="详细地址" prop="detailAddress">
          <el-input v-model="detailAddress" 
                    type="textarea" 
                    :readonly="readonly"
                    placeholder="请输入详细地址">
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getProvinces, getCities, getDistricts } from '@/api/region'

export default {
  name: 'RegionSelector',
  props: {
    provinceCode: String,
    cityCode: String,
    districtCode: String,
    value: String,
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      provinceList: [],
      cityList: [],
      districtList: [],
      detailAddress: this.value
    }
  },
  watch: {
    detailAddress(val) {
      this.$emit('input', val)
    },
    provinceCode: {
      immediate: true,
      handler(val) {
        if (val) {
          this.loadCities(val)
        }
      }
    },
    cityCode: {
      immediate: true,
      handler(val) {
        if (val) {
          this.loadDistricts(val)
        }
      }
    }
  },
  created() {
    this.loadProvinces()
  },
  methods: {
    async loadProvinces() {
      try {
        const response = await getProvinces()
        this.provinceList = response.data
      } catch (error) {
        this.$message.error('加载省份列表失败')
      }
    },
    
    async loadCities(provinceCode) {
      try {
        const response = await getCities({ provinceCode })
        this.cityList = response.data
      } catch (error) {
        this.$message.error('加载城市列表失败')
      }
    },
    
    async loadDistricts(cityCode) {
      try {
        const response = await getDistricts({ cityCode })
        this.districtList = response.data
      } catch (error) {
        this.$message.error('加载区县列表失败')
      }
    },
    
    onProvinceChange(provinceCode) {
      this.$emit('update:provinceCode', provinceCode)
      this.$emit('update:cityCode', '')
      this.$emit('update:districtCode', '')
      this.cityList = []
      this.districtList = []
      
      if (provinceCode) {
        this.loadCities(provinceCode)
      }
    },
    
    onCityChange(cityCode) {
      this.$emit('update:cityCode', cityCode)
      this.$emit('update:districtCode', '')
      this.districtList = []
      
      if (cityCode) {
        this.loadDistricts(cityCode)
      }
    },
    
    onDistrictChange(districtCode) {
      this.$emit('update:districtCode', districtCode)
    }
  }
}
</script>
```

## 5. 状态管理设计

### 5.1 Vuex Store结构
```javascript
// store/index.js
import Vue from 'vue'
import Vuex from 'vuex'
import transport from './modules/transport'
import user from './modules/user'
import app from './modules/app'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    transport,
    user,
    app
  }
})

// store/modules/transport.js
const state = {
  orderList: [],
  currentOrder: null,
  customerList: [],
  vehicleList: [],
  driverList: []
}

const mutations = {
  SET_ORDER_LIST(state, list) {
    state.orderList = list
  },
  SET_CURRENT_ORDER(state, order) {
    state.currentOrder = order
  },
  SET_CUSTOMER_LIST(state, list) {
    state.customerList = list
  }
}

const actions = {
  async fetchOrderList({ commit }, params) {
    try {
      const response = await getOrderList(params)
      commit('SET_ORDER_LIST', response.data.list)
      return response.data
    } catch (error) {
      throw error
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
```

## 6. 路由设计

### 6.1 路由配置
```javascript
// router/index.js
import Vue from 'vue'
import Router from 'vue-router'
import Layout from '@/layout'

Vue.use(Router)

export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index'),
        meta: { title: '首页', icon: 'dashboard' }
      }
    ]
  },
  {
    path: '/transport',
    component: Layout,
    name: 'Transport',
    meta: { title: '运输管理', icon: 'truck' },
    children: [
      {
        path: 'order',
        name: 'TransportOrder',
        component: () => import('@/views/transport/order/index'),
        meta: { title: '运输单管理', icon: 'list' }
      },
      {
        path: 'order/add',
        name: 'TransportOrderAdd',
        component: () => import('@/views/transport/order/form'),
        meta: { title: '新增运输单', activeMenu: '/transport/order' },
        hidden: true
      },
      {
        path: 'order/edit/:id',
        name: 'TransportOrderEdit',
        component: () => import('@/views/transport/order/form'),
        meta: { title: '编辑运输单', activeMenu: '/transport/order' },
        hidden: true
      }
    ]
  }
]

const createRouter = () => new Router({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

export default router
```

## 7. 工具函数设计

### 7.1 HTTP请求封装
```javascript
// utils/request.js
import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 5000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['Authorization'] = 'Bearer ' + getToken()
    }

    // 添加国际化请求头
    const language = localStorage.getItem('language') || 'zh-CN'
    config.headers['Accept-Language'] = language

    return config
  },
  error => {
    console.log(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    if (res.code !== 200) {
      Message({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      
      if (res.code === 401) {
        MessageBox.confirm('登录状态已过期，请重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.log('err' + error)
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
```

## 8. 构建配置

### 8.1 Vue配置文件
```javascript
// vue.config.js
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/transport/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: 8080,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: 'http://localhost:6877',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/transport/api'
        }
      }
    }
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  },
  chainWebpack(config) {
    config.plugins.delete('preload')
    config.plugins.delete('prefetch')
    
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  }
}
```

## 9. 性能优化

### 9.1 优化策略
- **路由懒加载**: 使用动态import实现按需加载
- **组件缓存**: 使用keep-alive缓存组件状态
- **图片优化**: 使用webp格式，实现懒加载
- **代码分割**: 合理配置webpack分包策略
- **CDN加速**: 第三方库使用CDN引入

### 9.2 监控和调试
- **性能监控**: 使用Performance API监控页面性能
- **错误监控**: 集成Sentry进行错误收集
- **用户行为**: 埋点统计用户操作行为

## 10. 部署配置

### 10.1 环境配置
```javascript
// .env.development
VUE_APP_BASE_API = '/api'
VUE_APP_ENV = 'development'
VUE_APP_DEFAULT_LANGUAGE = 'zh-CN'
VUE_APP_SUPPORTED_LANGUAGES = 'zh-CN,pt-BR'

// .env.production
VUE_APP_BASE_API = 'https://api.transport.com'
VUE_APP_ENV = 'production'
VUE_APP_DEFAULT_LANGUAGE = 'zh-CN'
VUE_APP_SUPPORTED_LANGUAGES = 'zh-CN,pt-BR'
```

### 10.2 构建部署
```bash
# 安装依赖
npm install

# 开发环境启动
npm run serve

# 生产环境构建
npm run build

# 代码检查
npm run lint
```
