# 运输系统需求文档

## 1. 引言

### 1.1. 项目背景

为了提升油品运输业务的管理效率和专业化水平，需要开发一套完全独立的运输管理系统。该系统将专注于油品从装货点到最终收货方的全程运输管理，旨在优化业务流程、加强资源管控、并实现精确的成本核算。

### 1.2. 项目目标

- **核心目标**: 实现运输业务全流程的数字化管理，包括车辆、司机、客户、订单、调度、计费及对账等。
- **独立性**: 系统需独立部署和运行，不依赖于任何其他现有业务系统。
- **效率提升**: 优化运输订单处理、任务调度和状态跟踪，减少人工干预，提高响应速度。
- **成本控制**: 通过精细化的运价管理和对账功能，实现对运输成本的精确控制和核算。
- **数据驱动**: 提供数据统计和分析功能，为管理决策提供数据支持。

## 2. 整体说明

### 2.1. 系统概述

本系统是一个专注于油品运输管理的B/S架构软件，主要用户为运输公司的管理人员、调度员和财务人员。系统覆盖了从接收运输委托到完成费用结算的完整业务闭环。

### 2.2. 技术要求

- **后端**: 采用Spring Boot 2.5.15框架。
- **前端**: 采用Vue.js + Element UI技术栈。
- **数据库**: 使用MySQL 8.0。
- **缓存**: 使用Redis，并为本系统设置独立的前缀 `transport:`。
- **部署**: 系统需独立部署，使用端口 `6877`，上下文路径为 `/transport`。

## 3. 功能需求

### 3.1. 车辆管理

#### 3.1.1. 车辆档案

- **需求**: 系统需支持对运输车辆信息的增、删、改、查。
- **数据字段**:
    - 基础信息: 车牌号（唯一）、车辆类型、载重吨位、油箱容量、购买日期。
    - 状态信息: 车辆状态（空闲、运输中、维修中、报废）、所属区域。
    - 资质信息: 年检日期、保险到期日期。
- **核心功能**:
    - 提供车辆列表，支持按区域、载重、状态等条件筛选。
    - 车辆年检和保险到期前，系统需提供预警提醒。

#### 3.1.2. 车辆维护管理

- **需求**: 系统需能记录车辆的维修和保养历史。
- **数据字段**: 关联车辆、维护类型、维护日期、维护费用、维护描述、下次维护日期。
- **核心功能**:
    - 记录每次维护的详细信息。
    - 支持根据下次维护日期生成提醒。

### 3.2. 司机管理

#### 3.2.1. 司机档案

- **需求**: 系统需支持对司机信息的增、删、改、查。
- **数据字段**:
    - 基础信息: 司机姓名、联系电话、身份证号。
    - 资质信息: 驾照类型、驾照号码、驾龄。
    - 在职信息: 司机状态（在职、休假、离职）、入职日期。
    - 紧急联系方式。
- **核心功能**:
    - 提供司机列表及详细信息查看。
    - 驾照到期前，系统需提供预警提醒。

#### 3.2.2. 司机排班

- **需求**: 系统需支持对司机的工作班次进行管理。
- **数据字段**: 关联司机、工作日期、班次类型（白班、夜班、休息）、上下班时间。
- **核心功能**:
    - 支持为司机安排未来的工作班次。
    - 统计司机实际工作时长。

### 3.3. 客户与地址管理

#### 3.3.1. 委托客户管理

- **需求**: 管理委托运输服务的客户（即运输公司的直接客户）。
- **数据字段**: 客户名称（唯一）、客户编码、公司类型、联系人、联系电话、地址、合作状态、结算周期、付款方式等。

#### 3.3.2. 收货方管理

- **需求**: 管理委托客户的终端客户（即货物的最终接收方）。
- **数据字段**: 关联的委托客户、收货方名称、联系人、联系电话、详细收货地址（省、市、区、详细地址）。
- **核心功能**:
    - 收货地址需支持省市区三级联动选择。
    - 一个委托客户可以关联多个收货方。

#### 3.3.3. 装货点管理

- **需求**: 管理货物的装载地点，如港口、油库等。
- **数据字段**: 装货点名称（唯一）、装货点类型、详细地址（省、市、区、详细地址）、联系人、联系电话。

#### 3.3.4. 地区数据

- **需求**: 系统需内置全国的省、市、区县数据，用于地址选择。
- **实现**: 沿用现有系统的地区表结构，后端交互使用地区编码，前端进行展示。

### 3.4. 运输单管理

#### 3.4.1. 新增运输单

- **需求**: 支持手动录入新的运输订单。
- **流程**:
    1.  **自动生成单号**: 系统自动生成格式化的运输单号和内部编号。
    2.  **选择关联方**: 从下拉列表中选择委托客户、装货点和收货方。
    3.  **录入货物信息**: 填写油品名称、运输数量（吨）、总体积（升）等。
    4.  **录入计划信息**: 设置计划装货时间和计划送达时间。
    5.  **费用计算**: 根据运价规则自动或手动计算运输费用。
    6.  **提交与保存**: 支持将订单提交为“待指派”状态，或临时存为草稿。

#### 3.4.2. 运输单状态流转

- **需求**: 运输单在全流程中应有明确的状态，并能手动或自动流转。
- **状态定义**:
    - **待指派**: 订单已创建，等待分配车辆和司机。
    - **已指派**: 已分配资源，等待出发。
    - **前往装货**: 司机已出发前往装货点。
    - **装货中**: 已到达装货点，正在装货。
    - **运输中**: 装货完成，在途运输。
    - **已送达**: 货物已运抵目的地。
    - **已对账**: 运输完成且费用已结算。
- **核心功能**: 在关键节点（如司机确认出发、到达、完成装货等），系统应能更新订单状态。

### 3.5. 任务指派

- **需求**: 为“待指派”的运输单分配最合适的车辆和司机。
- **功能**:
    - **自动指派**: 系统可根据预设规则（如车辆状态、载重、位置，司机状态、驾照）推荐最优的车辆和司机。
    - **人工指派**: 调度员可以无视推荐，手动选择任意空闲的车辆和司机。
    - **冲突检测**: 当手动指派的资源不满足订单要求时（如载重不足），系统应给予提示。

### 3.6. 运价管理

- **需求**: 系统需支持灵活的运价计算规则配置。
- **计费方式**:
    - **按距离计费**: `基础费用 + 运输距离 × 每公里单价`
    - **按载重计费**: `基础费用 + 货物重量 × 每吨单价`
    - **固定价格**: 一口价。
- **附加费用**: 支持配置额外费用，如高速费、过夜费、偏远地区附加费等。
- **核心功能**:
    - 支持为不同区域、不同油品类型设置不同的计费规则。
    - 在运输单创建时，能根据匹配的规则自动计算费用。

### 3.7. 仓库与库存管理

#### 3.7.1. 基础信息管理

- **需求**: 系统需管理仓库和商品（油品）的基础信息。
- **数据字段**:
    - **仓库**: 仓库名称、编码、地址、类型（油库、中转库）、状态。
    - **商品**: 商品名称、编码、类型、规格、密度、安全库存量。

#### 3.7.2. 库存管理

- **需求**: 实时跟踪各仓库中各商品的库存情况。
- **数据字段**: 关联仓库和商品、当前库存、可用库存、冻结库存。
- **核心功能**:
    - **库存查询**: 支持按仓库、商品查询实时库存。
    - **库存预警**: 当商品库存低于设定的安全库存时，系统需发出预警。

#### 3.7.3. 出入库管理

- **需求**: 记录所有商品的进出库活动，并与运输流程打通。
- **流程**:
    - **运输出库**: 当运输单状态变为“装货中”时，系统应自动从指定仓库创建出库单，并扣减相应库存。
    - **运输入库**: （如果目的地是仓库）当运输单状态变为“已送达”时，系统应自动为目标仓库创建入库单，并增加相应库存。
    - **库存检查**: 在创建运输单时，需检查始发仓库的可用库存是否满足订单需求。

### 3.8. 对账管理

- **需求**: 基于已完成的运输单，生成与运输公司的对账单。
- **数据结构**: 参照现有系统的 `oil_shipping_invoices` 表，包含订单号、客户、起止点、运输费用、杂费、发票号等信息。
- **核心功能**:
    - **账单生成**: 定期（如每月）自动汇总已完成的运输单，生成对账明细。
    - **状态管理**: 账单状态（待确认、已确认、已付款）。
    - **应收统计**: 提供应收账款的统计和查询功能。

## 4. 非功能需求

### 4.1. 性能需求

- **响应时间**: 核心查询页面（如订单列表、车辆列表）的平均响应时间应在3秒以内。
- **并发处理**: 系统应能支持至少50个用户同时在线操作。

### 4.2. 可用性需求

- **界面友好**: 界面设计应简洁直观，符合用户操作习惯。
- **操作便捷**: 常用操作应有快捷入口，减少用户点击次数。
- **容错性**: 对于用户的误操作（如输入格式错误），系统应有清晰的提示信息。

### 4.3. 可靠性需求

- **数据准确性**: 确保订单、库存、财务等核心数据的计算和存储准确无误。
- **系统稳定性**: 系统应能7x24小时稳定运行，年度可用性不低于99.9%。
- **数据备份**: 需制定定期的数据备份和恢复策略，以防数据丢失。

### 4.4. 安全需求

- **访问控制**: 不同角色的用户（管理员、调度员、财务）应有不同的操作权限。
- **数据安全**: 敏感数据（如客户信息、财务数据）在传输和存储过程中应进行加密处理。
- **操作日志**: 用户的关键操作（如新增订单、修改费用、删除数据）需要被记录，以便审计和追溯。
