# 运输管理系统后端研发设计文档

## 1. 项目概述

### 1.1 项目背景
开发独立的运输管理系统后端服务，专门用于油品运输业务的全流程管理，包括车辆管理、司机管理、运输单管理、任务调度、运价计算和对账管理等核心功能。系统完全独立运行，不与其他系统进行数据交互。

### 1.2 技术架构
- **基础框架**: Spring Boot 2.5.15
- **数据访问**: MyBatis + MyBatis-Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis (独立前缀 `transport:`)
- **安全框架**: Spring Security + JWT
- **国际化**: Spring MessageSource + ResourceBundle
- **API文档**: Swagger/OpenAPI 3.0
- **部署方式**: 独立部署，端口 6877，上下文路径 `/transport`

### 1.3 系统特点
- 完全独立的运输管理系统，无需与其他系统集成
- RESTful API设计，支持前后端分离
- 统一的响应格式和异常处理
- 完善的权限控制和数据安全
- 多语言国际化支持，默认支持中文和葡萄牙语

## 2. 数据库设计

### 2.1 统一字段规范
所有业务表统一包含以下基础字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT AUTO_INCREMENT | 唯一标识ID，主键 |
| create_by | VARCHAR(100) | 创建人 |
| create_time | TIMESTAMP | 创建时间，默认当前时间 |
| update_by | VARCHAR(100) | 修改人 |
| update_time | TIMESTAMP | 修改时间，自动更新 |
| is_deleted | TINYINT(1) | 逻辑删除标志，0-未删除，1-已删除 |

### 2.2 核心业务表

#### 2.2.1 车辆信息表
```sql
CREATE TABLE transport_vehicle (
    id INT AUTO_INCREMENT COMMENT '唯一标识ID' PRIMARY KEY,
    license_plate VARCHAR(50) NOT NULL UNIQUE COMMENT '车牌号',
    vehicle_type VARCHAR(50) COMMENT '车辆类型',
    load_capacity DECIMAL(10,2) COMMENT '载重吨位',
    fuel_tank_capacity DECIMAL(10,2) COMMENT '油箱容量(升)',
    vehicle_status TINYINT DEFAULT 1 COMMENT '车辆状态:1-空闲,2-运输中,3-维修中,4-报废',
    purchase_date DATE COMMENT '购买日期',
    annual_inspection_date DATE COMMENT '年检日期',
    insurance_expiry_date DATE COMMENT '保险到期日期',
    region_code VARCHAR(20) COMMENT '所属区域代码',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(100) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(100) COMMENT '修改人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    is_deleted TINYINT(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除'
) COMMENT '车辆信息表' CHARSET = utf8;
```

#### 2.2.2 司机信息表
```sql
CREATE TABLE transport_driver (
    id INT AUTO_INCREMENT COMMENT '唯一标识ID' PRIMARY KEY,
    driver_name VARCHAR(50) NOT NULL COMMENT '司机姓名',
    driver_phone VARCHAR(20) COMMENT '联系电话',
    id_card VARCHAR(18) COMMENT '身份证号',
    license_type VARCHAR(10) COMMENT '驾照类型',
    license_number VARCHAR(50) COMMENT '驾照号码',
    driving_years INT COMMENT '驾龄(年)',
    driver_status TINYINT DEFAULT 1 COMMENT '司机状态:1-在职,2-休假,3-离职',
    hire_date DATE COMMENT '入职日期',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    create_by VARCHAR(100) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(100) COMMENT '修改人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    is_deleted TINYINT(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除'
) COMMENT '司机信息表' CHARSET = utf8;
```

#### 2.2.3 运输单主表
```sql
CREATE TABLE transport_order (
    id INT AUTO_INCREMENT COMMENT '唯一标识ID' PRIMARY KEY,
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '运输单号',
    internal_code VARCHAR(32) NOT NULL COMMENT '内部编号',
    customer_id INT NOT NULL COMMENT '委托客户ID',
    customer_name VARCHAR(255) NOT NULL COMMENT '委托客户名称',
    consignee_id INT COMMENT '收货方ID',
    consignee_name VARCHAR(255) NOT NULL COMMENT '收货方名称',
    consignee_province_code VARCHAR(10) COMMENT '收货方省份编码',
    consignee_province VARCHAR(50) COMMENT '收货方省份',
    consignee_city_code VARCHAR(10) COMMENT '收货方城市编码',
    consignee_city VARCHAR(50) COMMENT '收货方城市',
    consignee_district_code VARCHAR(10) COMMENT '收货方区县编码',
    consignee_district VARCHAR(50) COMMENT '收货方区县',
    consignee_address VARCHAR(500) NOT NULL COMMENT '收货详细地址',
    consignee_contact VARCHAR(100) COMMENT '收货方联系人',
    consignee_phone VARCHAR(20) COMMENT '收货方电话',
    loading_point_id INT COMMENT '装货点ID',
    loading_point_name VARCHAR(255) NOT NULL COMMENT '装货点名称',
    loading_province_code VARCHAR(10) COMMENT '装货点省份编码',
    loading_province VARCHAR(50) COMMENT '装货点省份',
    loading_city_code VARCHAR(10) COMMENT '装货点城市编码',
    loading_city VARCHAR(50) COMMENT '装货点城市',
    loading_district_code VARCHAR(10) COMMENT '装货点区县编码',
    loading_district VARCHAR(50) COMMENT '装货点区县',
    loading_address VARCHAR(500) NOT NULL COMMENT '装货详细地址',
    product_name VARCHAR(100) COMMENT '油品名称',
    product_quantity DECIMAL(10,2) COMMENT '运输数量(吨)',
    total_volume DECIMAL(10,2) COMMENT '总体积(升)',
    transport_distance DECIMAL(10,2) COMMENT '运输距离(公里)',
    order_status TINYINT DEFAULT 1 COMMENT '订单状态:1-待指派,2-已指派,3-前往装货,4-装货中,5-运输中,6-已送达,7-已对账',
    vehicle_id INT COMMENT '指派车辆ID',
    driver_id INT COMMENT '指派司机ID',
    license_plate VARCHAR(50) COMMENT '车牌号',
    planned_loading_time DATETIME COMMENT '计划装货时间',
    actual_loading_time DATETIME COMMENT '实际装货时间',
    planned_delivery_time DATETIME COMMENT '计划送达时间',
    actual_delivery_time DATETIME COMMENT '实际送达时间',
    loading_completed_time DATETIME COMMENT '装货完成时间',
    departure_time DATETIME COMMENT '出发时间',
    arrival_time DATETIME COMMENT '到达时间',
    shipping_cost DECIMAL(10,2) NOT NULL COMMENT '运输费用',
    other_expenses DECIMAL(10,2) DEFAULT 0.00 COMMENT '杂费',
    tax_included DECIMAL(10,2) COMMENT '含税单价',
    delivery_requirements TEXT COMMENT '配送要求',
    special_instructions TEXT COMMENT '特殊说明',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(100) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(100) COMMENT '修改人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    is_deleted TINYINT(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除'
) COMMENT '运输单主表' CHARSET = utf8;
```

## 3. API接口设计

### 3.1 统一响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": "2024-01-15T10:30:00",
    "locale": "zh_CN"
}
```

### 3.2 国际化响应示例
**中文响应**:
```json
{
    "code": 200,
    "message": "运输单创建成功",
    "data": {...},
    "timestamp": "2024-01-15T10:30:00",
    "locale": "zh_CN"
}
```

**葡萄牙语响应**:
```json
{
    "code": 200,
    "message": "Ordem de transporte criada com sucesso",
    "data": {...},
    "timestamp": "2024-01-15T10:30:00",
    "locale": "pt_BR"
}
```

### 3.3 状态码规范
- 200: 操作成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

### 3.4 国际化请求头
所有API请求支持以下请求头：
- `Accept-Language`: 指定响应语言，支持 `zh-CN`(中文)、`pt-BR`(葡萄牙语)
- `Content-Language`: 请求内容语言

### 3.5 核心接口设计

#### 3.5.1 运输单管理接口
```http
POST /api/transport/orders
Content-Type: application/json
Accept-Language: zh-CN

{
    "customerId": 1,
    "customerName": "XX石油贸易公司",
    "consigneeId": 1,
    "consigneeName": "XX加油站",
    "loadingPointId": 1,
    "loadingPointName": "上海港石油码头",
    "productName": "92号汽油",
    "productQuantity": 10.5,
    "totalVolume": 12000.0,
    "transportDistance": 1200,
    "plannedLoadingTime": "2024-01-15 08:00:00",
    "plannedDeliveryTime": "2024-01-16 18:00:00",
    "shippingCost": 3000.00,
    "otherExpenses": 300.00,
    "deliveryRequirements": "工作时间配送",
    "remark": "备注信息"
}
```

#### 3.5.2 省市区数据接口
```http
GET /api/transport/provinces
GET /api/transport/cities?provinceCode=110000
GET /api/transport/districts?cityCode=110100
```

## 4. 国际化设计

### 4.1 国际化配置
```java
@Configuration
public class InternationalizationConfig {

    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasenames("i18n/messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setCacheSeconds(3600);
        messageSource.setFallbackToSystemLocale(false);
        return messageSource;
    }

    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver localeResolver = new AcceptHeaderLocaleResolver();
        localeResolver.setSupportedLocales(Arrays.asList(
            Locale.SIMPLIFIED_CHINESE,
            new Locale("pt", "BR")
        ));
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return localeResolver;
    }

    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor interceptor = new LocaleChangeInterceptor();
        interceptor.setParamName("lang");
        return interceptor;
    }
}
```

### 4.2 多语言资源文件

#### 4.2.1 中文资源文件 (messages_zh_CN.properties)
```properties
# 通用消息
common.success=操作成功
common.error=操作失败
common.invalid.param=参数验证失败
common.unauthorized=未授权访问
common.forbidden=权限不足
common.not.found=资源不存在
common.server.error=服务器内部错误

# 运输单相关
transport.order.create.success=运输单创建成功
transport.order.create.error=运输单创建失败
transport.order.update.success=运输单更新成功
transport.order.delete.success=运输单删除成功
transport.order.not.found=运输单不存在
transport.order.status.invalid=运输单状态无效

# 车辆相关
vehicle.create.success=车辆信息创建成功
vehicle.update.success=车辆信息更新成功
vehicle.delete.success=车辆信息删除成功
vehicle.not.found=车辆不存在
vehicle.license.duplicate=车牌号已存在

# 司机相关
driver.create.success=司机信息创建成功
driver.update.success=司机信息更新成功
driver.delete.success=司机信息删除成功
driver.not.found=司机不存在
driver.phone.duplicate=手机号已存在

# 客户相关
customer.create.success=客户信息创建成功
customer.update.success=客户信息更新成功
customer.not.found=客户不存在

# 库存相关
inventory.insufficient=库存不足
inventory.inbound.success=入库成功
inventory.outbound.success=出库成功
inventory.record.not.found=出入库记录不存在

# 验证消息
validation.required=此字段为必填项
validation.min.length=长度不能少于{0}个字符
validation.max.length=长度不能超过{0}个字符
validation.invalid.format=格式不正确
validation.invalid.phone=手机号格式不正确
validation.invalid.email=邮箱格式不正确
```

#### 4.2.2 葡萄牙语资源文件 (messages_pt_BR.properties)
```properties
# 通用消息
common.success=Operação realizada com sucesso
common.error=Falha na operação
common.invalid.param=Falha na validação de parâmetros
common.unauthorized=Acesso não autorizado
common.forbidden=Permissões insuficientes
common.not.found=Recurso não encontrado
common.server.error=Erro interno do servidor

# 运输单相关
transport.order.create.success=Ordem de transporte criada com sucesso
transport.order.create.error=Falha ao criar ordem de transporte
transport.order.update.success=Ordem de transporte atualizada com sucesso
transport.order.delete.success=Ordem de transporte excluída com sucesso
transport.order.not.found=Ordem de transporte não encontrada
transport.order.status.invalid=Status da ordem de transporte inválido

# 车辆相关
vehicle.create.success=Informações do veículo criadas com sucesso
vehicle.update.success=Informações do veículo atualizadas com sucesso
vehicle.delete.success=Informações do veículo excluídas com sucesso
vehicle.not.found=Veículo não encontrado
vehicle.license.duplicate=Placa já existe

# 司机相关
driver.create.success=Informações do motorista criadas com sucesso
driver.update.success=Informações do motorista atualizadas com sucesso
driver.delete.success=Informações do motorista excluídas com sucesso
driver.not.found=Motorista não encontrado
driver.phone.duplicate=Número de telefone já existe

# 客户相关
customer.create.success=Informações do cliente criadas com sucesso
customer.update.success=Informações do cliente atualizadas com sucesso
customer.not.found=Cliente não encontrado

# 库存相关
inventory.insufficient=Estoque insuficiente
inventory.inbound.success=Entrada realizada com sucesso
inventory.outbound.success=Saída realizada com sucesso
inventory.record.not.found=Registro de entrada/saída não encontrado

# 验证消息
validation.required=Este campo é obrigatório
validation.min.length=O comprimento não pode ser menor que {0} caracteres
validation.max.length=O comprimento não pode exceder {0} caracteres
validation.invalid.format=Formato incorreto
validation.invalid.phone=Formato de telefone incorreto
validation.invalid.email=Formato de e-mail incorreto
```

### 4.3 国际化工具类
```java
@Component
public class MessageUtils {

    @Autowired
    private MessageSource messageSource;

    /**
     * 获取国际化消息
     */
    public String getMessage(String code) {
        return getMessage(code, null);
    }

    /**
     * 获取国际化消息（带参数）
     */
    public String getMessage(String code, Object[] args) {
        return getMessage(code, args, LocaleContextHolder.getLocale());
    }

    /**
     * 获取指定语言的国际化消息
     */
    public String getMessage(String code, Object[] args, Locale locale) {
        try {
            return messageSource.getMessage(code, args, locale);
        } catch (NoSuchMessageException e) {
            return code;
        }
    }

    /**
     * 获取当前语言环境
     */
    public Locale getCurrentLocale() {
        return LocaleContextHolder.getLocale();
    }

    /**
     * 获取当前语言代码
     */
    public String getCurrentLanguage() {
        Locale locale = getCurrentLocale();
        return locale.getLanguage() + "_" + locale.getCountry();
    }
}
```

### 4.4 统一响应包装
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AjaxResult {
    private Integer code;
    private String message;
    private Object data;
    private String timestamp;
    private String locale;

    public static AjaxResult success() {
        return success("common.success");
    }

    public static AjaxResult success(String messageCode) {
        return success(messageCode, null);
    }

    public static AjaxResult success(String messageCode, Object data) {
        MessageUtils messageUtils = SpringUtils.getBean(MessageUtils.class);
        return new AjaxResult(
            200,
            messageUtils.getMessage(messageCode),
            data,
            DateUtils.getTime(),
            messageUtils.getCurrentLanguage()
        );
    }

    public static AjaxResult error(String messageCode) {
        return error(messageCode, null);
    }

    public static AjaxResult error(String messageCode, Object[] args) {
        MessageUtils messageUtils = SpringUtils.getBean(MessageUtils.class);
        return new AjaxResult(
            500,
            messageUtils.getMessage(messageCode, args),
            null,
            DateUtils.getTime(),
            messageUtils.getCurrentLanguage()
        );
    }
}
```

## 5. 服务层设计

### 5.1 运输单服务类
```java
@Service
@Transactional
public class TransportOrderService {
    
    @Autowired
    private TransportOrderMapper orderMapper;
    
    @Autowired
    private PricingRuleService pricingRuleService;
    
    @Autowired
    private RedisCache redisCache;
    
    /**
     * 新增运输单
     */
    public AjaxResult createOrder(TransportOrderDTO orderDTO) {
        // 1. 数据验证
        ValidationResult validation = validateOrderData(orderDTO);
        if (!validation.isValid()) {
            return AjaxResult.error("common.invalid.param");
        }
        
        // 2. 生成单号
        String orderNo = generateOrderNo();
        String internalCode = generateInternalCode();
        
        // 3. 转换DTO为实体
        TransportOrder order = convertToEntity(orderDTO);
        order.setOrderNo(orderNo);
        order.setInternalCode(internalCode);
        order.setOrderStatus(OrderStatus.PENDING_ASSIGN.getCode());
        
        // 4. 保存到数据库
        int result = orderMapper.insertOrder(order);
        if (result > 0) {
            // 5. 更新缓存
            updateOrderCache(order);
            return AjaxResult.success("transport.order.create.success", order);
        }

        return AjaxResult.error("transport.order.create.error");
    }
    
    /**
     * 生成运输单号
     */
    private String generateOrderNo() {
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
        String lockKey = "transport:order_no_lock:" + dateStr;
        
        // 使用Redis分布式锁确保单号唯一性
        return redisTemplate.execute(new RedisCallback<String>() {
            @Override
            public String doInRedis(RedisConnection connection) {
                // 获取当日最大序号并生成新单号
                String maxOrderNo = orderMapper.getMaxOrderNoByDate(dateStr);
                int sequence = 1;
                if (StringUtils.isNotEmpty(maxOrderNo)) {
                    String seqStr = maxOrderNo.substring(maxOrderNo.length() - 4);
                    sequence = Integer.parseInt(seqStr) + 1;
                }
                return "YS" + dateStr + String.format("%04d", sequence);
            }
        });
    }
}
```

## 5. 数据访问层设计

### 5.1 Mapper接口
```java
@Mapper
public interface TransportOrderMapper {
    
    /**
     * 新增运输单
     */
    int insertOrder(TransportOrder order);
    
    /**
     * 根据日期获取最大单号
     */
    String getMaxOrderNoByDate(@Param("dateStr") String dateStr);
    
    /**
     * 根据ID查询运输单
     */
    TransportOrder selectById(@Param("id") Long id);
    
    /**
     * 更新运输单状态
     */
    int updateOrderStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 分页查询运输单
     */
    List<TransportOrder> selectOrderList(TransportOrderQuery query);
}
```

### 5.2 SQL映射文件
```xml
<mapper namespace="com.ruoyi.transport.mapper.TransportOrderMapper">
    
    <insert id="insertOrder" parameterType="TransportOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO transport_order (
            order_no, internal_code, customer_id, customer_name,
            consignee_id, consignee_name, consignee_address,
            loading_point_id, loading_point_name, loading_address,
            product_name, product_quantity, total_volume,
            order_status, planned_loading_time, planned_delivery_time,
            shipping_cost, other_expenses, delivery_requirements,
            remark, create_by, create_time
        ) VALUES (
            #{orderNo}, #{internalCode}, #{customerId}, #{customerName},
            #{consigneeId}, #{consigneeName}, #{consigneeAddress},
            #{loadingPointId}, #{loadingPointName}, #{loadingAddress},
            #{productName}, #{productQuantity}, #{totalVolume},
            #{orderStatus}, #{plannedLoadingTime}, #{plannedDeliveryTime},
            #{shippingCost}, #{otherExpenses}, #{deliveryRequirements},
            #{remark}, #{createBy}, NOW()
        )
    </insert>
    
    <select id="getMaxOrderNoByDate" resultType="String">
        SELECT MAX(order_no) 
        FROM transport_order 
        WHERE order_no LIKE CONCAT('YS', #{dateStr}, '%')
        AND is_deleted = 0
    </select>
    
</mapper>
```

## 6. 缓存设计

### 6.1 Redis Key设计
- 运输单缓存: `transport:order:{orderId}`
- 车辆状态缓存: `transport:vehicle:status:{vehicleId}`
- 司机状态缓存: `transport:driver:status:{driverId}`
- 分布式锁: `transport:lock:{lockName}`

### 6.2 缓存策略
- 热点数据缓存30分钟
- 状态数据实时更新
- 使用Redis事务保证数据一致性

## 7. 安全设计

### 7.1 认证授权
- JWT Token认证
- 基于角色的权限控制(RBAC)
- 接口级权限验证

### 7.2 数据安全
- SQL注入防护
- XSS攻击防护
- 敏感数据加密存储

## 8. 异常处理

### 8.1 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public AjaxResult handleBusinessException(BusinessException e) {
        return AjaxResult.error(e.getMessage());
    }
    
    @ExceptionHandler(ValidationException.class)
    public AjaxResult handleValidationException(ValidationException e) {
        return AjaxResult.error("参数验证失败: " + e.getMessage());
    }
}
```

## 9. 部署配置

### 9.1 应用配置
```yaml
server:
  port: 6877
  servlet:
    context-path: /transport

spring:
  datasource:
    url: *************************************
    username: root
    password: password
  
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 3000
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
```

### 9.2 日志配置
- 业务日志记录关键操作
- 错误日志记录异常信息
- 性能日志记录接口响应时间
