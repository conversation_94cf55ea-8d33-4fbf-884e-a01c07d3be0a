package com.ruoyi.common.utils.uuid;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;


public class UniqueIdentifierGenerator {
    // 时间戳的起始值，一般取系统的一个时间点作为起始点
    private static final long START_TIMESTAMP = 1483200000000L;

    // 用于生成序列号的原子类
    private static final AtomicLong SEQUENCE = new AtomicLong(0);

    // 机器ID和数据中心ID，这里假设为1，实际应用中应该是不同的值
    private static final long MACHINE_ID = 1L;
    private static final long DATA_CENTER_ID = 1L;
    // 机器标识位数
    private static final long MACHINE_ID_BITS = 5L;

    // 毫秒内序列号的位数
    private static final long SEQUENCE_BITS = 12L;
    // 每一部分的最大值位数
    private static final long TIMESTAMP_LEFT_SHIFT = 17; // 调整为17位，因为我们需要8位的年月日

    // 机器ID部分
    private static final long MACHINE_ID_SHIFT = SEQUENCE_BITS;

    // 数据中心ID部分
    private static final long DATA_CENTER_ID_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS;

    // 毫秒内序列号的最大值
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);

    private static long lastTimestamp = -1L;

    /**
     * 生成包含年月日的不重复编号
     *
     * @return 包含年月日的不重复编号字符串
     */
    public static synchronized String generateUniqueIdentifier() {
        long timestamp = System.currentTimeMillis();
        if (timestamp < lastTimestamp) {
            throw new RuntimeException("Clock moved backwards. Refusing to generate id for the time being.");
        }

        long sequence;
        if (lastTimestamp == timestamp) {
            sequence = SEQUENCE.incrementAndGet();
            if (sequence >= MAX_SEQUENCE) {
                waitUntilNextTime();
                timestamp = System.currentTimeMillis();
            }
        } else {
            sequence = 0L;
        }
        lastTimestamp = timestamp;

        // 格式化时间戳为年月日
        String datePart = new SimpleDateFormat("yyyyMMdd").format(new Date(timestamp));

        // 时间戳部分
        long time = timestamp - START_TIMESTAMP;
        // 数据中心ID部分
        long dataCenterId = DATA_CENTER_ID << DATA_CENTER_ID_SHIFT;
        // 机器ID部分
        long machineId = MACHINE_ID << MACHINE_ID_SHIFT;
        // 序列号部分
        long seq = sequence;

        // 拼接各部分
        long unique = (time << TIMESTAMP_LEFT_SHIFT) | (dataCenterId | machineId | seq);

        // 将ID转换为字符串，确保前8位为年月日
        return datePart + String.format("%015d", unique);
    }

    /**
     * 生成带有前缀的30位不重复编号
     *
     * @return 带有前缀的30位不重复编号字符串
     */
    public static synchronized String generateUniqueIdentifierWithPrefix(String prefix) {
        // 生成编号，假设编号长度为20位（不包括前缀）
        String uniqueIdentifier = generateUniqueIdentifier();

        // 确保编号与前缀拼接后总长度不超过30位
        if (prefix.length() + uniqueIdentifier.length() <= 30) {
            // 返回带有前缀的编号
            return prefix + uniqueIdentifier;
        } else {
            throw new RuntimeException("Prefix is too long for the unique identifier.");
        }
    }

    private static void waitUntilNextTime() {
        while (lastTimestamp == System.currentTimeMillis()) {
            // 等待直到下一个时间单位
        }
    }

    public static void main(String[] args) {
        String uniqueIdentifier = generateUniqueIdentifierWithPrefix("CS");
        System.out.println("Generated Unique Identifier with Date Prefix: " + uniqueIdentifier);
    }
}