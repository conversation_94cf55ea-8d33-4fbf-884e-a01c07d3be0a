package com.ruoyi.common.utils;

import java.math.BigDecimal;

public class AmountUtils {

    /**
     * 比较两个BigDecimal是否相等，忽略小数位数
     *
     * @param a BigDecimal
     * @param b BigDecimal
     * @return boolean
     */
    public static boolean compareBigDecimal(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        // 使用 compareTo 方法比较数值，忽略小数位数
        return a.compareTo(b) == 0;
    }
}
