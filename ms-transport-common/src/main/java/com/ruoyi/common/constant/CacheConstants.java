package com.ruoyi.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 运输管理系统缓存前缀
     */
    public static final String TRANSPORT_PREFIX = "transport:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = TRANSPORT_PREFIX + "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = TRANSPORT_PREFIX + "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = TRANSPORT_PREFIX + "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = TRANSPORT_PREFIX + "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = TRANSPORT_PREFIX + "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = TRANSPORT_PREFIX + "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = TRANSPORT_PREFIX + "pwd_err_cnt:";

    /**
     * 地址树 cache key
     */
    public static final String ADDRESS_TREE_KEY = "address_tree";
}
