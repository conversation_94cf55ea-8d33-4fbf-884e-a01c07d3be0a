package com.ruoyi.common.enums;

public enum OrderStatusEnum {
    ORDER_PENDING(100, "订单待确认"),
    AWAITING_SHIPMENT(110, "待发货"),
    SHIPPED(120, "已发货"),
    DELIVERED(130, "已收货"),
    REJECTED(140, "审核拒绝"),
    COMPLETED(150, "订单完成"),
    CANCEL(160, "已取消");

    private final Integer code;
    private final String desc;

    OrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
