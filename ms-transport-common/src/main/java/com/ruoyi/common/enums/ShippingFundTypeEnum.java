package com.ruoyi.common.enums;

public enum ShippingFundTypeEnum {
    SHIPPING_COST(0, "运费"),
    OTHER_EXPENSES(1, "杂费"),
    PAYMENT(2, "付款"),
    RED_ACCOUNTS_PAYABLE(3, "红冲应付");

    private final Integer code;
    private final String desc;

    ShippingFundTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        ShippingFundTypeEnum[] enums = ShippingFundTypeEnum.values();
        for (ShippingFundTypeEnum anEnum : enums) {
            if (code.equals(anEnum.code)) {
                return anEnum.desc;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
