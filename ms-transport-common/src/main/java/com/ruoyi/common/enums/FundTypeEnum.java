package com.ruoyi.common.enums;

public enum FundTypeEnum {


    PRE_RECEIVABLE(0, "预收款"),
    PAYABLE(1, "应付款"),
    RED_INVOICE_RECEIVABLE(2, "红冲收款"),
    RED_INVOICE_PAYABLE(3, "红冲订单");

    private final Integer code;
    private final String desc;

    FundTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        FundTypeEnum[] enums = FundTypeEnum.values();
        for (FundTypeEnum anEnum : enums) {
            if (code.equals(anEnum.code)) {
                return anEnum.desc;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
