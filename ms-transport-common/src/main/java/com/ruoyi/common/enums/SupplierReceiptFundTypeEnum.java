package com.ruoyi.common.enums;

public enum SupplierReceiptFundTypeEnum {

    PAYABLE(0, "应付款"),
    PAID(1, "已付款"),
    RED_INVOICE_PAYABLE(2, "红冲应付");

    private final Integer code;
    private final String desc;

    SupplierReceiptFundTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        SupplierReceiptFundTypeEnum[] enums = SupplierReceiptFundTypeEnum.values();
        for (SupplierReceiptFundTypeEnum anEnum : enums) {
            if (code.equals(anEnum.code)) {
                return anEnum.desc;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
