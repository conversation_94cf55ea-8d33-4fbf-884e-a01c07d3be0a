package com.ruoyi.common.enums;

public enum ShippingInvoiceStatusEnum {
    TO_BE_UPLOADED_INVOICE(10, "待传发票"),
    TO_BE_CONFIRMED_ORDER(20, "待确认"),
    CONFIRMED(30, "已确认");


    private final Integer code;
    private final String desc;

    ShippingInvoiceStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (ShippingInvoiceStatusEnum value : ShippingInvoiceStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
