package com.ruoyi.common.enums;

public enum OrderPaymentStatusEnum {
    PENDING(10, "待支付"),
    PARTIALLY_PAID(20, "部分支付"),
    PAID(30, "支付完成");


    private final Integer code;
    private final String desc;

    OrderPaymentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
