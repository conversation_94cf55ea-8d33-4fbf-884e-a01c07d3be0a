package com.ruoyi.common.enums;

public enum DeliveryStatusEnum {
    PENDING_DELIVERY(100, "待出库"),
    COMPLETED_DELIVERY(110, "已出库"),
    CANCEL_DELIVERY(120, "已取消");

    private final Integer key;
    private final String value;

    DeliveryStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}