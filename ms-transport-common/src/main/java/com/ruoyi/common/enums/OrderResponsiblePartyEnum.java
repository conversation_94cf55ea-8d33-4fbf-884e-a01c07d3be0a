package com.ruoyi.common.enums;

public enum OrderResponsiblePartyEnum {
    ONE_SELF(1, "我司"),
    THIRD_PARTY(2, "第三方");


    private final Integer code;
    private final String desc;

    OrderResponsiblePartyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
