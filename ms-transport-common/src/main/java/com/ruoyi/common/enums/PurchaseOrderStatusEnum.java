package com.ruoyi.common.enums;

public enum PurchaseOrderStatusEnum {
    PENDING_STORAGE(100, "待入库"),
    PARTIAL_STORAGE(110, "部分入库"),
    COMPLETED_STORAGE(120, "已入库"),
    CANCEL(130, "已取消"),
    ;

    private final Integer key;
    private final String value;

    PurchaseOrderStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}