# 运输单自动生成对账单使用指南

## 功能简介

当运输单配送完成时，系统会自动生成对应的对账单信息，实现运输业务与财务对账的无缝衔接。

## 使用流程

### 1. 前置条件
- 运输单已创建并处于"运输中"状态
- 客户信息已完善（建议设置客户编号）
- 运输费用已设置

### 2. 完成配送操作

#### 通过API接口
```http
PUT /order/completeDelivery/{orderId}
Authorization: Bearer {token}
```

**请求示例：**
```bash
curl -X PUT "http://localhost:8080/order/completeDelivery/123" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json"
```

**成功响应：**
```json
{
  "code": 200,
  "msg": "配送完成，已自动生成对账单信息",
  "data": null
}
```

#### 通过前端界面
1. 进入运输单管理页面
2. 找到状态为"运输中"的运输单
3. 点击"完成配送"按钮
4. 系统提示"配送完成，已自动生成对账单信息"

### 3. 查看生成的对账单

#### 方式一：通过对账单管理页面
1. 进入"运输对账管理"页面
2. 根据收据号码查询（格式：TR + 日期 + 序号）
3. 或根据客户编号查询

#### 方式二：通过API查询
```http
GET /receipts/list?receiptNumber=TR202401150123
```

## 自动生成规则

### 对账单信息映射

| 字段 | 来源 | 示例值 |
|------|------|--------|
| 收据号码 | 自动生成 | TR202401150123 |
| 款项金额 | 运输单运费 | 5000.00 |
| 款项类型 | 固定：应付款(1) | 1 |
| 描述 | 自动生成 | 运输单[YS20240115001]配送完成自动生成 |
| 客户编号 | 客户档案 | CUST001 |
| 内部编号 | 运输单内部编号 | NB20240115001 |
| 客户名称 | 运输单客户名称 | 测试客户有限公司 |
| 收据日期 | 实际送达时间 | 2024-01-15 14:30:00 |

### 收据号码生成规则
- **格式**：`TR + yyyyMMdd + 4位序号`
- **示例**：`TR202401150123`
- **说明**：
  - `TR`：运输单对账单前缀
  - `20240115`：当前日期
  - `0123`：运输单ID的后4位

### 客户编号处理
- **优先使用**：客户档案中的客户编号
- **备用方案**：如果客户编号为空，自动生成 `CUST + 6位客户ID`
- **示例**：客户ID为123，生成编号为 `CUST000123`

## 异常情况处理

### 1. 对账单生成失败
- **现象**：运输单状态正常更新为"已送达"，但对账单未生成
- **原因**：可能是数据库连接问题、权限问题等
- **处理**：
  1. 查看系统日志确认具体错误
  2. 手动创建对账单记录
  3. 联系系统管理员

### 2. 收据号码重复
- **现象**：系统日志显示"收据号码已存在，跳过生成对账单"
- **原因**：可能是重复执行或系统时钟问题
- **处理**：
  1. 检查是否已有对应的对账单
  2. 如果没有，手动创建对账单

### 3. 客户信息不完整
- **现象**：系统日志显示"客户编号为空，使用默认编号"
- **原因**：客户档案中未设置客户编号
- **处理**：
  1. 完善客户档案信息
  2. 对账单仍会正常生成，使用默认编号

## 最佳实践

### 1. 数据准备
- **客户档案**：确保所有客户都设置了唯一的客户编号
- **运输费用**：在创建运输单时准确设置运输费用
- **时间设置**：确保服务器时间准确

### 2. 操作建议
- **及时完成配送**：运输完成后及时更新状态
- **检查对账单**：完成配送后检查对账单是否正确生成
- **定期核对**：定期核对运输单与对账单的一致性

### 3. 监控要点
- **生成成功率**：监控对账单自动生成的成功率
- **数据一致性**：定期检查运输单与对账单数据的一致性
- **异常处理**：及时处理生成失败的情况

## 权限要求

### API调用权限
- **完成配送**：需要 `transport:order:edit` 权限
- **查看对账单**：需要 `transport:receipts:list` 权限

### 用户角色建议
- **运输调度员**：可完成配送操作
- **财务人员**：可查看和管理对账单
- **系统管理员**：可处理异常情况

## 常见问题

### Q1：为什么对账单没有自动生成？
**A1：** 可能的原因：
1. 运输单状态不是从"运输中"更新为"已送达"
2. 系统异常或数据库连接问题
3. 权限不足或服务未启动

**解决方案：**
1. 检查运输单状态流转是否正确
2. 查看系统日志确认具体错误
3. 手动创建对账单记录

### Q2：收据号码重复怎么办？
**A2：** 系统会自动跳过重复的收据号码，不会重复生成。如果确实需要生成，可以：
1. 检查现有对账单是否正确
2. 手动创建新的对账单记录
3. 联系系统管理员调整生成规则

### Q3：客户编号显示为CUST开头的默认编号？
**A3：** 这是因为客户档案中未设置客户编号，系统自动生成的默认编号。建议：
1. 完善客户档案，设置正确的客户编号
2. 更新已生成的对账单记录
3. 建立客户编号管理规范

### Q4：对账单金额与运输单不一致？
**A4：** 对账单金额直接来源于运输单的运输费用字段，如果不一致：
1. 检查运输单的运输费用是否正确
2. 确认是否有其他费用需要包含
3. 手动调整对账单金额或重新生成

## 技术支持

如果遇到技术问题，请提供以下信息：
1. 运输单ID和运输单号
2. 操作时间和用户信息
3. 错误信息和系统日志
4. 预期结果和实际结果

联系方式：
- 技术支持邮箱：<EMAIL>
- 系统管理员：<EMAIL>
- 紧急联系电话：400-xxx-xxxx

## 版本更新记录

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持运输单配送完成自动生成对账单
- 支持收据号码自动生成
- 支持异常情况处理

### 后续计划
- 支持批量生成对账单
- 支持自定义收据号码格式
- 支持对账单模板配置
- 集成财务系统接口
