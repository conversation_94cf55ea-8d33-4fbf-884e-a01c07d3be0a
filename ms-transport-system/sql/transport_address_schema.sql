-- 运输管理系统地址表建表脚本

-- 运输管理国家表
DROP TABLE IF EXISTS `transport_countries`;
CREATE TABLE `transport_countries` (
  `code` varchar(10) NOT NULL COMMENT '国家编码',
  `name` varchar(100) NOT NULL COMMENT '国家名称',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运输管理国家表';

-- 运输管理省份表
DROP TABLE IF EXISTS `transport_provinces`;
CREATE TABLE `transport_provinces` (
  `code` varchar(10) NOT NULL COMMENT '省份编码',
  `name` varchar(100) NOT NULL COMMENT '省份名称',
  `country_code` varchar(10) NOT NULL COMMENT '国家编码',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_country_code` (`country_code`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_provinces_country` FOREIGN KEY (`country_code`) REFERENCES `transport_countries` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运输管理省份表';

-- 运输管理城市表
DROP TABLE IF EXISTS `transport_cities`;
CREATE TABLE `transport_cities` (
  `code` varchar(10) NOT NULL COMMENT '城市编码',
  `name` varchar(100) NOT NULL COMMENT '城市名称',
  `province_code` varchar(10) NOT NULL COMMENT '省份编码',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_province_code` (`province_code`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_cities_province` FOREIGN KEY (`province_code`) REFERENCES `transport_provinces` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运输管理城市表';

-- 运输管理区县表
DROP TABLE IF EXISTS `transport_districts`;
CREATE TABLE `transport_districts` (
  `code` varchar(10) NOT NULL COMMENT '区县编码',
  `name` varchar(100) NOT NULL COMMENT '区县名称',
  `city_code` varchar(10) NOT NULL COMMENT '城市编码',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`code`),
  KEY `idx_city_code` (`city_code`),
  KEY `idx_name` (`name`),
  CONSTRAINT `fk_districts_city` FOREIGN KEY (`city_code`) REFERENCES `transport_cities` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运输管理区县表';

-- 插入初始数据（中国地址数据）
INSERT INTO `transport_countries` (`code`, `name`, `create_by`) VALUES
('CN', '中国', 'admin');

INSERT INTO `transport_provinces` (`code`, `name`, `country_code`, `create_by`) VALUES
('110000', '北京市', 'CN', 'admin'),
('120000', '天津市', 'CN', 'admin'),
('130000', '河北省', 'CN', 'admin'),
('140000', '山西省', 'CN', 'admin'),
('150000', '内蒙古自治区', 'CN', 'admin'),
('210000', '辽宁省', 'CN', 'admin'),
('220000', '吉林省', 'CN', 'admin'),
('230000', '黑龙江省', 'CN', 'admin'),
('310000', '上海市', 'CN', 'admin'),
('320000', '江苏省', 'CN', 'admin'),
('330000', '浙江省', 'CN', 'admin'),
('340000', '安徽省', 'CN', 'admin'),
('350000', '福建省', 'CN', 'admin'),
('360000', '江西省', 'CN', 'admin'),
('370000', '山东省', 'CN', 'admin'),
('410000', '河南省', 'CN', 'admin'),
('420000', '湖北省', 'CN', 'admin'),
('430000', '湖南省', 'CN', 'admin'),
('440000', '广东省', 'CN', 'admin'),
('450000', '广西壮族自治区', 'CN', 'admin'),
('460000', '海南省', 'CN', 'admin'),
('500000', '重庆市', 'CN', 'admin'),
('510000', '四川省', 'CN', 'admin'),
('520000', '贵州省', 'CN', 'admin'),
('530000', '云南省', 'CN', 'admin'),
('540000', '西藏自治区', 'CN', 'admin'),
('610000', '陕西省', 'CN', 'admin'),
('620000', '甘肃省', 'CN', 'admin'),
('630000', '青海省', 'CN', 'admin'),
('640000', '宁夏回族自治区', 'CN', 'admin'),
('650000', '新疆维吾尔自治区', 'CN', 'admin');

-- 插入部分城市数据（以北京、上海、广东为例）
INSERT INTO `transport_cities` (`code`, `name`, `province_code`, `create_by`) VALUES
-- 北京市
('110100', '北京市', '110000', 'admin'),
-- 上海市
('310100', '上海市', '310000', 'admin'),
-- 广东省主要城市
('440100', '广州市', '440000', 'admin'),
('440300', '深圳市', '440000', 'admin'),
('440400', '珠海市', '440000', 'admin'),
('440500', '汕头市', '440000', 'admin'),
('440600', '佛山市', '440000', 'admin'),
('440700', '江门市', '440000', 'admin'),
('440800', '湛江市', '440000', 'admin'),
('440900', '茂名市', '440000', 'admin'),
('441200', '肇庆市', '440000', 'admin'),
('441300', '惠州市', '440000', 'admin'),
('441400', '梅州市', '440000', 'admin'),
('441500', '汕尾市', '440000', 'admin'),
('441600', '河源市', '440000', 'admin'),
('441700', '阳江市', '440000', 'admin'),
('441800', '清远市', '440000', 'admin'),
('441900', '东莞市', '440000', 'admin'),
('442000', '中山市', '440000', 'admin'),
('445100', '潮州市', '440000', 'admin'),
('445200', '揭阳市', '440000', 'admin'),
('445300', '云浮市', '440000', 'admin');

-- 插入部分区县数据（以北京、广州为例）
INSERT INTO `transport_districts` (`code`, `name`, `city_code`, `create_by`) VALUES
-- 北京市区县
('110101', '东城区', '110100', 'admin'),
('110102', '西城区', '110100', 'admin'),
('110105', '朝阳区', '110100', 'admin'),
('110106', '丰台区', '110100', 'admin'),
('110107', '石景山区', '110100', 'admin'),
('110108', '海淀区', '110100', 'admin'),
('110109', '门头沟区', '110100', 'admin'),
('110111', '房山区', '110100', 'admin'),
('110112', '通州区', '110100', 'admin'),
('110113', '顺义区', '110100', 'admin'),
('110114', '昌平区', '110100', 'admin'),
('110115', '大兴区', '110100', 'admin'),
('110116', '怀柔区', '110100', 'admin'),
('110117', '平谷区', '110100', 'admin'),
('110118', '密云区', '110100', 'admin'),
('110119', '延庆区', '110100', 'admin'),

-- 广州市区县
('440103', '荔湾区', '440100', 'admin'),
('440104', '越秀区', '440100', 'admin'),
('440105', '海珠区', '440100', 'admin'),
('440106', '天河区', '440100', 'admin'),
('440111', '白云区', '440100', 'admin'),
('440112', '黄埔区', '440100', 'admin'),
('440113', '番禺区', '440100', 'admin'),
('440114', '花都区', '440100', 'admin'),
('440115', '南沙区', '440100', 'admin'),
('440117', '从化区', '440100', 'admin'),
('440118', '增城区', '440100', 'admin');
