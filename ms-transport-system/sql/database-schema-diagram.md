# 运输管理系统数据库表关系图

## 数据库ER图概述

运输管理系统包含21张核心业务表，按功能模块划分为10个子系统。

## 表关系图

```mermaid
erDiagram
    %% 地址管理模块
    transport_countries ||--o{ transport_provinces : "country_code"
    transport_provinces ||--o{ transport_cities : "province_code"
    transport_cities ||--o{ transport_districts : "city_code"
    
    %% 客户管理模块
    transport_customer ||--o{ transport_consignee : "customer_id"
    transport_customer ||--o{ transport_order : "customer_id"
    transport_customer ||--o{ transport_pricing_rule : "customer_id"
    
    %% 车辆和司机管理
    transport_vehicle ||--o{ transport_order : "vehicle_id"
    transport_driver ||--o{ transport_order : "driver_id"
    transport_driver ||--o{ transport_driver_schedule : "driver_id"
    
    %% 运输单管理
    transport_order ||--o{ transport_order_status_log : "order_id"
    
    %% 仓库管理模块
    transport_warehouse ||--o{ transport_inventory : "warehouse_id"
    transport_product ||--o{ transport_inventory : "product_id"
    transport_warehouse ||--o{ transport_inventory_record : "warehouse_id"
    transport_inventory_record ||--o{ transport_inventory_record_detail : "record_id"
    transport_product ||--o{ transport_inventory_record_detail : "product_id"
    
    %% 表结构定义
    transport_countries {
        string code PK
        string name
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
    }
    
    transport_provinces {
        string code PK
        string name
        string country_code FK
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
    }
    
    transport_cities {
        string code PK
        string name
        string province_code FK
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
    }
    
    transport_districts {
        string code PK
        string name
        string city_code FK
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
    }
    
    transport_customer {
        bigint id PK
        string customer_name
        string customer_code
        string company_type
        string contact_person
        string contact_phone
        string contact_email
        string company_address
        tinyint customer_status
        string credit_rating
        tinyint payment_method
        int payment_days
        tinyint is_vip
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_vehicle {
        bigint id PK
        string license_plate
        string vehicle_type
        string vehicle_model
        decimal load_capacity
        decimal fuel_tank_capacity
        tinyint vehicle_status
        date purchase_date
        date registration_date
        date insurance_expiry
        date annual_inspection_date
        string region_code
        string owner_name
        string owner_phone
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_driver {
        bigint id PK
        string driver_name
        string driver_phone
        string id_card
        string license_type
        string license_number
        date license_expiry
        int driving_years
        tinyint driver_status
        date hire_date
        string emergency_contact
        string emergency_phone
        string address
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_order {
        bigint id PK
        string order_no
        string internal_code
        bigint customer_id FK
        string customer_name
        bigint consignee_id FK
        string consignee_name
        string consignee_address
        string consignee_contact
        string consignee_phone
        bigint loading_point_id FK
        string loading_point_name
        string loading_address
        string product_name
        decimal product_quantity
        decimal total_volume
        decimal transport_distance
        tinyint order_status
        bigint vehicle_id FK
        string vehicle_license
        bigint driver_id FK
        string driver_name
        string driver_phone
        datetime planned_loading_time
        datetime planned_delivery_time
        datetime actual_loading_time
        datetime actual_departure_time
        datetime actual_delivery_time
        datetime assign_time
        string assign_by
        decimal shipping_cost
        decimal other_expenses
        decimal total_cost
        datetime billing_time
        string billing_by
        text delivery_requirements
        text special_instructions
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_consignee {
        bigint id PK
        string consignee_name
        string consignee_code
        bigint customer_id FK
        string contact_person
        string contact_phone
        string province_code
        string province_name
        string city_code
        string city_name
        string district_code
        string district_name
        string detail_address
        tinyint consignee_type
        string business_hours
        text special_requirements
        tinyint is_active
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_loading_point {
        bigint id PK
        string point_name
        string point_code
        tinyint point_type
        string province_code
        string province_name
        string city_code
        string city_name
        string district_code
        string district_name
        string detail_address
        string contact_person
        string contact_phone
        string operating_hours
        string loading_capacity
        text special_requirements
        tinyint is_active
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_pricing_rule {
        bigint id PK
        string rule_name
        bigint customer_id FK
        bigint route_id FK
        string product_type
        tinyint pricing_type
        decimal base_price
        decimal unit_price
        decimal min_price
        decimal max_price
        date effective_date
        date expiry_date
        int priority
        tinyint is_active
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_warehouse {
        bigint id PK
        string warehouse_name
        string warehouse_code
        text warehouse_address
        tinyint warehouse_type
        tinyint warehouse_status
        decimal storage_capacity
        string manager_name
        string manager_phone
        string province_code
        string province_name
        string city_code
        string city_name
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_product {
        bigint id PK
        string product_name
        string product_code
        string product_type
        string product_spec
        string unit
        decimal density
        decimal safety_stock
        tinyint product_status
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_inventory {
        bigint id PK
        bigint warehouse_id FK
        bigint product_id FK
        decimal current_stock
        decimal available_stock
        decimal frozen_stock
        timestamp last_in_time
        timestamp last_out_time
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_inventory_record {
        bigint id PK
        string record_no
        bigint warehouse_id FK
        tinyint record_type
        tinyint business_type
        string related_order_no
        tinyint record_status
        decimal total_quantity
        string operator_name
        string audit_by
        timestamp audit_time
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_inventory_record_detail {
        bigint id PK
        bigint record_id FK
        bigint product_id FK
        decimal quantity
        decimal unit_price
        decimal total_amount
        string batch_no
        date production_date
        date expiry_date
        text remark
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_driver_schedule {
        bigint id PK
        bigint driver_id FK
        date work_date
        tinyint shift_type
        time work_start_time
        time work_end_time
        decimal actual_work_hours
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_order_status_log {
        bigint id PK
        bigint order_id FK
        string order_no
        tinyint old_status
        tinyint new_status
        datetime status_change_time
        string change_reason
        string operator
        text remark
        timestamp create_time
    }
    
    transport_additional_fee {
        bigint id PK
        string fee_name
        string fee_type
        tinyint calculation_method
        decimal fee_amount
        text condition_desc
        tinyint is_active
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
        tinyint is_deleted
    }
    
    transport_system_config {
        bigint id PK
        string config_key
        text config_value
        string config_desc
        string config_type
        tinyint is_system
        timestamp create_time
        timestamp update_time
        string create_by
        string update_by
    }
```

## 核心业务流程

### 1. 运输单业务流程
```
客户下单 → 创建运输单 → 指派车辆司机 → 前往装货 → 装货完成 → 运输中 → 送达完成 → 对账结算
```

### 2. 库存管理流程
```
商品入库 → 库存更新 → 运输出库 → 库存扣减 → 库存盘点 → 库存调整
```

### 3. 运费计算流程
```
获取运输参数 → 匹配计费规则 → 计算基础运费 → 添加附加费用 → 生成最终运费
```

## 数据字典

### 枚举值说明

#### 运输单状态 (order_status)
- 1: 待指派
- 2: 已指派
- 3: 前往装货
- 4: 装货中
- 5: 运输中
- 6: 已送达
- 7: 已对账

#### 车辆状态 (vehicle_status)
- 1: 空闲
- 2: 运输中
- 3: 维修中
- 4: 报废

#### 司机状态 (driver_status)
- 1: 在职
- 2: 休假
- 3: 离职

#### 客户状态 (customer_status)
- 1: 正常合作
- 2: 暂停合作
- 3: 终止合作

#### 计费方式 (pricing_type)
- 1: 按距离
- 2: 按重量
- 3: 按体积
- 4: 固定价格

## 索引设计

### 主要索引
- **主键索引**: 所有表的id字段
- **唯一索引**: 编码类字段(order_no, customer_code等)
- **外键索引**: 所有外键字段
- **业务索引**: 状态、时间、类型等查询字段

### 复合索引建议
- `(customer_id, order_status, create_time)` - 客户订单查询
- `(vehicle_id, order_status)` - 车辆任务查询
- `(driver_id, order_status)` - 司机任务查询
- `(warehouse_id, product_id)` - 库存查询

## 性能优化建议

1. **分区策略**: 大表可按时间分区
2. **读写分离**: 查询和写入分离
3. **缓存策略**: 热点数据缓存
4. **归档策略**: 历史数据定期归档

这个数据库设计支持运输管理系统的所有核心业务功能，具有良好的扩展性和性能表现。
