-- 运输对账表
DROP TABLE IF EXISTS `transport_receipts`;
CREATE TABLE `transport_receipts` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `receipt_number` varchar(32) NOT NULL COMMENT '收据号码/订单号',
    `amount` decimal(12,2) DEFAULT NULL COMMENT '款项金额(分)',
    `fund_type` tinyint(1) DEFAULT 0 COMMENT '款项类型  预收款 0 应付款 1 红冲应收 2 红冲应付 3',
    `description` varchar(255) DEFAULT NULL COMMENT '描述',
    `customer_code` varchar(32) NOT NULL COMMENT '委托客户编号',
    `internal_code` varchar(32) DEFAULT NULL COMMENT '内部编号',
    `customer_name` varchar(255) NOT NULL COMMENT '客户名称',
    `payment_method` varchar(32) DEFAULT NULL COMMENT '收款方式',
    `bank_name` varchar(255) DEFAULT NULL COMMENT '收款银行',
    `receipt_date` timestamp NULL DEFAULT NULL COMMENT '收款时间',
    `used_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '可用款项（分）',
    `total_prepaid_amount` decimal(12,2) DEFAULT NULL COMMENT '总预存金额',
    `total_expense_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总支出金额',
    `total_arrears_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总欠款金额',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_receipt_number` (`receipt_number`) COMMENT '收据号码唯一键',
    KEY `idx_create_time` (`create_time`),
    KEY `idx_customer_code` (`customer_code`) COMMENT '基于客户编号的索引',
    KEY `idx_receipt_date` (`receipt_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运输对账表';

-- 插入测试数据
INSERT INTO `transport_receipts` (`receipt_number`, `amount`, `fund_type`, `description`, `customer_code`, `internal_code`, `customer_name`, `payment_method`, `bank_name`, `receipt_date`, `used_amount`, `total_prepaid_amount`, `total_expense_amount`, `total_arrears_amount`, `create_by`) VALUES
('TR202401001', 50000.00, 0, '预收运输费用', 'CUST001', 'INT001', '上海物流有限公司', '银行转账', '中国银行', '2024-01-15 10:30:00', 50000.00, 50000.00, 0.00, 0.00, 'admin'),
('TR202401002', 30000.00, 1, '应付运输费用', 'CUST002', 'INT002', '北京运输集团', '现金支付', '工商银行', '2024-01-16 14:20:00', 0.00, 0.00, 30000.00, 0.00, 'admin'),
('TR202401003', 25000.00, 0, '预收货运费', 'CUST003', 'INT003', '广州货运公司', '支票支付', '建设银行', '2024-01-17 09:15:00', 25000.00, 25000.00, 0.00, 0.00, 'admin'),
('TR202401004', 15000.00, 2, '红冲应收款', 'CUST001', 'INT004', '上海物流有限公司', '银行转账', '中国银行', '2024-01-18 16:45:00', 15000.00, 0.00, 0.00, 15000.00, 'admin'),
('TR202401005', 40000.00, 1, '应付配送费', 'CUST004', 'INT005', '深圳快递服务', '网银转账', '招商银行', '2024-01-19 11:30:00', 0.00, 0.00, 40000.00, 0.00, 'admin');
