-- 运输管理系统数据库表结构
-- 根据后端研发设计文档创建

-- 车辆信息表
DROP TABLE IF EXISTS `transport_vehicle`;
CREATE TABLE `transport_vehicle` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `license_plate` varchar(50) NOT NULL COMMENT '车牌号',
  `vehicle_type` varchar(50) DEFAULT NULL COMMENT '车辆类型',
  `load_capacity` decimal(10,2) DEFAULT NULL COMMENT '载重吨位',
  `fuel_tank_capacity` decimal(10,2) DEFAULT NULL COMMENT '油箱容量(升)',
  `vehicle_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '车辆状态:1-空闲,2-运输中,3-维修中,4-报废',
  `purchase_date` date DEFAULT NULL COMMENT '购买日期',
  `annual_inspection_date` date DEFAULT NULL COMMENT '年检日期',
  `insurance_expiry_date` date DEFAULT NULL COMMENT '保险到期日期',
  `region_code` varchar(20) DEFAULT NULL COMMENT '所属区域代码',
  `remark` text COMMENT '备注',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_license_plate` (`license_plate`),
  KEY `idx_vehicle_status` (`vehicle_status`),
  KEY `idx_region_code` (`region_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='车辆信息表';

-- 司机信息表
DROP TABLE IF EXISTS `transport_driver`;
CREATE TABLE `transport_driver` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `driver_name` varchar(50) NOT NULL COMMENT '司机姓名',
  `driver_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `license_type` varchar(10) DEFAULT NULL COMMENT '驾照类型',
  `license_number` varchar(50) DEFAULT NULL COMMENT '驾照号码',
  `driving_years` int(11) DEFAULT NULL COMMENT '驾龄(年)',
  `driver_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '司机状态:1-在职,2-休假,3-离职',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `emergency_contact` varchar(50) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系电话',
  `remark` text COMMENT '备注',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  KEY `idx_driver_phone` (`driver_phone`),
  KEY `idx_driver_status` (`driver_status`),
  KEY `idx_id_card` (`id_card`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='司机信息表';

-- 委托客户信息表
DROP TABLE IF EXISTS `transport_customer`;
CREATE TABLE `transport_customer` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `customer_name` varchar(255) NOT NULL COMMENT '委托客户名称',
  `customer_code` varchar(50) DEFAULT NULL COMMENT '客户编码',
  `company_type` varchar(100) DEFAULT NULL COMMENT '公司类型(贸易公司/生产企业/经销商等)',
  `business_license` varchar(100) DEFAULT NULL COMMENT '营业执照号',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '主要联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `province_code` varchar(10) DEFAULT NULL COMMENT '省份编码',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `city_code` varchar(10) DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
  `district_code` varchar(10) DEFAULT NULL COMMENT '区县编码',
  `district_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
  `detail_address` varchar(500) DEFAULT NULL COMMENT '详细地址',
  `customer_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '客户状态:1-正常合作,2-暂停合作,3-终止合作',
  `credit_rating` varchar(10) DEFAULT NULL COMMENT '信用等级(AAA/AA/A/B/C)',
  `settlement_cycle` int(11) DEFAULT '30' COMMENT '结算周期(天)',
  `payment_method` tinyint(4) DEFAULT '1' COMMENT '付款方式:1-月结,2-现结,3-预付',
  `freight_discount` decimal(5,2) DEFAULT '100.00' COMMENT '运费折扣(%)',
  `cooperation_start_date` date DEFAULT NULL COMMENT '合作开始日期',
  `main_products` text COMMENT '主要经营产品',
  `delivery_regions` text COMMENT '主要配送区域',
  `remark` text COMMENT '备注',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_code` (`customer_code`),
  KEY `idx_customer_status` (`customer_status`),
  KEY `idx_customer_name` (`customer_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='委托客户信息表';

-- 终端收货方信息表
DROP TABLE IF EXISTS `transport_consignee`;
CREATE TABLE `transport_consignee` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `customer_id` int(11) NOT NULL COMMENT '委托客户ID',
  `consignee_name` varchar(255) NOT NULL COMMENT '收货方名称',
  `consignee_code` varchar(50) DEFAULT NULL COMMENT '收货方编码',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `province_code` varchar(10) DEFAULT NULL COMMENT '省份编码',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `city_code` varchar(10) DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
  `district_code` varchar(10) DEFAULT NULL COMMENT '区县编码',
  `district_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
  `detail_address` varchar(500) NOT NULL COMMENT '详细地址',
  `consignee_type` tinyint(4) DEFAULT '1' COMMENT '收货方类型:1-加油站,2-工厂,3-仓库,4-其他',
  `is_active` tinyint(4) DEFAULT '1' COMMENT '是否启用:1-启用,0-停用',
  `remark` text COMMENT '备注',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_consignee_code` (`consignee_code`),
  KEY `idx_consignee_name` (`consignee_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='终端收货方信息表';

-- 装货点信息表
DROP TABLE IF EXISTS `transport_loading_point`;
CREATE TABLE `transport_loading_point` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `point_name` varchar(255) NOT NULL COMMENT '装货点名称',
  `point_code` varchar(50) DEFAULT NULL COMMENT '装货点编码',
  `point_type` tinyint(4) NOT NULL COMMENT '装货点类型:1-港口,2-油库,3-炼厂,4-其他',
  `province_code` varchar(10) DEFAULT NULL COMMENT '省份编码',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `city_code` varchar(10) DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
  `district_code` varchar(10) DEFAULT NULL COMMENT '区县编码',
  `district_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
  `detail_address` varchar(500) NOT NULL COMMENT '详细地址',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `operating_hours` varchar(100) DEFAULT NULL COMMENT '营业时间',
  `loading_capacity` varchar(100) DEFAULT NULL COMMENT '装货能力',
  `special_requirements` text COMMENT '特殊要求',
  `is_active` tinyint(4) DEFAULT '1' COMMENT '是否启用:1-启用,0-停用',
  `remark` text COMMENT '备注',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_point_code` (`point_code`),
  KEY `idx_point_type` (`point_type`),
  KEY `idx_point_name` (`point_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='装货点信息表';

-- 运输单主表
DROP TABLE IF EXISTS `transport_order`;
CREATE TABLE `transport_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `order_no` varchar(32) NOT NULL COMMENT '运输单号',
  `internal_code` varchar(32) NOT NULL COMMENT '内部编号',
  `customer_id` int(11) NOT NULL COMMENT '委托客户ID',
  `customer_name` varchar(255) NOT NULL COMMENT '委托客户名称',
  `consignee_id` int(11) DEFAULT NULL COMMENT '收货方ID',
  `consignee_name` varchar(255) NOT NULL COMMENT '收货方名称',
  `consignee_province_code` varchar(10) DEFAULT NULL COMMENT '收货方省份编码',
  `consignee_province` varchar(50) DEFAULT NULL COMMENT '收货方省份',
  `consignee_city_code` varchar(10) DEFAULT NULL COMMENT '收货方城市编码',
  `consignee_city` varchar(50) DEFAULT NULL COMMENT '收货方城市',
  `consignee_district_code` varchar(10) DEFAULT NULL COMMENT '收货方区县编码',
  `consignee_district` varchar(50) DEFAULT NULL COMMENT '收货方区县',
  `consignee_address` varchar(500) NOT NULL COMMENT '收货详细地址',
  `consignee_contact` varchar(100) DEFAULT NULL COMMENT '收货方联系人',
  `consignee_phone` varchar(20) DEFAULT NULL COMMENT '收货方电话',
  `loading_point_id` int(11) DEFAULT NULL COMMENT '装货点ID',
  `loading_point_name` varchar(255) NOT NULL COMMENT '装货点名称',
  `loading_province_code` varchar(10) DEFAULT NULL COMMENT '装货点省份编码',
  `loading_province` varchar(50) DEFAULT NULL COMMENT '装货点省份',
  `loading_city_code` varchar(10) DEFAULT NULL COMMENT '装货点城市编码',
  `loading_city` varchar(50) DEFAULT NULL COMMENT '装货点城市',
  `loading_district_code` varchar(10) DEFAULT NULL COMMENT '装货点区县编码',
  `loading_district` varchar(50) DEFAULT NULL COMMENT '装货点区县',
  `loading_address` varchar(500) NOT NULL COMMENT '装货详细地址',
  `product_name` varchar(100) DEFAULT NULL COMMENT '油品名称',
  `product_quantity` decimal(10,2) DEFAULT NULL COMMENT '运输数量(吨)',
  `total_volume` decimal(10,2) DEFAULT NULL COMMENT '总体积(升)',
  `transport_distance` decimal(10,2) DEFAULT NULL COMMENT '运输距离(公里)',
  `order_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态:1-待指派,2-已指派,3-前往装货,4-装货中,5-运输中,6-已送达,7-已对账',
  `vehicle_id` int(11) DEFAULT NULL COMMENT '指派车辆ID',
  `driver_id` int(11) DEFAULT NULL COMMENT '指派司机ID',
  `license_plate` varchar(50) DEFAULT NULL COMMENT '车牌号',
  `planned_loading_time` datetime DEFAULT NULL COMMENT '计划装货时间',
  `actual_loading_time` datetime DEFAULT NULL COMMENT '实际装货时间',
  `planned_delivery_time` datetime DEFAULT NULL COMMENT '计划送达时间',
  `actual_delivery_time` datetime DEFAULT NULL COMMENT '实际送达时间',
  `loading_completed_time` datetime DEFAULT NULL COMMENT '装货完成时间',
  `departure_time` datetime DEFAULT NULL COMMENT '出发时间',
  `arrival_time` datetime DEFAULT NULL COMMENT '到达时间',
  `shipping_cost` decimal(10,2) NOT NULL COMMENT '运输费用',
  `other_expenses` decimal(10,2) DEFAULT '0.00' COMMENT '杂费',
  `tax_included` decimal(10,2) DEFAULT NULL COMMENT '含税单价',
  `delivery_requirements` text COMMENT '配送要求',
  `special_instructions` text COMMENT '特殊说明',
  `remark` text COMMENT '备注',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  UNIQUE KEY `uk_internal_code` (`internal_code`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_consignee_id` (`consignee_id`),
  KEY `idx_loading_point_id` (`loading_point_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_driver_id` (`driver_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运输单主表';

-- 运费定价信息表
DROP TABLE IF EXISTS `transport_shipping_rates`;
CREATE TABLE `transport_shipping_rates` (
    `id`                    INT AUTO_INCREMENT COMMENT '唯一标识ID' PRIMARY KEY,
    `loading_point_id`      INT                                  NOT NULL COMMENT '装货点ID',
    `loading_point_name`    VARCHAR(255)                         NULL COMMENT '装货点名称',
    `start_location`        VARCHAR(255)                         NOT NULL COMMENT '起点位置',
    `end_location`          VARCHAR(255)                         NOT NULL COMMENT '终点位置',
    `end_city_code`         VARCHAR(10)                          NOT NULL COMMENT '结束的市编码',
    `start_city_code`       VARCHAR(10)                          NOT NULL COMMENT '开始的市编码',
    `freight_unit`          DECIMAL(10, 2)                       NOT NULL COMMENT '运费单价',
    `tax_included`          DECIMAL(12, 4)                       NOT NULL COMMENT '含税单价',
    `minimum_volume`        DECIMAL(10, 2)                       NULL COMMENT '最低起送量',
    `internal_codes`        TEXT                                 NULL COMMENT '客户内部编号集合',
    `rate_type`             TINYINT    DEFAULT 0                 NOT NULL COMMENT '规则类型 0 默认 1 特定',
    `product_type`          VARCHAR(50)                          NULL COMMENT '适用油品类型',
    `contract_freight_unit` DECIMAL(10, 2)                       NOT NULL COMMENT '合同运费单价',
    `contract_tax_included` DECIMAL(12, 4)                       NOT NULL COMMENT '合同含税单价',
    `create_by`             VARCHAR(100)                         NULL COMMENT '创建人',
    `create_time`           TIMESTAMP  DEFAULT CURRENT_TIMESTAMP NULL COMMENT '创建时间',
    `update_by`             VARCHAR(100)                         NULL COMMENT '修改人',
    `update_time`           TIMESTAMP  DEFAULT CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `is_deleted`            TINYINT(1) DEFAULT 0                 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除'
) COMMENT '运费定价信息表' CHARSET = utf8;

-- 插入初始数据
INSERT INTO `transport_vehicle` (`license_plate`, `vehicle_type`, `load_capacity`, `vehicle_status`, `create_by`) VALUES
('京A12345', '油罐车', 20.00, 1, 'admin'),
('沪B67890', '油罐车', 25.00, 1, 'admin'),
('粤C11111', '油罐车', 30.00, 1, 'admin');

INSERT INTO `transport_driver` (`driver_name`, `driver_phone`, `driver_status`, `create_by`) VALUES
('张三', '13800138001', 1, 'admin'),
('李四', '13800138002', 1, 'admin'),
('王五', '13800138003', 1, 'admin');

INSERT INTO `transport_customer` (`customer_name`, `customer_code`, `customer_status`, `create_by`) VALUES
('XX石油贸易公司', 'CUST001', 1, 'admin'),
('YY能源集团', 'CUST002', 1, 'admin'),
('ZZ化工有限公司', 'CUST003', 1, 'admin');

INSERT INTO `transport_loading_point` (`point_name`, `point_code`, `point_type`, `detail_address`, `create_by`) VALUES
('上海港石油码头', 'LP001', 1, '上海市浦东新区港口大道1号', 'admin'),
('青岛港油库', 'LP002', 2, '山东省青岛市黄岛区港湾路88号', 'admin'),
('大连石化炼厂', 'LP003', 3, '辽宁省大连市甘井子区石化路168号', 'admin');

-- 运费计算规则表
DROP TABLE IF EXISTS `transport_pricing_rule`;
CREATE TABLE `transport_pricing_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `rule_name` varchar(255) NOT NULL COMMENT '规则名称',
  `rule_code` varchar(50) DEFAULT NULL COMMENT '规则编码',
  `pricing_type` tinyint(4) NOT NULL COMMENT '计费方式:1-按距离,2-按重量,3-按体积,4-固定价格',
  `base_price` decimal(10,2) DEFAULT NULL COMMENT '基础价格',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单位价格(每公里/每吨/每升)',
  `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低价格',
  `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高价格',
  `customer_id` int(11) DEFAULT NULL COMMENT '适用客户ID(为空表示通用规则)',
  `route_id` int(11) DEFAULT NULL COMMENT '适用路线ID(为空表示通用规则)',
  `product_type` varchar(100) DEFAULT NULL COMMENT '适用油品类型',
  `effective_date` date DEFAULT NULL COMMENT '生效日期',
  `expiry_date` date DEFAULT NULL COMMENT '失效日期',
  `rule_status` tinyint(4) DEFAULT '1' COMMENT '规则状态:1-启用,0-停用',
  `rule_desc` text COMMENT '规则描述',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_route_id` (`route_id`),
  KEY `idx_product_type` (`product_type`),
  KEY `idx_rule_status` (`rule_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运费计算规则表';

-- 运输路线表
DROP TABLE IF EXISTS `transport_route`;
CREATE TABLE `transport_route` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `route_name` varchar(255) NOT NULL COMMENT '路线名称',
  `route_code` varchar(50) DEFAULT NULL COMMENT '路线编码',
  `start_point_id` int(11) NOT NULL COMMENT '起点装货点ID',
  `start_point_name` varchar(255) DEFAULT NULL COMMENT '起点装货点名称',
  `end_point_id` int(11) NOT NULL COMMENT '终点收货方ID',
  `end_point_name` varchar(255) DEFAULT NULL COMMENT '终点收货方名称',
  `route_distance` decimal(10,2) DEFAULT NULL COMMENT '路线距离(公里)',
  `estimated_time` decimal(10,2) DEFAULT NULL COMMENT '预计运输时间(小时)',
  `standard_freight` decimal(10,2) DEFAULT NULL COMMENT '标准运费',
  `route_status` tinyint(4) DEFAULT '1' COMMENT '路线状态:1-启用,0-停用',
  `route_desc` text COMMENT '路线描述',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_route_code` (`route_code`),
  KEY `idx_start_point_id` (`start_point_id`),
  KEY `idx_end_point_id` (`end_point_id`),
  KEY `idx_route_status` (`route_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运输路线表';

-- 车辆维护记录表
DROP TABLE IF EXISTS `transport_vehicle_maintenance`;
CREATE TABLE `transport_vehicle_maintenance` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `vehicle_id` int(11) NOT NULL COMMENT '车辆ID',
  `maintenance_type` varchar(100) DEFAULT NULL COMMENT '维护类型',
  `maintenance_date` date DEFAULT NULL COMMENT '维护日期',
  `maintenance_cost` decimal(10,2) DEFAULT NULL COMMENT '维护费用',
  `maintenance_desc` text COMMENT '维护描述',
  `next_maintenance_date` date DEFAULT NULL COMMENT '下次维护日期',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_maintenance_date` (`maintenance_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='车辆维护记录表';

-- 运费定价杂费细则信息表
DROP TABLE IF EXISTS `transport_shipping_rate_detail`;
CREATE TABLE `transport_shipping_rate_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
  `rate_id` int(11) NOT NULL COMMENT '规则id',
  `vehicle_type` varchar(50) DEFAULT NULL COMMENT '车辆类型',
  `other_expenses` decimal(10,2) NOT NULL COMMENT '杂费',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
  PRIMARY KEY (`id`),
  KEY `idx_rate_id` (`rate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运费定价杂费细则信息表';

-- 插入初始运费规则数据
INSERT INTO `transport_pricing_rule` (`rule_name`, `rule_code`, `pricing_type`, `base_price`, `unit_price`, `min_price`, `max_price`, `rule_status`, `create_by`) VALUES
('通用按距离计费', 'RULE001', 1, 500.00, 2.50, 300.00, 5000.00, 1, 'admin'),
('通用按重量计费', 'RULE002', 2, 200.00, 150.00, 500.00, 8000.00, 1, 'admin'),
('固定价格规则', 'RULE003', 4, 1000.00, 0.00, 1000.00, 1000.00, 1, 'admin');
