# 运输管理系统数据库建表脚本说明

## 概述

本目录包含运输管理系统的完整数据库建表脚本和相关文档。

## 文件说明

### 主要脚本文件
- `transport_complete_schema.sql` - 完整的建表脚本，包含所有表结构和初始数据
- `transport_address_schema.sql` - 地址管理相关表的建表脚本

### 脚本特点
- ✅ **完整性** - 包含系统所有核心业务表
- ✅ **规范性** - 遵循数据库设计规范
- ✅ **安全性** - 包含外键约束和数据完整性检查
- ✅ **初始化** - 包含必要的初始数据
- ✅ **兼容性** - 支持MySQL 5.7+版本

## 数据库表结构

### 1. 地址管理模块 (4张表)
- `transport_countries` - 国家表
- `transport_provinces` - 省份表
- `transport_cities` - 城市表
- `transport_districts` - 区县表

### 2. 车辆管理模块 (1张表)
- `transport_vehicle` - 车辆信息表

### 3. 司机管理模块 (2张表)
- `transport_driver` - 司机信息表
- `transport_driver_schedule` - 司机排班表

### 4. 客户管理模块 (1张表)
- `transport_customer` - 客户信息表

### 5. 收货方管理模块 (1张表)
- `transport_consignee` - 收货方信息表

### 6. 装货点管理模块 (1张表)
- `transport_loading_point` - 装货点信息表

### 7. 运输单管理模块 (2张表)
- `transport_order` - 运输单主表
- `transport_order_status_log` - 运输单状态变更记录表

### 8. 运费规则管理模块 (2张表)
- `transport_pricing_rule` - 运费规则表
- `transport_additional_fee` - 附加费用规则表

### 9. 仓库管理模块 (6张表)
- `transport_warehouse` - 仓库信息表
- `transport_product` - 商品信息表
- `transport_inventory` - 库存信息表
- `transport_inventory_record` - 出入库记录主表
- `transport_inventory_record_detail` - 出入库记录明细表

### 10. 系统配置模块 (1张表)
- `transport_system_config` - 系统参数配置表

**总计：21张核心业务表**

## 使用方法

### 1. 环境要求
- MySQL 5.7+ 或 MariaDB 10.2+
- 数据库字符集：utf8mb4
- 存储引擎：InnoDB

### 2. 执行步骤

#### 方法一：直接执行完整脚本
```sql
-- 1. 创建数据库
CREATE DATABASE transport_system DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 使用数据库
USE transport_system;

-- 3. 执行建表脚本
SOURCE transport_complete_schema.sql;
```

#### 方法二：通过MySQL命令行
```bash
# 1. 登录MySQL
mysql -u root -p

# 2. 创建数据库
mysql> CREATE DATABASE transport_system DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 3. 使用数据库
mysql> USE transport_system;

# 4. 执行脚本
mysql> SOURCE /path/to/transport_complete_schema.sql;
```

#### 方法三：通过MySQL客户端工具
1. 使用Navicat、phpMyAdmin等工具
2. 创建数据库 `transport_system`
3. 导入 `transport_complete_schema.sql` 文件

### 3. 验证安装
执行以下SQL验证表是否创建成功：
```sql
-- 查看所有表
SHOW TABLES;

-- 查看表数量（应该是21张表）
SELECT COUNT(*) as table_count FROM information_schema.tables 
WHERE table_schema = 'transport_system';

-- 查看初始数据
SELECT COUNT(*) FROM transport_countries;  -- 应该有1条记录
SELECT COUNT(*) FROM transport_provinces;  -- 应该有31条记录
SELECT COUNT(*) FROM transport_product;    -- 应该有8条记录
```

## 初始数据说明

### 地址数据
- **国家**: 中国(CN)
- **省份**: 31个省市自治区
- **城市**: 主要一二线城市
- **区县**: 北京、上海、广州的主要区县

### 商品数据
- 92号汽油、95号汽油、98号汽油
- 0号柴油、-10号柴油、-20号柴油
- 航空煤油、润滑油

### 运费规则
- 通用按距离计费规则
- 通用按重量计费规则
- 通用按体积计费规则
- 短途固定价格规则

### 附加费用
- 过夜费、高速费、偏远地区费
- 装卸费、等待费

### 系统配置
- 运输单号生成规则
- 业务参数配置
- 系统开关配置

## 数据库设计特点

### 1. 规范化设计
- 遵循第三范式
- 合理的表关系设计
- 避免数据冗余

### 2. 性能优化
- 合理的索引设计
- 主键和外键约束
- 查询性能优化

### 3. 数据完整性
- 外键约束保证数据一致性
- 非空约束和默认值
- 数据类型合理选择

### 4. 扩展性
- 预留扩展字段
- 模块化表设计
- 支持业务扩展

### 5. 安全性
- 逻辑删除设计
- 操作日志记录
- 数据备份支持

## 注意事项

### 1. 字符集设置
- 必须使用 utf8mb4 字符集
- 支持中文和特殊字符
- 避免乱码问题

### 2. 存储引擎
- 使用 InnoDB 存储引擎
- 支持事务和外键
- 更好的并发性能

### 3. 索引优化
- 已创建必要的索引
- 根据实际查询需求可以添加复合索引
- 定期分析索引使用情况

### 4. 数据备份
- 建议定期备份数据库
- 重要操作前先备份
- 测试环境和生产环境分离

### 5. 权限管理
- 为应用程序创建专用数据库用户
- 分配最小必要权限
- 定期更新密码

## 升级和维护

### 1. 版本管理
- 脚本文件包含版本信息
- 记录每次结构变更
- 提供升级脚本

### 2. 数据迁移
- 提供数据迁移工具
- 支持从旧版本升级
- 保证数据完整性

### 3. 性能监控
- 定期检查表大小
- 监控查询性能
- 优化慢查询

## 技术支持

如果在使用过程中遇到问题，请检查：
1. MySQL版本是否符合要求
2. 字符集设置是否正确
3. 用户权限是否充足
4. 磁盘空间是否充足

更多技术支持请参考项目文档或联系开发团队。
