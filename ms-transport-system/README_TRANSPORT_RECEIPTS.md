# 运输对账管理功能说明

## 概述

运输对账管理功能用于管理运输业务中的收款、付款、红冲等财务对账信息，支持客户资金管理、对账单生成和Excel导入导出等功能。

## 功能特性

### 1. 对账信息管理
- ✅ 对账信息CRUD操作
- ✅ 收据号码唯一性验证
- ✅ 多种款项类型支持（预收款、应付款、红冲应收、红冲应付）
- ✅ 客户资金余额计算
- ✅ 多维度查询和统计

### 2. 款项类型说明
- **预收款(0)**: 客户预先支付的运输费用
- **应付款(1)**: 需要支付给客户或供应商的费用
- **红冲应收(2)**: 冲减应收账款的红字发票
- **红冲应付(3)**: 冲减应付账款的红字发票

### 3. Excel导入导出
- ✅ 支持Excel批量导入对账数据
- ✅ 支持按客户导出对账单
- ✅ 导入数据验证和错误提示
- ✅ 导出文件自定义命名

### 4. 客户资金管理
- ✅ 客户期初余额查询
- ✅ 资金使用情况统计
- ✅ 预存金额、支出金额、欠款金额汇总

## 数据库表结构

### transport_receipts 运输对账表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 唯一标识ID |
| receipt_number | varchar(32) | 收据号码/订单号（唯一） |
| amount | decimal(12,2) | 款项金额(分) |
| fund_type | tinyint(1) | 款项类型 0-预收款 1-应付款 2-红冲应收 3-红冲应付 |
| description | varchar(255) | 描述 |
| customer_code | varchar(32) | 委托客户编号 |
| internal_code | varchar(32) | 内部编号 |
| customer_name | varchar(255) | 客户名称 |
| payment_method | varchar(32) | 收款方式 |
| bank_name | varchar(255) | 收款银行 |
| receipt_date | timestamp | 收款时间 |
| used_amount | decimal(12,2) | 可用款项（分） |
| total_prepaid_amount | decimal(12,2) | 总预存金额 |
| total_expense_amount | decimal(12,2) | 总支出金额 |
| total_arrears_amount | decimal(12,2) | 总欠款金额 |
| create_by | varchar(100) | 创建人 |
| create_time | timestamp | 创建时间 |
| update_by | varchar(100) | 修改人 |
| update_time | timestamp | 修改时间 |
| is_deleted | tinyint(1) | 逻辑删除标志 |

### 索引说明
- `PRIMARY KEY (id)`: 主键索引
- `UNIQUE KEY uniq_receipt_number (receipt_number)`: 收据号码唯一索引
- `KEY idx_customer_code (customer_code)`: 客户编号索引
- `KEY idx_receipt_date (receipt_date)`: 收款时间索引
- `KEY idx_create_time (create_time)`: 创建时间索引

## API接口

### 基础CRUD接口
- `GET /transport/receipts/list` - 查询对账列表
- `GET /transport/receipts/{id}` - 查询对账详情
- `POST /transport/receipts` - 新增对账信息
- `PUT /transport/receipts` - 修改对账信息
- `DELETE /transport/receipts/{id}` - 删除对账信息
- `DELETE /transport/receipts/{ids}` - 批量删除对账信息

### 业务功能接口
- `GET /transport/receipts/getInitialAmount` - 查询客户期初余额
- `GET /transport/receipts/export` - 导出对账单
- `POST /transport/receipts/importToExcel` - 导入对账数据

## 权限配置

### 菜单权限
- `transport:receipts:list` - 查询对账列表
- `transport:receipts:query` - 查询对账详情
- `transport:receipts:add` - 新增对账信息
- `transport:receipts:edit` - 修改对账信息
- `transport:receipts:delete` - 删除对账信息
- `transport:receipts:export` - 导出对账单
- `transport:receipts:import` - 导入对账数据
- `transport:receipts:initial.amount` - 查询期初余额

## 使用说明

### 1. 新增对账信息
1. 填写收据号码（必填，系统自动验证唯一性）
2. 选择款项类型
3. 输入款项金额
4. 填写客户信息（客户编号、客户名称必填）
5. 选择收款方式和银行
6. 设置收款时间
7. 填写描述信息

### 2. 批量导入
1. 下载Excel模板
2. 按模板格式填写数据
3. 上传Excel文件
4. 系统自动验证数据并导入

### 3. 导出对账单
1. 设置查询条件（客户编号、时间范围等）
2. 点击导出按钮
3. 系统生成Excel文件并下载

### 4. 客户资金查询
1. 输入客户编号
2. 查询期初余额和资金使用情况
3. 查看预存、支出、欠款等统计信息

## 注意事项

1. **收据号码唯一性**: 系统会自动验证收据号码的唯一性，重复的收据号码无法保存
2. **款项类型影响**: 不同的款项类型会影响客户资金余额的计算
3. **逻辑删除**: 系统采用逻辑删除，删除的记录不会物理删除，只是标记为已删除
4. **数据验证**: 导入Excel时会进行数据格式和业务规则验证
5. **权限控制**: 所有操作都需要相应的权限，请确保用户具有必要的权限

## 技术实现

### 核心类说明
- `TransportReceipts`: 对账信息实体类
- `TransportReceiptsDetails`: Excel导入导出DTO类
- `TransportReceiptsMapper`: 数据访问接口
- `ITransportReceiptsService`: 业务服务接口
- `TransportReceiptsServiceImpl`: 业务服务实现类
- `TransportReceiptsController`: 控制器类
- `ITransportExcelProcessService`: Excel处理服务接口

### 关键特性
- 使用MyBatis进行数据访问
- 支持分页查询
- 集成Excel导入导出功能
- 完整的异常处理和日志记录
- 数据验证和业务规则检查

## 扩展功能

### 可扩展的功能点
1. **对账单模板**: 支持自定义对账单Excel模板
2. **自动对账**: 与运输单关联，自动生成对账信息
3. **财务集成**: 与财务系统集成，自动同步财务数据
4. **报表统计**: 生成各种财务报表和统计图表
5. **审批流程**: 添加对账信息审批流程
6. **消息通知**: 对账状态变更时发送消息通知

## 版本历史

- v1.0.0 (2024-01-15): 初始版本，实现基础对账管理功能
  - 对账信息CRUD操作
  - Excel导入导出
  - 客户资金管理
  - 权限控制
