<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportCitiesMapper">

    <resultMap type="com.ruoyi.system.domain.transport.TransportCities" id="TransportCitiesResult">
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="provinceCode" column="province_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTransportCitiesVo">
        select code, name, province_code, create_by, create_time, update_by, update_time
        from transport_cities
    </sql>

    <select id="selectTransportCitiesList" parameterType="String" resultMap="TransportCitiesResult">
        <include refid="selectTransportCitiesVo"/>
        <where>
            <if test="provinceCode != null and provinceCode != ''">
                and province_code = #{provinceCode}
            </if>
        </where>
        order by code
    </select>

    <select id="selectTransportCitiesByCode" parameterType="String" resultMap="TransportCitiesResult">
        <include refid="selectTransportCitiesVo"/>
        where code = #{code}
    </select>

    <select id="selectList" resultMap="TransportCitiesResult">
        <include refid="selectTransportCitiesVo"/>
        where is_deleted = 0
    </select>

</mapper>
