<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportVehicleMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportVehicle" id="TransportVehicleResult">
        <result property="id"    column="id"    />
        <result property="licensePlate"    column="license_plate"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="loadCapacity"    column="load_capacity"    />
        <result property="fuelTankCapacity"    column="fuel_tank_capacity"    />
        <result property="vehicleStatus"    column="vehicle_status"    />
        <result property="purchaseDate"    column="purchase_date"    />
        <result property="annualInspectionDate"    column="annual_inspection_date"    />
        <result property="insuranceExpiryDate"    column="insurance_expiry"    />
        <result property="regionCode"    column="region_code"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTransportVehicleVo">
        select id, license_plate, vehicle_type, load_capacity, fuel_tank_capacity, vehicle_status, 
               purchase_date, annual_inspection_date, insurance_expiry, region_code, is_deleted, 
               create_by, create_time, update_by, update_time, remark 
        from transport_vehicle
    </sql>

    <select id="selectTransportVehicleList" parameterType="com.ruoyi.system.domain.transport.TransportVehicle" resultMap="TransportVehicleResult">
        <include refid="selectTransportVehicleVo"/>
        <where>
            is_deleted = 0
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate like concat('%', #{licensePlate}, '%')</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="vehicleStatus != null "> and vehicle_status = #{vehicleStatus}</if>
            <if test="regionCode != null  and regionCode != ''"> and region_code = #{regionCode}</if>
        </where>
    </select>
    
    <select id="selectTransportVehicleById" parameterType="Long" resultMap="TransportVehicleResult">
        <include refid="selectTransportVehicleVo"/>
        where id = #{id} and is_deleted = 0
    </select>
        
    <insert id="insertTransportVehicle" parameterType="com.ruoyi.system.domain.transport.TransportVehicle" useGeneratedKeys="true" keyProperty="id">
        insert into transport_vehicle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licensePlate != null and licensePlate != ''">license_plate,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="loadCapacity != null">load_capacity,</if>
            <if test="fuelTankCapacity != null">fuel_tank_capacity,</if>
            <if test="vehicleStatus != null">vehicle_status,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="annualInspectionDate != null">annual_inspection_date,</if>
            <if test="insuranceExpiryDate != null">insurance_expiry,</if>
            <if test="regionCode != null">region_code,</if>
            is_deleted,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licensePlate != null and licensePlate != ''">#{licensePlate},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="loadCapacity != null">#{loadCapacity},</if>
            <if test="fuelTankCapacity != null">#{fuelTankCapacity},</if>
            <if test="vehicleStatus != null">#{vehicleStatus},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="annualInspectionDate != null">#{annualInspectionDate},</if>
            <if test="insuranceExpiryDate != null">#{insuranceExpiryDate},</if>
            <if test="regionCode != null">#{regionCode},</if>
            0,
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate(),
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTransportVehicle" parameterType="com.ruoyi.system.domain.transport.TransportVehicle">
        update transport_vehicle
        <trim prefix="SET" suffixOverrides=",">
            <if test="licensePlate != null and licensePlate != ''">license_plate = #{licensePlate},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="loadCapacity != null">load_capacity = #{loadCapacity},</if>
            <if test="fuelTankCapacity != null">fuel_tank_capacity = #{fuelTankCapacity},</if>
            <if test="vehicleStatus != null">vehicle_status = #{vehicleStatus},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="annualInspectionDate != null">annual_inspection_date = #{annualInspectionDate},</if>
            <if test="insuranceExpiryDate != null">insurance_expiry = #{insuranceExpiryDate},</if>
            <if test="regionCode != null">region_code = #{regionCode},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTransportVehicleById" parameterType="Long">
        update transport_vehicle set is_deleted = 1, update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteTransportVehicleByIds" parameterType="String">
        update transport_vehicle set is_deleted = 1, update_time = sysdate() where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectVehicleByLicensePlate" resultMap="TransportVehicleResult">
        <include refid="selectTransportVehicleVo"/>
        where license_plate = #{licensePlate} and is_deleted = 0
    </select>

    <select id="checkLicensePlateExists" resultType="int">
        select count(1) from transport_vehicle where license_plate = #{licensePlate} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="selectAvailableVehicles" resultMap="TransportVehicleResult">
        <include refid="selectTransportVehicleVo"/>
        where vehicle_status = 1 and is_deleted = 0
    </select>

    <update id="updateVehicleStatus">
        update transport_vehicle set vehicle_status = #{status}, update_by = #{updateBy}, update_time = sysdate() where id = #{id}
    </update>

    <select id="selectVehiclesByRegion" resultMap="TransportVehicleResult">
        <include refid="selectTransportVehicleVo"/>
        where region_code = #{regionCode} and is_deleted = 0
    </select>

    <select id="selectVehicleStatusStatistics" resultType="java.util.Map">
        select vehicle_status, count(id) as vehicle_count from transport_vehicle 
        where is_deleted = 0 
        group by vehicle_status
    </select>

    <select id="selectExpiringVehicles" resultMap="TransportVehicleResult">
        <include refid="selectTransportVehicleVo"/>
        where is_deleted = 0 and (
            (annual_inspection_date is not null and annual_inspection_date &lt;= date_add(curdate(), interval #{days} day))
            or 
            (insurance_expiry is not null and insurance_expiry &lt;= date_add(curdate(), interval #{days} day))
        )
    </select>

    <select id="selectVehicleDropdownList" resultType="com.ruoyi.system.domain.dto.TransportVehicleDropdownDto">
        select
            id,
            license_plate as licensePlate
        from
            transport_vehicle
        <where>
            is_deleted = 0 and vehicle_status = 1
            <if test="keyword != null and keyword != ''">
                and license_plate like concat('%', #{keyword}, '%')
            </if>
        </where>
        order by license_plate
    </select>

</mapper>