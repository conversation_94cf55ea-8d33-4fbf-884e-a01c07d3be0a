<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportDistrictsMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportDistricts" id="TransportDistrictsResult">
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="cityCode"    column="city_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTransportDistrictsVo">
        select code, name, city_code, create_by, create_time, update_by, update_time from transport_districts
    </sql>

    <select id="selectTransportDistrictsList" parameterType="String" resultMap="TransportDistrictsResult">
        <include refid="selectTransportDistrictsVo"/>
        <where>
            <if test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </if>
        </where>
        order by code
    </select>
    
    <select id="selectTransportDistrictsByCode" parameterType="String" resultMap="TransportDistrictsResult">
        <include refid="selectTransportDistrictsVo"/>
        where code = #{code}
    </select>

</mapper>
