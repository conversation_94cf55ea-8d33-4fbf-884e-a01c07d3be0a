<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportDriverMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportDriver" id="TransportDriverResult">
        <result property="id"    column="id"    />
        <result property="driverName"    column="driver_name"    />
        <result property="driverPhone"    column="driver_phone"    />
        <result property="idCard"    column="id_card"    />
        <result property="licenseType"    column="license_type"    />
        <result property="licenseNumber"    column="license_number"    />
        <result property="drivingYears"    column="driving_years"    />
        <result property="driverStatus"    column="driver_status"    />
        <result property="hireDate"    column="hire_date"    />
        <result property="emergencyContact"    column="emergency_contact"    />
        <result property="emergencyPhone"    column="emergency_phone"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTransportDriverVo">
        select id, driver_name, driver_phone, id_card, license_type, license_number, driving_years, 
               driver_status, hire_date, emergency_contact, emergency_phone, is_deleted, 
               create_by, create_time, update_by, update_time, remark 
        from transport_driver
    </sql>

    <select id="selectTransportDriverList" parameterType="com.ruoyi.system.domain.transport.TransportDriver" resultMap="TransportDriverResult">
        <include refid="selectTransportDriverVo"/>
        <where>  
            is_deleted = 0
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="driverPhone != null  and driverPhone != ''"> and driver_phone = #{driverPhone}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="licenseType != null  and licenseType != ''"> and license_type = #{licenseType}</if>
            <if test="drivingYears != null "> and driving_years = #{drivingYears}</if>
            <if test="driverStatus != null "> and driver_status = #{driverStatus}</if>
        </where>
    </select>
    
    <select id="selectTransportDriverById" parameterType="Long" resultMap="TransportDriverResult">
        <include refid="selectTransportDriverVo"/>
        where id = #{id} and is_deleted = 0
    </select>
        
    <insert id="insertTransportDriver" parameterType="com.ruoyi.system.domain.transport.TransportDriver" useGeneratedKeys="true" keyProperty="id">
        insert into transport_driver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="driverName != null and driverName != ''">driver_name,</if>
            <if test="driverPhone != null and driverPhone != ''">driver_phone,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="licenseType != null">license_type,</if>
            <if test="licenseNumber != null">license_number,</if>
            <if test="drivingYears != null">driving_years,</if>
            <if test="driverStatus != null">driver_status,</if>
            <if test="hireDate != null">hire_date,</if>
            <if test="emergencyContact != null">emergency_contact,</if>
            <if test="emergencyPhone != null">emergency_phone,</if>
            is_deleted,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="driverName != null and driverName != ''">#{driverName},</if>
            <if test="driverPhone != null and driverPhone != ''">#{driverPhone},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="licenseType != null">#{licenseType},</if>
            <if test="licenseNumber != null">#{licenseNumber},</if>
            <if test="drivingYears != null">#{drivingYears},</if>
            <if test="driverStatus != null">#{driverStatus},</if>
            <if test="hireDate != null">#{hireDate},</if>
            <if test="emergencyContact != null">#{emergencyContact},</if>
            <if test="emergencyPhone != null">#{emergencyPhone},</if>
            0,
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate(),
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTransportDriver" parameterType="com.ruoyi.system.domain.transport.TransportDriver">
        update transport_driver
        <trim prefix="SET" suffixOverrides=",">
            <if test="driverName != null and driverName != ''">driver_name = #{driverName},</if>
            <if test="driverPhone != null and driverPhone != ''">driver_phone = #{driverPhone},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="licenseType != null">license_type = #{licenseType},</if>
            <if test="licenseNumber != null">license_number = #{licenseNumber},</if>
            <if test="drivingYears != null">driving_years = #{drivingYears},</if>
            <if test="driverStatus != null">driver_status = #{driverStatus},</if>
            <if test="hireDate != null">hire_date = #{hireDate},</if>
            <if test="emergencyContact != null">emergency_contact = #{emergencyContact},</if>
            <if test="emergencyPhone != null">emergency_phone = #{emergencyPhone},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTransportDriverById" parameterType="Long">
        update transport_driver set is_deleted = 1, update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteTransportDriverByIds" parameterType="String">
        update transport_driver set is_deleted = 1, update_time = sysdate() where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectDriverByPhone" resultMap="TransportDriverResult">
        <include refid="selectTransportDriverVo"/>
        where driver_phone = #{driverPhone} and is_deleted = 0
    </select>

    <select id="checkPhoneExists" resultType="int">
        select count(1) from transport_driver where driver_phone = #{driverPhone} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="checkIdCardExists" resultType="int">
        select count(1) from transport_driver where id_card = #{idCard} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="selectAvailableDrivers" resultMap="TransportDriverResult">
        <include refid="selectTransportDriverVo"/>
        where driver_status = 1 and is_deleted = 0
    </select>

    <update id="updateDriverStatus">
        update transport_driver set driver_status = #{status}, update_by = #{updateBy}, update_time = sysdate() where id = #{id}
    </update>

    <select id="selectDriverStatusStatistics" resultMap="TransportDriverResult">
        select driver_status, count(id) as driver_count from transport_driver where is_deleted = 0 group by driver_status
    </select>

    <select id="selectDriversByLicenseType" resultMap="TransportDriverResult">
        <include refid="selectTransportDriverVo"/>
        where license_type = #{licenseType} and is_deleted = 0
    </select>

    <select id="selectExperiencedDrivers" resultMap="TransportDriverResult">
        <include refid="selectTransportDriverVo"/>
        where driving_years &gt;= #{years} and is_deleted = 0
    </select>

    <select id="selectDriverDropdownList" resultType="com.ruoyi.system.domain.dto.TransportDriverDropdownDto">
        select
            id,
            driver_name as driverName,
            driver_phone as driverPhone
        from
            transport_driver
        <where>
            is_deleted = 0 and driver_status = 1
            <if test="keyword != null and keyword != ''">
                and (driver_name like concat('%', #{keyword}, '%') or driver_phone like concat('%', #{keyword}, '%'))
            </if>
        </where>
        order by driver_name
    </select>

</mapper>