<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportLoadingPointMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportLoadingPoint" id="TransportLoadingPointResult">
        <result property="id"    column="id"    />
        <result property="pointName"    column="point_name"    />
        <result property="pointCode"    column="point_code"    />
        <result property="pointType"    column="point_type"    />
        <result property="countryCode"    column="country_code"    />
        <result property="countryName"    column="country_name"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityCode"    column="city_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="detailAddress"    column="detail_address"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="operatingHours"    column="operating_hours"    />
        <result property="loadingCapacity"    column="loading_capacity"    />
        <result property="specialRequirements"    column="special_requirements"    />
        <result property="isActive"    column="is_active"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTransportLoadingPointVo">
        select id, point_name, point_code, point_type, country_code, country_name, province_code, province_name, city_code, city_name, 
               detail_address, contact_person, contact_phone, operating_hours, 
               loading_capacity, special_requirements, is_active, is_deleted, 
               create_by, create_time, update_by, update_time, remark 
        from transport_loading_point
    </sql>

    <select id="selectTransportLoadingPointList" parameterType="com.ruoyi.system.domain.transport.TransportLoadingPoint" resultMap="TransportLoadingPointResult">
        <include refid="selectTransportLoadingPointVo"/>
        <where>  
            is_deleted = 0
            <if test="pointCode != null  and pointCode != ''"> and point_code = #{pointCode}</if>
            <if test="pointName != null  and pointName != ''"> and point_name like concat('%', #{pointName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="pointType != null "> and point_type = #{pointType}</if>
            <if test="countryCode != null and countryCode != ''"> and country_code = #{countryCode}</if>
            <if test="provinceCode != null and provinceCode != ''"> and province_code = #{provinceCode}</if>
            <if test="cityCode != null and cityCode != ''"> and city_code = #{cityCode}</if>
        </where>
    </select>
    
    <select id="selectTransportLoadingPointById" parameterType="Long" resultMap="TransportLoadingPointResult">
        <include refid="selectTransportLoadingPointVo"/>
        where id = #{id} and is_deleted = 0
    </select>
        
    <insert id="insertTransportLoadingPoint" parameterType="com.ruoyi.system.domain.transport.TransportLoadingPoint" useGeneratedKeys="true" keyProperty="id">
        insert into transport_loading_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">point_name,</if>
            <if test="pointCode != null and pointCode != ''">point_code,</if>
            <if test="pointType != null">point_type,</if>
            <if test="countryCode != null">country_code,</if>
            <if test="countryName != null">country_name,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="cityName != null">city_name,</if>
            <if test="detailAddress != null and detailAddress != ''">detail_address,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="operatingHours != null">operating_hours,</if>
            <if test="loadingCapacity != null">loading_capacity,</if>
            <if test="specialRequirements != null">special_requirements,</if>
            <if test="isActive != null">is_active,</if>
            is_deleted,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">#{pointName},</if>
            <if test="pointCode != null and pointCode != ''">#{pointCode},</if>
            <if test="pointType != null">#{pointType},</if>
            <if test="countryCode != null">#{countryCode},</if>
            <if test="countryName != null">#{countryName},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="detailAddress != null and detailAddress != ''">#{detailAddress},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="operatingHours != null">#{operatingHours},</if>
            <if test="loadingCapacity != null">#{loadingCapacity},</if>
            <if test="specialRequirements != null">#{specialRequirements},</if>
            <if test="isActive != null">#{isActive},</if>
            0,
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate(),
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTransportLoadingPoint" parameterType="com.ruoyi.system.domain.transport.TransportLoadingPoint">
        update transport_loading_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="pointName != null and pointName != ''">point_name = #{pointName},</if>
            <if test="pointCode != null and pointCode != ''">point_code = #{pointCode},</if>
            <if test="pointType != null">point_type = #{pointType},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="countryName != null">country_name = #{countryName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="detailAddress != null and detailAddress != ''">detail_address = #{detailAddress},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="operatingHours != null">operating_hours = #{operatingHours},</if>
            <if test="loadingCapacity != null">loading_capacity = #{loadingCapacity},</if>
            <if test="specialRequirements != null">special_requirements = #{specialRequirements},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTransportLoadingPointById" parameterType="Long">
        update transport_loading_point set is_deleted = 1, update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteTransportLoadingPointByIds" parameterType="String">
        update transport_loading_point set is_deleted = 1, update_time = sysdate() where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectLoadingPointByCode" resultMap="TransportLoadingPointResult">
        <include refid="selectTransportLoadingPointVo"/>
        where point_code = #{pointCode} and is_deleted = 0
    </select>

    <select id="checkPointCodeExists" resultType="int">
        select count(1) from transport_loading_point where point_code = #{pointCode} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="selectActiveLoadingPoints" resultMap="TransportLoadingPointResult">
        <include refid="selectTransportLoadingPointVo"/>
        where is_active = 1 and is_deleted = 0
    </select>

    <update id="updateLoadingPointStatus">
        update transport_loading_point set is_active = #{isActive}, update_by = #{updateBy}, update_time = sysdate() where id = #{id}
    </update>

    <select id="selectLoadingPointsByType" resultMap="TransportLoadingPointResult">
        <include refid="selectTransportLoadingPointVo"/>
        where point_type = #{pointType} and is_deleted = 0
    </select>

    <select id="selectLoadingPointsByRegion" resultMap="TransportLoadingPointResult">
        <include refid="selectTransportLoadingPointVo"/>
        <where>
            is_deleted = 0
            <if test="countryCode != null and countryCode != ''">and country_code = #{countryCode}</if>
            <if test="provinceCode != null and provinceCode != ''">and province_code = #{provinceCode}</if>
            <if test="cityCode != null and cityCode != ''">and city_code = #{cityCode}</if>
        </where>
    </select>

    <select id="selectLoadingPointOptions" parameterType="com.ruoyi.system.domain.transport.TransportLoadingPoint" resultMap="TransportLoadingPointResult">
        select id, point_name, point_code
        from transport_loading_point
        <where>
            is_deleted = 0
            <if test="pointName != null and pointName != ''"> and point_name like concat('%', #{pointName}, '%')</if>
            <if test="pointCode != null and pointCode != ''"> and point_code like concat('%', #{pointCode}, '%')</if>
        </where>
    </select>
</mapper>