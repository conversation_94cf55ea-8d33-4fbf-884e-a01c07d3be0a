<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportOrderMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportOrder" id="TransportOrderResult">
        <id property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="internalCode"    column="internal_code"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="consigneeId"    column="consignee_id"    />
        <result property="consigneeName"    column="consignee_name"    />
        <result property="consigneeCountryCode"    column="consignee_country_code"    />
        <result property="consigneeCountry"    column="consignee_country"    />
        <result property="consigneeProvinceCode"    column="consignee_province_code"    />
        <result property="consigneeProvince"    column="consignee_province"    />
        <result property="consigneeCityCode"    column="consignee_city_code"    />
        <result property="consigneeCity"    column="consignee_city"    />
        <result property="consigneeDistrictCode"    column="consignee_district_code"    />
        <result property="consigneeDistrict"    column="consignee_district"    />
        <result property="consigneeAddress"    column="consignee_address"    />
        <result property="consigneeContact"    column="consignee_contact"    />
        <result property="consigneePhone"    column="consignee_phone"    />
        <result property="loadingPointId"    column="loading_point_id"    />
        <result property="loadingPointName"    column="loading_point_name"    />
        <result property="loadingCountryCode"    column="loading_country_code"    />
        <result property="loadingCountry"    column="loading_country"    />
        <result property="loadingProvinceCode"    column="loading_province_code"    />
        <result property="loadingProvince"    column="loading_province"    />
        <result property="loadingCityCode"    column="loading_city_code"    />
        <result property="loadingCity"    column="loading_city"    />
        <result property="loadingDistrictCode"    column="loading_district_code"    />
        <result property="loadingDistrict"    column="loading_district"    />
        <result property="loadingAddress"    column="loading_address"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productQuantity"    column="product_quantity"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="transportDistance"    column="transport_distance"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="driverId"    column="driver_id"    />
        <result property="licensePlate"    column="license_plate"    />
        <result property="plannedLoadingTime"    column="planned_loading_time"    />
        <result property="actualLoadingTime"    column="actual_loading_time"    />
        <result property="plannedDeliveryTime"    column="planned_delivery_time"    />
        <result property="actualDeliveryTime"    column="actual_delivery_time"    />
        <result property="loadingCompletedTime"    column="loading_completed_time"    />
        <result property="departureTime"    column="departure_time"    />
        <result property="arrivalTime"    column="arrival_time"    />
        <result property="shippingCost"    column="shipping_cost"    />
        <result property="otherExpenses"    column="other_expenses"    />
        <result property="taxIncluded"    column="tax_included"    />
        <result property="deliveryRequirements"    column="delivery_requirements"    />
        <result property="specialInstructions"    column="special_instructions"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <collection property="details" ofType="com.ruoyi.system.domain.transport.TransportShippingRatesDetail"
                    select="com.ruoyi.system.mapper.transport.TransportShippingRatesDetailMapper.selectByOrderId" column="id"/>
    </resultMap>

    <sql id="selectTransportOrderVo">
        select id, order_no, internal_code, customer_id, customer_name, consignee_id, consignee_name, 
               consignee_country_code, consignee_country, consignee_province_code, consignee_province, consignee_city_code, consignee_city, 
               consignee_district_code, consignee_district, consignee_address, consignee_contact, consignee_phone, 
               loading_point_id, loading_point_name, loading_country_code, loading_country, loading_province_code, loading_province, 
               loading_city_code, loading_city, loading_district_code, loading_district, loading_address, 
               product_id, productName, product_quantity, total_volume, transport_distance, order_status, 
               vehicle_id, driver_id, license_plate, planned_loading_time, actual_loading_time, 
               planned_delivery_time, actual_delivery_time, loading_completed_time, departure_time, arrival_time, 
               shipping_cost, other_expenses, tax_included, delivery_requirements, special_instructions, 
               is_deleted, create_by, create_time, update_by, update_time, remark 
        from transport_order
    </sql>

    <select id="selectTransportOrderList" parameterType="com.ruoyi.system.domain.transport.TransportOrder" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        <where>
            is_deleted = 0
            <if test="orderNo != null  and orderNo != ''"> and order_no like concat('%', #{orderNo}, '%')</if>
            <if test="internalCode != null  and internalCode != ''"> and internal_code like concat('%', #{internalCode}, '%')</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="consigneeId != null "> and consignee_id = #{consigneeId}</if>
            <if test="consigneeName != null  and consigneeName != ''"> and consignee_name like concat('%', #{consigneeName}, '%')</if>
            <if test="loadingPointId != null "> and loading_point_id = #{loadingPointId}</if>
            <if test="loadingPointName != null  and loadingPointName != ''"> and loading_point_name like concat('%', #{loadingPointName}, '%')</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="orderStatus != null "> and order_status = #{orderStatus}</if>
            <if test="vehicleId != null "> and vehicle_id = #{vehicleId}</if>
            <if test="driverId != null "> and driver_id = #{driverId}</if>
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate like concat('%', #{licensePlate}, '%')</if>
        </where>
    </select>
    
    <select id="selectTransportOrderById" parameterType="Long" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        where id = #{id} and is_deleted = 0
    </select>
        
    <insert id="insertTransportOrder" parameterType="com.ruoyi.system.domain.transport.TransportOrder" useGeneratedKeys="true" keyProperty="id">
        insert into transport_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="internalCode != null and internalCode != ''">internal_code,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="consigneeId != null">consignee_id,</if>
            <if test="consigneeName != null and consigneeName != ''">consignee_name,</if>
            <if test="consigneeCountryCode != null">consignee_country_code,</if>
            <if test="consigneeCountry != null">consignee_country,</if>
            <if test="consigneeProvinceCode != null">consignee_province_code,</if>
            <if test="consigneeProvince != null">consignee_province,</if>
            <if test="consigneeCityCode != null">consignee_city_code,</if>
            <if test="consigneeCity != null">consignee_city,</if>
            <if test="consigneeDistrictCode != null">consignee_district_code,</if>
            <if test="consigneeDistrict != null">consignee_district,</if>
            <if test="consigneeAddress != null and consigneeAddress != ''">consignee_address,</if>
            <if test="consigneeContact != null">consignee_contact,</if>
            <if test="consigneePhone != null">consignee_phone,</if>
            <if test="loadingPointId != null">loading_point_id,</if>
            <if test="loadingPointName != null and loadingPointName != ''">loading_point_name,</if>
            <if test="loadingCountryCode != null">loading_country_code,</if>
            <if test="loadingCountry != null">loading_country,</if>
            <if test="loadingProvinceCode != null">loading_province_code,</if>
            <if test="loadingProvince != null">loading_province,</if>
            <if test="loadingCityCode != null">loading_city_code,</if>
            <if test="loadingCity != null">loading_city,</if>
            <if test="loadingDistrictCode != null">loading_district_code,</if>
            <if test="loadingDistrict != null">loading_district,</if>
            <if test="loadingAddress != null and loadingAddress != ''">loading_address,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="productQuantity != null">product_quantity,</if>
            <if test="totalVolume != null">total_volume,</if>
            <if test="transportDistance != null">transport_distance,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="driverId != null">driver_id,</if>
            <if test="licensePlate != null">license_plate,</if>
            <if test="plannedLoadingTime != null">planned_loading_time,</if>
            <if test="actualLoadingTime != null">actual_loading_time,</if>
            <if test="plannedDeliveryTime != null">planned_delivery_time,</if>
            <if test="actualDeliveryTime != null">actual_delivery_time,</if>
            <if test="loadingCompletedTime != null">loading_completed_time,</if>
            <if test="departureTime != null">departure_time,</if>
            <if test="arrivalTime != null">arrival_time,</if>
            <if test="shippingCost != null">shipping_cost,</if>
            <if test="otherExpenses != null">other_expenses,</if>
            <if test="taxIncluded != null">tax_included,</if>
            <if test="deliveryRequirements != null">delivery_requirements,</if>
            <if test="specialInstructions != null">special_instructions,</if>
            is_deleted,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="internalCode != null and internalCode != ''">#{internalCode},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="consigneeId != null">#{consigneeId},</if>
            <if test="consigneeName != null and consigneeName != ''">#{consigneeName},</if>
            <if test="consigneeCountryCode != null">#{consigneeCountryCode},</if>
            <if test="consigneeCountry != null">#{consigneeCountry},</if>
            <if test="consigneeProvinceCode != null">#{consigneeProvinceCode},</if>
            <if test="consigneeProvince != null">#{consigneeProvince},</if>
            <if test="consigneeCityCode != null">#{consigneeCityCode},</if>
            <if test="consigneeCity != null">#{consigneeCity},</if>
            <if test="consigneeDistrictCode != null">#{consigneeDistrictCode},</if>
            <if test="consigneeDistrict != null">#{consigneeDistrict},</if>
            <if test="consigneeAddress != null and consigneeAddress != ''">#{consigneeAddress},</if>
            <if test="consigneeContact != null">#{consigneeContact},</if>
            <if test="consigneePhone != null">#{consigneePhone},</if>
            <if test="loadingPointId != null">#{loadingPointId},</if>
            <if test="loadingPointName != null and loadingPointName != ''">#{loadingPointName},</if>
            <if test="loadingCountryCode != null">#{loadingCountryCode},</if>
            <if test="loadingCountry != null">#{loadingCountry},</if>
            <if test="loadingProvinceCode != null">#{loadingProvinceCode},</if>
            <if test="loadingProvince != null">#{loadingProvince},</if>
            <if test="loadingCityCode != null">#{loadingCityCode},</if>
            <if test="loadingCity != null">#{loadingCity},</if>
            <if test="loadingDistrictCode != null">#{loadingDistrictCode},</if>
            <if test="loadingDistrict != null">#{loadingDistrict},</if>
            <if test="loadingAddress != null and loadingAddress != ''">#{loadingAddress},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productQuantity != null">#{productQuantity},</if>
            <if test="totalVolume != null">#{totalVolume},</if>
            <if test="transportDistance != null">#{transportDistance},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="licensePlate != null">#{licensePlate},</if>
            <if test="plannedLoadingTime != null">#{plannedLoadingTime},</if>
            <if test="actualLoadingTime != null">#{actualLoadingTime},</if>
            <if test="plannedDeliveryTime != null">#{plannedDeliveryTime},</if>
            <if test="actualDeliveryTime != null">#{actualDeliveryTime},</if>
            <if test="loadingCompletedTime != null">#{loadingCompletedTime},</if>
            <if test="departureTime != null">#{departureTime},</if>
            <if test="arrivalTime != null">#{arrivalTime},</if>
            <if test="shippingCost != null">#{shippingCost},</if>
            <if test="otherExpenses != null">#{otherExpenses},</if>
            <if test="taxIncluded != null">#{taxIncluded},</if>
            <if test="deliveryRequirements != null">#{deliveryRequirements},</if>
            <if test="specialInstructions != null">#{specialInstructions},</if>
            0,
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate(),
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTransportOrder" parameterType="com.ruoyi.system.domain.transport.TransportOrder">
        update transport_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="internalCode != null and internalCode != ''">internal_code = #{internalCode},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="consigneeId != null">consignee_id = #{consigneeId},</if>
            <if test="consigneeName != null and consigneeName != ''">consignee_name = #{consigneeName},</if>
            <if test="consigneeCountryCode != null">consignee_country_code = #{consigneeCountryCode},</if>
            <if test="consigneeCountry != null">consignee_country = #{consigneeCountry},</if>
            <if test="consigneeProvinceCode != null">consignee_province_code = #{consigneeProvinceCode},</if>
            <if test="consigneeProvince != null">consignee_province = #{consigneeProvince},</if>
            <if test="consigneeCityCode != null">consignee_city_code = #{consigneeCityCode},</if>
            <if test="consigneeCity != null">consignee_city = #{consigneeCity},</if>
            <if test="consigneeDistrictCode != null">consignee_district_code = #{consigneeDistrictCode},</if>
            <if test="consigneeDistrict != null">consignee_district = #{consigneeDistrict},</if>
            <if test="consigneeAddress != null and consigneeAddress != ''">consignee_address = #{consigneeAddress},</if>
            <if test="consigneeContact != null">consignee_contact = #{consigneeContact},</if>
            <if test="consigneePhone != null">consignee_phone = #{consigneePhone},</if>
            <if test="loadingPointId != null">loading_point_id = #{loadingPointId},</if>
            <if test="loadingPointName != null and loadingPointName != ''">loading_point_name = #{loadingPointName},</if>
            <if test="loadingCountryCode != null">loading_country_code = #{loadingCountryCode},</if>
            <if test="loadingCountry != null">loading_country = #{loadingCountry},</if>
            <if test="loadingProvinceCode != null">loading_province_code = #{loadingProvinceCode},</if>
            <if test="loadingProvince != null">loading_province = #{loadingProvince},</if>
            <if test="loadingCityCode != null">loading_city_code = #{loadingCityCode},</if>
            <if test="loadingCity != null">loading_city = #{loadingCity},</if>
            <if test="loadingDistrictCode != null">loading_district_code = #{loadingDistrictCode},</if>
            <if test="loadingDistrict != null">loading_district = #{loadingDistrict},</if>
            <if test="loadingAddress != null and loadingAddress != ''">loading_address = #{loadingAddress},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productQuantity != null">product_quantity = #{productQuantity},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="transportDistance != null">transport_distance = #{transportDistance},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="driverId != null">driver_id = #{driverId},</if>
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
            <if test="plannedLoadingTime != null">planned_loading_time = #{plannedLoadingTime},</if>
            <if test="actualLoadingTime != null">actual_loading_time = #{actualLoadingTime},</if>
            <if test="plannedDeliveryTime != null">planned_delivery_time = #{plannedDeliveryTime},</if>
            <if test="actualDeliveryTime != null">actual_delivery_time = #{actualDeliveryTime},</if>
            <if test="loadingCompletedTime != null">loading_completed_time = #{loadingCompletedTime},</if>
            <if test="departureTime != null">departure_time = #{departureTime},</if>
            <if test="arrivalTime != null">arrival_time = #{arrivalTime},</if>
            <if test="shippingCost != null">shipping_cost = #{shippingCost},</if>
            <if test="otherExpenses != null">other_expenses = #{otherExpenses},</if>
            <if test="taxIncluded != null">tax_included = #{taxIncluded},</if>
            <if test="deliveryRequirements != null">delivery_requirements = #{deliveryRequirements},</if>
            <if test="specialInstructions != null">special_instructions = #{specialInstructions},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTransportOrderById" parameterType="Long">
        update transport_order set is_deleted = 1, update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteTransportOrderByIds" parameterType="String">
        update transport_order set is_deleted = 1, update_time = sysdate() where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getMaxOrderNoByDate" resultType="String">
        select max(order_no) from transport_order 
        where order_no like concat('YS', #{dateStr}, '%') and is_deleted = 0
    </select>

    <select id="getMaxInternalCodeByDate" resultType="String">
        select max(internal_code) from transport_order 
        where internal_code like concat('NB', #{dateStr}, '%') and is_deleted = 0
    </select>

    <update id="updateOrderStatus">
        update transport_order 
        set order_status = #{status}, update_by = #{updateBy}, update_time = sysdate()
        where id = #{id}
    </update>

    <update id="assignVehicleAndDriver">
        update transport_order 
        set vehicle_id = #{vehicleId}, driver_id = #{driverId}, 
            <if test="licensePlate != null and licensePlate != ''">license_plate = #{licensePlate},</if>
            order_status = 2, update_by = #{updateBy}, update_time = sysdate()
        where id = #{id}
    </update>

    <select id="selectOrdersByVehicleId" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        where vehicle_id = #{vehicleId} and is_deleted = 0
    </select>

    <select id="selectOrdersByDriverId" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        where driver_id = #{driverId} and is_deleted = 0
    </select>

    <select id="selectOrdersByCustomerId" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        where customer_id = #{customerId} and is_deleted = 0
    </select>

    <select id="selectOrderStatusStatistics" resultType="java.util.Map">
        select order_status, count(id) as order_count from transport_order 
        where is_deleted = 0 
        group by order_status
    </select>

    <select id="selectPendingOrders" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        where order_status = 1 and is_deleted = 0
    </select>

    <select id="selectTransportingOrders" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        where order_status in (3,4,5) and is_deleted = 0
    </select>

    <select id="selectTransportOrderByOrderNo" resultMap="TransportOrderResult">
        <include refid="selectTransportOrderVo"/>
        where order_no = #{orderNo} and is_deleted = 0
    </select>

    <select id="checkOrderNoExists" resultType="int">
        select count(1) from transport_order where order_no = #{orderNo} and is_deleted = 0
    </select>

    <select id="checkInternalCodeExists" resultType="int">
        select count(1) from transport_order where internal_code = #{internalCode} and is_deleted = 0
    </select>

    <update id="updateActualLoadingTime">
        update transport_order set actual_loading_time = #{actualLoadingTime}, update_time = sysdate() where id = #{id}
    </update>

    <update id="updateLoadingCompletedTime">
        update transport_order set loading_completed_time = #{loadingCompletedTime}, update_time = sysdate() where id = #{id}
    </update>

    <update id="updateDepartureTime">
        update transport_order set departure_time = #{departureTime}, update_time = sysdate() where id = #{id}
    </update>

    <update id="updateArrivalTime">
        update transport_order set arrival_time = #{arrivalTime}, update_time = sysdate() where id = #{id}
    </update>

    <update id="updateActualDeliveryTime">
        update transport_order set actual_delivery_time = #{actualDeliveryTime}, update_time = sysdate() where id = #{id}
    </update>

</mapper>