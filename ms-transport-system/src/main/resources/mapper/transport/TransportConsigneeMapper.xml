<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportConsigneeMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportConsignee" id="TransportConsigneeResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="consigneeName"    column="consignee_name"    />
        <result property="consigneeCode"    column="consignee_code"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="countryCode"    column="country_code"    />
        <result property="countryName"    column="country_name"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityCode"    column="city_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="detailAddress"    column="detail_address"    />
        <result property="consigneeType"    column="consignee_type"    />
        <result property="isActive"    column="is_active"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTransportConsigneeVo">
        select id, customer_id, consignee_name, consignee_code, contact_person, contact_phone, 
               country_code, country_name, province_code, province_name, city_code, city_name, 
               detail_address, consignee_type, is_active, is_deleted, 
               create_by, create_time, update_by, update_time, remark 
        from transport_consignee
    </sql>

    <select id="selectTransportConsigneeList" parameterType="com.ruoyi.system.domain.transport.TransportConsignee" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        <where>
            is_deleted = 0
            <if test="consigneeCode != null  and consigneeCode != ''"> and consignee_code = #{consigneeCode}</if>
            <if test="consigneeName != null  and consigneeName != ''"> and consignee_name like concat('%', #{consigneeName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="countryCode != null and countryCode != ''"> and country_code = #{countryCode}</if>
            <if test="provinceCode != null and provinceCode != ''"> and province_code = #{provinceCode}</if>
            <if test="cityCode != null and cityCode != ''"> and city_code = #{cityCode}</if>
            <if test="detailAddress != null and detailAddress != ''"> and detail_address like concat('%', #{detailAddress}, '%')</if>
            <if test="consigneeType != null"> and consignee_type = #{consigneeType}</if>
        </where>
    </select>
    
    <select id="selectTransportConsigneeById" parameterType="Long" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        where id = #{id} and is_deleted = 0
    </select>
        
    <insert id="insertTransportConsignee" parameterType="com.ruoyi.system.domain.transport.TransportConsignee" useGeneratedKeys="true" keyProperty="id">
        insert into transport_consignee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="consigneeName != null and consigneeName != ''">consignee_name,</if>
            <if test="consigneeCode != null and consigneeCode != ''">consignee_code,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="countryCode != null">country_code,</if>
            <if test="countryName != null">country_name,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="cityName != null">city_name,</if>
            <if test="detailAddress != null and detailAddress != ''">detail_address,</if>
            <if test="consigneeType != null">consignee_type,</if>
            <if test="isActive != null">is_active,</if>
            is_deleted,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="consigneeName != null and consigneeName != ''">#{consigneeName},</if>
            <if test="consigneeCode != null and consigneeCode != ''">#{consigneeCode},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="countryCode != null">#{countryCode},</if>
            <if test="countryName != null">#{countryName},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="detailAddress != null and detailAddress != ''">#{detailAddress},</if>
            <if test="consigneeType != null">#{consigneeType},</if>
            <if test="isActive != null">#{isActive},</if>
            0,
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate(),
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTransportConsignee" parameterType="com.ruoyi.system.domain.transport.TransportConsignee">
        update transport_consignee
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="consigneeName != null and consigneeName != ''">consignee_name = #{consigneeName},</if>
            <if test="consigneeCode != null and consigneeCode != ''">consignee_code = #{consigneeCode},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="countryName != null">country_name = #{countryName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="detailAddress != null and detailAddress != ''">detail_address = #{detailAddress},</if>
            <if test="consigneeType != null">consignee_type = #{consigneeType},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTransportConsigneeById" parameterType="Long">
        update transport_consignee set is_deleted = 1, update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteTransportConsigneeByIds" parameterType="String">
        update transport_consignee set is_deleted = 1, update_time = sysdate() where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectConsigneesByCustomerId" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        where customer_id = #{customerId} and is_deleted = 0
    </select>

    <select id="selectConsigneeByCode" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        where consignee_code = #{consigneeCode} and is_deleted = 0
    </select>

    <select id="checkConsigneeCodeExists" resultType="int">
        select count(1) from transport_consignee where consignee_code = #{consigneeCode} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="selectActiveConsignees" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        where is_active = 1 and is_deleted = 0
    </select>

    <update id="updateConsigneeStatus">
        update transport_consignee set is_active = #{isActive}, update_by = #{updateBy}, update_time = sysdate() where id = #{id}
    </update>

    <select id="selectConsigneesByType" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        where consignee_type = #{consigneeType} and is_deleted = 0
    </select>

    <select id="selectConsigneesByRegion" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        <where>
            is_deleted = 0
            <if test="countryCode != null and countryCode != ''">and country_code = #{countryCode}</if>
            <if test="provinceCode != null and provinceCode != ''">and province_code = #{provinceCode}</if>
            <if test="cityCode != null and cityCode != ''">and city_code = #{cityCode}</if>
        </where>
    </select>

    <select id="listConsigneeByCodes" resultMap="TransportConsigneeResult">
        <include refid="selectTransportConsigneeVo"/>
        where is_deleted = 0 and consignee_code in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

</mapper>