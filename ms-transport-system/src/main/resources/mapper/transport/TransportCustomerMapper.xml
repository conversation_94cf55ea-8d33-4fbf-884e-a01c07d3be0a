<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportCustomerMapper">

    <resultMap type="com.ruoyi.system.domain.transport.TransportCustomer" id="TransportCustomerResult">
        <result property="id"    column="id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerCode"    column="customer_code"    />
        <result property="companyType"    column="company_type"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="companyAddress"    column="company_address"    />
        <result property="customerStatus"    column="customer_status"    />
        <result property="creditRating"    column="credit_rating"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="isVip"    column="is_vip"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTransportCustomerVo">
        select id, customer_name, customer_code, company_type, contact_person, contact_phone, 
               contact_email, company_address, customer_status, credit_rating, payment_method, payment_days, 
               is_vip, is_deleted, create_by, create_time, update_by, update_time, remark 
        from transport_customer
    </sql>

    <select id="selectTransportCustomerList" parameterType="com.ruoyi.system.domain.transport.TransportCustomer" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        <where>
            is_deleted = 0
            <if test="customerCode != null  and customerCode != ''"> and customer_code = #{customerCode}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="creditRating != null  and creditRating != ''"> and credit_rating = #{creditRating}</if>
            <if test="paymentMethod != null "> and payment_method = #{paymentMethod}</if>
            <if test="customerStatus != null "> and customer_status = #{customerStatus}</if>
            <if test="companyType != null and companyType != ''"> and company_type = #{companyType}</if>
        </where>
    </select>
    
    <select id="selectTransportCustomerById" parameterType="Long" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <insert id="insertTransportCustomer" parameterType="com.ruoyi.system.domain.transport.TransportCustomer" useGeneratedKeys="true" keyProperty="id">
        insert into transport_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="customerCode != null and customerCode != ''">customer_code,</if>
            <if test="companyType != null">company_type,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="customerStatus != null">customer_status,</if>
            <if test="creditRating != null">credit_rating,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="paymentDays != null">payment_days,</if>
            <if test="isVip != null">is_vip,</if>
            is_deleted,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="customerCode != null and customerCode != ''">#{customerCode},</if>
            <if test="companyType != null">#{companyType},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="customerStatus != null">#{customerStatus},</if>
            <if test="creditRating != null">#{creditRating},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="paymentDays != null">#{paymentDays},</if>
            <if test="isVip != null">#{isVip},</if>
            0,
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate(),
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTransportCustomer" parameterType="com.ruoyi.system.domain.transport.TransportCustomer">
        update transport_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="customerCode != null and customerCode != ''">customer_code = #{customerCode},</if>
            <if test="companyType != null">company_type = #{companyType},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="customerStatus != null">customer_status = #{customerStatus},</if>
            <if test="creditRating != null">credit_rating = #{creditRating},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentDays != null">payment_days = #{paymentDays},</if>
            <if test="isVip != null">is_vip = #{isVip},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTransportCustomerById" parameterType="Long">
        update transport_customer set is_deleted = 1, update_time = sysdate() where id = #{id}
    </update>

    <update id="deleteTransportCustomerByIds" parameterType="String">
        update transport_customer set is_deleted = 1, update_time = sysdate() where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectCustomerByCode" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where customer_code = #{customerCode} and is_deleted = 0
    </select>

    <select id="listCustomerByCodes" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where customer_code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="checkCustomerCodeExists" resultType="int">
        select count(1) from transport_customer where customer_code = #{customerCode} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="checkCustomerNameExists" resultType="int">
        select count(1) from transport_customer where customer_name = #{customerName} and is_deleted = 0
        <if test="id != null">and id != #{id}</if>
    </select>

    <select id="selectActiveCustomers" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where customer_status = 1 and is_deleted = 0
    </select>

    <update id="updateCustomerStatus">
        update transport_customer set customer_status = #{status}, update_by = #{updateBy}, update_time = sysdate() where id = #{id}
    </update>

    <select id="selectCustomerStatusStatistics" resultType="java.util.Map">
        select customer_status as status, count(*) as count from transport_customer where is_deleted = 0 group by customer_status
    </select>

    <select id="selectCustomersByCreditRating" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where credit_rating = #{creditRating} and is_deleted = 0
    </select>

    <select id="selectCustomersByPaymentMethod" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where payment_method = #{paymentMethod} and is_deleted = 0
    </select>

    <select id="selectVipCustomers" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where is_vip = 1 and is_deleted = 0
    </select>

    <select id="selectCustomerDropdownList" resultType="com.ruoyi.system.domain.dto.TransportCustomerDropdownDto">
        select
            id,
            customer_name as customerName,
            customer_code as customerCode
        from
            transport_customer
        <where>
            is_deleted = 0 and customer_status = 1
            <if test="keyword != null and keyword != ''">
                and (customer_code like concat('%', #{keyword}, '%') or customer_name like concat('%', #{keyword}, '%'))
            </if>
        </where>
        order by customer_code
    </select>

    <select id="selectCustomersByIds" resultMap="TransportCustomerResult">
        <include refid="selectTransportCustomerVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
    </select>

</mapper>