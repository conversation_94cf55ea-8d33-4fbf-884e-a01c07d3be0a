<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportShippingRateDetailMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportShippingRateDetail" id="TransportShippingRateDetailResult">
        <result property="id"    column="id"    />
        <result property="rateId"    column="rate_id"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="otherExpenses"    column="other_expenses"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectTransportShippingRateDetailVo">
        select id, rate_id, vehicle_type, other_expenses, create_by, create_time, update_by, update_time, is_deleted from transport_shipping_rate_detail
    </sql>

    <select id="selectByRateId" parameterType="java.lang.Long" resultMap="TransportShippingRateDetailResult">
        <include refid="selectTransportShippingRateDetailVo"/>
        where rate_id = #{rateId} and is_deleted = 0
    </select>

    <insert id="insert" parameterType="com.ruoyi.system.domain.transport.TransportShippingRateDetail" useGeneratedKeys="true" keyProperty="id">
        insert into transport_shipping_rate_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rateId != null">rate_id,</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type,</if>
            <if test="otherExpenses != null">other_expenses,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rateId != null">#{rateId},</if>
            <if test="vehicleType != null and vehicleType != ''">#{vehicleType},</if>
            <if test="otherExpenses != null">#{otherExpenses},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <insert id="insertBatch">
        insert into transport_shipping_rate_detail (rate_id, vehicle_type, other_expenses, create_by) values
        <foreach collection="details" item="item" index="index" separator=",">
            (#{item.rateId}, #{item.vehicleType}, #{item.otherExpenses}, #{item.createBy})
        </foreach>
    </insert>

    <update id="update" parameterType="com.ruoyi.system.domain.transport.TransportShippingRateDetail">
        update transport_shipping_rate_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="rateId != null">rate_id = #{rateId},</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type = #{vehicleType},</if>
            <if test="otherExpenses != null">other_expenses = #{otherExpenses},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        update transport_shipping_rate_detail set is_deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteByRateId" parameterType="java.lang.Long">
        update transport_shipping_rate_detail set is_deleted = 1 where rate_id = #{rateId}
    </delete>

</mapper>