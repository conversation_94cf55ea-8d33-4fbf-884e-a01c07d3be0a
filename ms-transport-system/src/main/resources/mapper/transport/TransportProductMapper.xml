<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TransportProductMapper">
    
    <resultMap type="com.ruoyi.system.domain.TransportProduct" id="TransportProductResult">
        <result property="id"    column="ID"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="categoryName"    column="category_name"    />
        <result property="name"    column="name"    />
        <result property="netContent"    column="net_content"    />
        <result property="unit"    column="unit"    />
        <result property="price"    column="price"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectTransportProductVo">
        select ID, category_code, category_name, name, net_content, unit, price, create_by, create_time, update_by, update_time, is_deleted from transport_product
    </sql>

    <select id="selectTransportProductList" parameterType="com.ruoyi.system.domain.TransportProduct" resultMap="TransportProductResult">
        <include refid="selectTransportProductVo"/>
        <where>
            is_deleted = 0
            <if test="categoryCode != null  and categoryCode != ''">
                AND category_code = #{categoryCode}
            </if>
            <if test="categoryName != null  and categoryName != ''">
                AND category_name like concat('%', #{categoryName}, '%')
            </if>
            <if test="name != null  and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>
    
    <select id="selectTransportProductById" parameterType="Long" resultMap="TransportProductResult">
        <include refid="selectTransportProductVo"/>
        where ID = #{id} and is_deleted = 0
    </select>
        
    <insert id="insertTransportProduct" parameterType="com.ruoyi.system.domain.TransportProduct" useGeneratedKeys="true" keyProperty="id">
        insert into transport_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryCode != null and categoryCode != ''">
                category_code,
            </if>
            <if test="categoryName != null and categoryName != ''">
                category_name,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="netContent != null">
                net_content,
            </if>
            <if test="unit != null and unit != ''">
                unit,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            create_time,
            <if test="updateBy != null">
                update_by,
            </if>
            update_time,
            is_deleted,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryCode != null and categoryCode != ''">
                #{categoryCode},
            </if>
            <if test="categoryName != null and categoryName != ''">
                #{categoryName},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="netContent != null">
                #{netContent},
            </if>
            <if test="unit != null and unit != ''">
                #{unit},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            now(),
            <if test="updateBy != null">
                #{updateBy},
            </if>
            now(),
            0,
         </trim>
    </insert>

    <update id="updateTransportProduct" parameterType="com.ruoyi.system.domain.TransportProduct">
        update transport_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryCode != null and categoryCode != ''">
                category_code = #{categoryCode},
            </if>
            <if test="categoryName != null and categoryName != ''">
                category_name = #{categoryName},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="netContent != null">
                net_content = #{netContent},
            </if>
            <if test="unit != null and unit != ''">
                unit = #{unit},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            update_time = now(),
        </trim>
        where ID = #{id}
    </update>

    <update id="deleteTransportProductById" parameterType="Long">
        update transport_product set is_deleted = 1 where ID = #{id}
    </update>

    <update id="deleteTransportProductByIds" parameterType="String">
        update transport_product set is_deleted = 1 where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
