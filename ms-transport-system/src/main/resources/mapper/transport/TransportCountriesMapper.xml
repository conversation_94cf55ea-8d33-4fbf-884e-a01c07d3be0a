<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportCountriesMapper">

    <resultMap type="com.ruoyi.system.domain.transport.TransportCountries" id="BaseResultMap">
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTransportCountriesVo">
        select code, name, create_by, create_time, update_by, update_time
        from transport_countries
    </sql>

    <select id="selectTransportCountriesList" parameterType="com.ruoyi.system.domain.transport.TransportCountries"
            resultMap="BaseResultMap">
        <include refid="selectTransportCountriesVo"/>
        order by code
    </select>

    <select id="selectTransportCountriesByCode" parameterType="String" resultMap="BaseResultMap">
        <include refid="selectTransportCountriesVo"/>
        where code = #{code}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        <include refid="selectTransportCountriesVo"/>
        where is_deleted = 0
    </select>

</mapper>
