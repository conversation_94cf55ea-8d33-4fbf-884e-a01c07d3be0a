<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportShippingRateMapper">
    
    <resultMap type="com.ruoyi.system.domain.transport.TransportShippingRate" id="TransportShippingRateResult">
        <result property="id"    column="id"    />
        <result property="loadingPointId"    column="loading_point_id"    />
        <result property="loadingPointName"    column="loading_point_name"    />
        <result property="startLocation"    column="start_location"    />
        <result property="endLocation"    column="end_location"    />
        <result property="endCityCode"    column="end_city_code"    />
        <result property="startCityCode"    column="start_city_code"    />
        <result property="freightUnit"    column="freight_unit"    />
        <result property="taxIncluded"    column="tax_included"    />
        <result property="minimumVolume"    column="minimum_volume"    />
        <result property="internalCodes"    column="internal_codes"    />
        <result property="rateType"    column="rate_type"    />
        <result property="productType"    column="product_type"    />
        <result property="contractFreightUnit"    column="contract_freight_unit"    />
        <result property="contractTaxIncluded"    column="contract_tax_included"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectTransportShippingRateVo">
        select id, loading_point_id, loading_point_name, start_location, end_location, end_city_code, start_city_code, freight_unit, tax_included, minimum_volume, internal_codes, rate_type, product_type, contract_freight_unit, contract_tax_included, create_by, create_time, update_by, update_time, is_deleted from transport_shipping_rates
    </sql>

    <select id="selectTransportShippingRateList" parameterType="com.ruoyi.system.domain.transport.TransportShippingRate" resultMap="TransportShippingRateResult">
        <include refid="selectTransportShippingRateVo"/>
        <where>
            is_deleted = 0
            <if test="startLocation != null  and startLocation != ''">
                AND start_location like concat('%', #{startLocation}, '%')
            </if>
            <if test="endLocation != null  and endLocation != ''">
                AND end_location like concat('%', #{endLocation}, '%')
            </if>
            <if test="rateType != null ">
                AND rate_type = #{rateType}
            </if>
            <if test="productType != null and productType != ''">
                AND product_type = #{productType}
            </if>
        </where>
    </select>
    
    <select id="selectTransportShippingRateById" parameterType="Long" resultMap="TransportShippingRateResult">
        <include refid="selectTransportShippingRateVo"/>
        where id = #{id} and is_deleted = 0
    </select>

    <select id="listByIds" resultMap="TransportShippingRateResult">
        <include refid="selectTransportShippingRateVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="findByUniqueConstraint" resultMap="TransportShippingRateResult">
        <include refid="selectTransportShippingRateVo"/>
        <where>
            is_deleted = 0
            AND start_location = #{startLocation}
            AND end_location = #{endLocation}
            AND rate_type = #{rateType}
            <if test="internalCodes != null and internalCodes != ''">
                AND internal_codes = #{internalCodes}
            </if>
            <if test="internalCodes == null or internalCodes == ''">
                AND (internal_codes is null or internal_codes = '')
            </if>
        </where>
        limit 1
    </select>

    <select id="getRatesByRoute" resultMap="TransportShippingRateResult">
        <include refid="selectTransportShippingRateVo"/>
        where start_city_code = #{startCityCode} and end_city_code = #{endCityCode} and is_deleted = 0
    </select>

    <select id="findApplicableRate" resultMap="TransportShippingRateResult">
        <include refid="selectTransportShippingRateVo"/>
        <where>
            is_deleted = 0
            AND start_location = #{startLocation}
            AND end_location = #{endLocation}
            AND product_type = #{productType}
            AND (internal_codes = #{internalCodes} OR internal_codes IS NULL OR internal_codes = '')
        </where>
        ORDER BY rate_type DESC, create_time DESC
        LIMIT 1
    </select>

    <select id="findSpecificRate" resultMap="TransportShippingRateResult">
        SELECT tsr.*
        FROM transport_shipping_rates tsr
                 INNER JOIN transport_consignee tc ON FIND_IN_SET(tc.consignee_code, tsr.internal_codes) > 0
        WHERE tsr.is_deleted = 0
          AND tsr.rate_type = 1
          AND tsr.loading_point_id = #{loadingPointId}
          AND tc.id = #{consigneeId}
        ORDER BY tsr.create_time DESC
        LIMIT 1
    </select>

    <select id="findDefaultRate" resultMap="TransportShippingRateResult">
        <include refid="selectTransportShippingRateVo"/>
        WHERE is_deleted = 0
          AND rate_type = 0
          AND loading_point_id = #{loadingPointId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>
        
    <insert id="insertTransportShippingRate" parameterType="com.ruoyi.system.domain.transport.TransportShippingRate" useGeneratedKeys="true" keyProperty="id">
        insert into transport_shipping_rates
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loadingPointId != null">loading_point_id,</if>
            <if test="loadingPointName != null and loadingPointName != ''">loading_point_name,</if>
            <if test="startLocation != null and startLocation != ''">start_location,</if>
            <if test="endLocation != null and endLocation != ''">end_location,</if>
            <if test="endCityCode != null and endCityCode != ''">end_city_code,</if>
            <if test="startCityCode != null and startCityCode != ''">start_city_code,</if>
            <if test="freightUnit != null">freight_unit,</if>
            <if test="taxIncluded != null">tax_included,</if>
            <if test="minimumVolume != null">minimum_volume,</if>
            <if test="internalCodes != null">internal_codes,</if>
            <if test="rateType != null">rate_type,</if>
            <if test="productType != null">product_type,</if>
            <if test="contractFreightUnit != null">contract_freight_unit,</if>
            <if test="contractTaxIncluded != null">contract_tax_included,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            is_deleted
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loadingPointId != null">#{loadingPointId},</if>
            <if test="loadingPointName != null and loadingPointName != ''">#{loadingPointName},</if>
            <if test="startLocation != null and startLocation != ''">#{startLocation},</if>
            <if test="endLocation != null and endLocation != ''">#{endLocation},</if>
            <if test="endCityCode != null and endCityCode != ''">#{endCityCode},</if>
            <if test="startCityCode != null and startCityCode != ''">#{startCityCode},</if>
            <if test="freightUnit != null">#{freightUnit},</if>
            <if test="taxIncluded != null">#{taxIncluded},</if>
            <if test="minimumVolume != null">#{minimumVolume},</if>
            <if test="internalCodes != null">#{internalCodes},</if>
            <if test="rateType != null">#{rateType},</if>
            <if test="productType != null">#{productType},</if>
            <if test="contractFreightUnit != null">#{contractFreightUnit},</if>
            <if test="contractTaxIncluded != null">#{contractTaxIncluded},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            0
         </trim>
    </insert>

    <update id="updateTransportShippingRate" parameterType="com.ruoyi.system.domain.transport.TransportShippingRate">
        update transport_shipping_rates
        <trim prefix="SET" suffixOverrides=",">
            <if test="loadingPointId != null">loading_point_id = #{loadingPointId},</if>
            <if test="loadingPointName != null and loadingPointName != ''">loading_point_name = #{loadingPointName},</if>
            <if test="startLocation != null and startLocation != ''">start_location = #{startLocation},</if>
            <if test="endLocation != null and endLocation != ''">end_location = #{endLocation},</if>
            <if test="endCityCode != null and endCityCode != ''">end_city_code = #{endCityCode},</if>
            <if test="startCityCode != null and startCityCode != ''">start_city_code = #{startCityCode},</if>
            <if test="freightUnit != null">freight_unit = #{freightUnit},</if>
            <if test="taxIncluded != null">tax_included = #{taxIncluded},</if>
            <if test="minimumVolume != null">minimum_volume = #{minimumVolume},</if>
            <if test="internalCodes != null">internal_codes = #{internalCodes},</if>
            <if test="rateType != null">rate_type = #{rateType},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="contractFreightUnit != null">contract_freight_unit = #{contractFreightUnit},</if>
            <if test="contractTaxIncluded != null">contract_tax_included = #{contractTaxIncluded},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="updateRate" parameterType="com.ruoyi.system.domain.transport.TransportShippingRate">
        update transport_shipping_rates
        set freight_unit = #{freightUnit},
            tax_included = #{taxIncluded},
            minimum_volume = #{minimumVolume},
            contract_freight_unit = #{contractFreightUnit},
            contract_tax_included = #{contractTaxIncluded},
            update_time = sysdate()
        where id = #{id}
    </update>

    <update id="deleteTransportShippingRateById" parameterType="Long">
        update transport_shipping_rates set is_deleted = 1 where id = #{id}
    </update>

    <update id="deleteTransportShippingRateByIds" parameterType="String">
        update transport_shipping_rates set is_deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
