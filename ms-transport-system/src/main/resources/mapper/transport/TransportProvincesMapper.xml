<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.transport.TransportProvincesMapper">

    <resultMap type="com.ruoyi.system.domain.transport.TransportProvinces" id="TransportProvincesResult">
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="countryCode" column="country_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTransportProvincesVo">
        select code, name, country_code, create_by, create_time, update_by, update_time
        from transport_provinces
    </sql>

    <select id="selectTransportProvincesList" parameterType="String" resultMap="TransportProvincesResult">
        <include refid="selectTransportProvincesVo"/>
        <where>
            <if test="countryCode != null and countryCode != ''">
                and country_code = #{countryCode}
            </if>
        </where>
        order by code
    </select>

    <select id="selectTransportProvincesByCode" parameterType="String" resultMap="TransportProvincesResult">
        <include refid="selectTransportProvincesVo"/>
        where code = #{code}
    </select>


    <select id="selectList" resultMap="TransportProvincesResult">
        <include refid="selectTransportProvincesVo"/>
        where is_deleted = 0
    </select>

</mapper>
