package com.ruoyi.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    PENDING_ASSIGNMENT(1, "待指派"),
    ASSIGNED(2, "已指派"),
    TO_BE_LOADED(3, "前往装货"),
    LOADING(4, "装货中"),
    IN_TRANSIT(5, "运输中"),
    DELIVERED(6, "已送达"),
    RECONCILED(7, "已对账");

    private final Integer code;
    private final String description;

    public static OrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStatusEnum value : OrderStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
