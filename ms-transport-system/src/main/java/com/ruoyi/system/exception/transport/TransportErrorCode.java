package com.ruoyi.system.exception.transport;

/**
 * 运输管理系统错误码枚举
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public enum TransportErrorCode {
    
    // 通用错误 (1000-1099)
    SYSTEM_ERROR("1000", "系统错误"),
    PARAM_ERROR("1001", "参数错误"),
    DATA_NOT_FOUND("1002", "数据不存在"),
    DATA_EXISTS("1003", "数据已存在"),
    OPERATION_FAILED("1004", "操作失败"),
    
    // 运输单错误 (1100-1199)
    ORDER_NOT_FOUND("1100", "运输单不存在"),
    ORDER_STATUS_ERROR("1101", "运输单状态不允许该操作"),
    ORDER_NO_EXISTS("1102", "运输单号已存在"),
    INTERNAL_CODE_EXISTS("1103", "内部编号已存在"),
    ORDER_ASSIGNED("1104", "运输单已指派"),
    ORDER_NOT_ASSIGNED("1105", "运输单未指派"),
    ORDER_COMPLETED("1106", "运输单已完成"),
    
    // 车辆错误 (1200-1299)
    VEHICLE_NOT_FOUND("1200", "车辆不存在"),
    VEHICLE_NOT_AVAILABLE("1201", "车辆不可用"),
    LICENSE_PLATE_EXISTS("1202", "车牌号已存在"),
    VEHICLE_IN_USE("1203", "车辆正在使用中"),
    VEHICLE_MAINTENANCE("1204", "车辆正在维修中"),
    VEHICLE_SCRAPPED("1205", "车辆已报废"),
    
    // 司机错误 (1300-1399)
    DRIVER_NOT_FOUND("1300", "司机不存在"),
    DRIVER_NOT_AVAILABLE("1301", "司机不可用"),
    DRIVER_PHONE_EXISTS("1302", "手机号已存在"),
    ID_CARD_EXISTS("1303", "身份证号已存在"),
    DRIVER_ON_VACATION("1304", "司机正在休假"),
    DRIVER_RESIGNED("1305", "司机已离职"),
    DRIVER_IN_TASK("1306", "司机正在执行任务"),
    
    // 客户错误 (1400-1499)
    CUSTOMER_NOT_FOUND("1400", "客户不存在"),
    CUSTOMER_CODE_EXISTS("1401", "客户编码已存在"),
    CUSTOMER_NAME_EXISTS("1402", "客户名称已存在"),
    CUSTOMER_SUSPENDED("1403", "客户已暂停合作"),
    CUSTOMER_TERMINATED("1404", "客户已终止合作"),
    
    // 收货方错误 (1500-1599)
    CONSIGNEE_NOT_FOUND("1500", "收货方不存在"),
    CONSIGNEE_CODE_EXISTS("1501", "收货方编码已存在"),
    CONSIGNEE_DISABLED("1502", "收货方已停用"),
    
    // 装货点错误 (1600-1699)
    LOADING_POINT_NOT_FOUND("1600", "装货点不存在"),
    LOADING_POINT_CODE_EXISTS("1601", "装货点编码已存在"),
    LOADING_POINT_DISABLED("1602", "装货点已停用"),
    
    // 运费计算错误 (1700-1799)
    PRICING_RULE_NOT_FOUND("1700", "未找到适用的计费规则"),
    PRICING_CALCULATION_ERROR("1701", "运费计算失败"),
    PRICING_RULE_EXISTS("1702", "计费规则已存在"),
    PRICING_RULE_EXPIRED("1703", "计费规则已过期"),
    
    // 状态流转错误 (1800-1899)
    STATUS_TRANSITION_ERROR("1800", "状态流转不合法"),
    STATUS_NOT_ALLOWED("1801", "当前状态不允许该操作"),
    STATUS_ALREADY_CHANGED("1802", "状态已变更"),
    
    // 权限错误 (1900-1999)
    PERMISSION_DENIED("1900", "权限不足"),
    USER_NOT_LOGIN("1901", "用户未登录"),
    TOKEN_EXPIRED("1902", "登录已过期");
    
    private final String code;
    private final String message;
    
    TransportErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据错误码获取错误信息
     */
    public static String getMessageByCode(String code) {
        for (TransportErrorCode errorCode : values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode.getMessage();
            }
        }
        return "未知错误";
    }
    
    /**
     * 创建业务异常
     */
    public TransportBusinessException createException() {
        return new TransportBusinessException(this.code, this.message);
    }
    
    /**
     * 创建业务异常（带自定义消息）
     */
    public TransportBusinessException createException(String customMessage) {
        return new TransportBusinessException(this.code, customMessage);
    }
}
