package com.ruoyi.system.exception.transport;

import com.ruoyi.common.utils.StringUtils;

/**
 * 运输管理异常工具类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class TransportExceptionUtils {
    
    /**
     * 断言对象不为空，为空则抛出异常
     */
    public static void assertNotNull(Object obj, String message) {
        if (obj == null) {
            throw new TransportBusinessException(message);
        }
    }
    
    /**
     * 断言对象不为空，为空则抛出指定错误码异常
     */
    public static void assertNotNull(Object obj, TransportErrorCode errorCode) {
        if (obj == null) {
            throw errorCode.createException();
        }
    }

    /**
     * 断言对象不为空，为空则抛出指定错误码异常（带自定义消息）
     */
    public static void assertNotNull(Object obj, TransportErrorCode errorCode, String customMessage) {
        if (obj == null) {
            throw errorCode.createException(customMessage);
        }
    }
    
    /**
     * 断言字符串不为空，为空则抛出异常
     */
    public static void assertNotEmpty(String str, String message) {
        if (StringUtils.isEmpty(str)) {
            throw new TransportBusinessException(message);
        }
    }
    
    /**
     * 断言字符串不为空，为空则抛出指定错误码异常
     */
    public static void assertNotEmpty(String str, TransportErrorCode errorCode) {
        if (StringUtils.isEmpty(str)) {
            throw errorCode.createException();
        }
    }
    
    /**
     * 断言条件为真，为假则抛出异常
     */
    public static void assertTrue(boolean condition, String message) {
        if (!condition) {
            throw new TransportBusinessException(message);
        }
    }
    
    /**
     * 断言条件为真，为假则抛出指定错误码异常
     */
    public static void assertTrue(boolean condition, TransportErrorCode errorCode) {
        if (!condition) {
            throw errorCode.createException();
        }
    }

    /**
     * 断言条件为真，为假则抛出指定错误码异常（带自定义消息）
     */
    public static void assertTrue(boolean condition, TransportErrorCode errorCode, String customMessage) {
        if (!condition) {
            throw errorCode.createException(customMessage);
        }
    }
    
    /**
     * 断言条件为假，为真则抛出异常
     */
    public static void assertFalse(boolean condition, String message) {
        if (condition) {
            throw new TransportBusinessException(message);
        }
    }
    
    /**
     * 断言条件为假，为真则抛出指定错误码异常
     */
    public static void assertFalse(boolean condition, TransportErrorCode errorCode) {
        if (condition) {
            throw errorCode.createException();
        }
    }
    
    /**
     * 断言两个对象相等，不相等则抛出异常
     */
    public static void assertEquals(Object expected, Object actual, String message) {
        if (expected == null && actual == null) {
            return;
        }
        if (expected == null || !expected.equals(actual)) {
            throw new TransportBusinessException(message);
        }
    }
    
    /**
     * 断言两个对象相等，不相等则抛出指定错误码异常
     */
    public static void assertEquals(Object expected, Object actual, TransportErrorCode errorCode) {
        if (expected == null && actual == null) {
            return;
        }
        if (expected == null || !expected.equals(actual)) {
            throw errorCode.createException();
        }
    }
    
    /**
     * 抛出运输单异常
     */
    public static void throwOrderException(String message) {
        throw new TransportBusinessException.OrderException(message);
    }
    
    /**
     * 抛出车辆异常
     */
    public static void throwVehicleException(String message) {
        throw new TransportBusinessException.VehicleException(message);
    }
    
    /**
     * 抛出司机异常
     */
    public static void throwDriverException(String message) {
        throw new TransportBusinessException.DriverException(message);
    }
    
    /**
     * 抛出客户异常
     */
    public static void throwCustomerException(String message) {
        throw new TransportBusinessException.CustomerException(message);
    }
    
    /**
     * 抛出状态异常
     */
    public static void throwStatusException(String message) {
        throw new TransportBusinessException.StatusException(message);
    }
    
    /**
     * 抛出运费计算异常
     */
    public static void throwPricingException(String message) {
        throw new TransportBusinessException.PricingException(message);
    }
    
    /**
     * 抛出数据验证异常
     */
    public static void throwValidationException(String message) {
        throw new TransportBusinessException.ValidationException(message);
    }
    
    /**
     * 检查运输单是否存在
     */
    public static void checkOrderExists(Object order) {
        assertNotNull(order, TransportErrorCode.ORDER_NOT_FOUND);
    }
    
    /**
     * 检查车辆是否存在
     */
    public static void checkVehicleExists(Object vehicle) {
        assertNotNull(vehicle, TransportErrorCode.VEHICLE_NOT_FOUND);
    }
    
    /**
     * 检查司机是否存在
     */
    public static void checkDriverExists(Object driver) {
        assertNotNull(driver, TransportErrorCode.DRIVER_NOT_FOUND);
    }
    
    /**
     * 检查客户是否存在
     */
    public static void checkCustomerExists(Object customer) {
        assertNotNull(customer, TransportErrorCode.CUSTOMER_NOT_FOUND);
    }
    
    /**
     * 检查车辆是否可用
     */
    public static void checkVehicleAvailable(Integer vehicleStatus) {
        assertTrue(vehicleStatus != null && vehicleStatus == 1, TransportErrorCode.VEHICLE_NOT_AVAILABLE);
    }
    
    /**
     * 检查司机是否可用
     */
    public static void checkDriverAvailable(Integer driverStatus) {
        assertTrue(driverStatus != null && driverStatus == 1, TransportErrorCode.DRIVER_NOT_AVAILABLE);
    }
    
    /**
     * 检查运输单状态是否允许操作
     */
    public static void checkOrderStatusAllowed(Integer currentStatus, Integer targetStatus) {
        // 这里可以根据业务规则检查状态流转是否合法
        if (currentStatus == null || targetStatus == null) {
            throw TransportErrorCode.STATUS_TRANSITION_ERROR.createException();
        }
        
        // 简单的状态流转检查
        if (targetStatus <= currentStatus) {
            throw TransportErrorCode.STATUS_TRANSITION_ERROR.createException("不能回退到之前的状态");
        }
        
        if (targetStatus - currentStatus > 1) {
            throw TransportErrorCode.STATUS_TRANSITION_ERROR.createException("不能跳跃状态");
        }
    }
}
