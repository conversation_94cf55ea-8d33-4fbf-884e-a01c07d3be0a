package com.ruoyi.system.exception.transport;

/**
 * 运输管理业务异常
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class TransportBusinessException extends TransportException {
    
    private static final long serialVersionUID = 1L;
    
    public TransportBusinessException(String message) {
        super(message);
    }
    
    public TransportBusinessException(String code, String message) {
        super(code, message);
    }
    
    public TransportBusinessException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public TransportBusinessException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
    
    /**
     * 运输单相关异常
     */
    public static class OrderException extends TransportBusinessException {
        public OrderException(String message) {
            super("ORDER_ERROR", message);
        }
    }
    
    /**
     * 车辆相关异常
     */
    public static class VehicleException extends TransportBusinessException {
        public VehicleException(String message) {
            super("VEHICLE_ERROR", message);
        }
    }
    
    /**
     * 司机相关异常
     */
    public static class DriverException extends TransportBusinessException {
        public DriverException(String message) {
            super("DRIVER_ERROR", message);
        }
    }
    
    /**
     * 客户相关异常
     */
    public static class CustomerException extends TransportBusinessException {
        public CustomerException(String message) {
            super("CUSTOMER_ERROR", message);
        }
    }
    
    /**
     * 状态流转异常
     */
    public static class StatusException extends TransportBusinessException {
        public StatusException(String message) {
            super("STATUS_ERROR", message);
        }
    }
    
    /**
     * 运费计算异常
     */
    public static class PricingException extends TransportBusinessException {
        public PricingException(String message) {
            super("PRICING_ERROR", message);
        }
    }
    
    /**
     * 数据验证异常
     */
    public static class ValidationException extends TransportBusinessException {
        public ValidationException(String message) {
            super("VALIDATION_ERROR", message);
        }
    }
}
