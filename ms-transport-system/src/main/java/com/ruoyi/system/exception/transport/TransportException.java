package com.ruoyi.system.exception.transport;

/**
 * 运输管理系统自定义异常
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class TransportException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private String code;
    
    /**
     * 错误信息
     */
    private String message;
    
    public TransportException() {
        super();
    }
    
    public TransportException(String message) {
        super(message);
        this.message = message;
    }
    
    public TransportException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public TransportException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
    }
    
    public TransportException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
}
