package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class InStockWarehouseVO {

    @ApiModelProperty(value = "仓库的唯一标识符")
    private Long warehouseId;
    @ApiModelProperty(value = "仓库的名称")
    private String warehouseName;
    @ApiModelProperty(value = "仓库城市编码")
    private String warehouseCityCode;
    @ApiModelProperty(value = "仓库商品列表信息")
    private Set<InStockProductVO> productList;

}
