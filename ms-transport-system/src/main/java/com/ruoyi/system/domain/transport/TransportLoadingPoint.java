package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 装货点信息表 transport_loading_point
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("装货点信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportLoadingPoint extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("装货点ID")
    private Long id;

    /** 装货点名称 */
    @Excel(name = "装货点名称")
    @ApiModelProperty("装货点名称")
    @NotBlank(message = "装货点名称不能为空")
    private String pointName;

    /** 装货点编码 */
    @Excel(name = "装货点编码")
    @ApiModelProperty("装货点编码")
    private String pointCode;

    /** 装货点类型:1-港口,2-油库,3-炼厂,4-其他 */
    @Excel(name = "装货点类型", readConverterExp = "1=港口,2=油库,3=炼厂,4=其他")
    @ApiModelProperty("装货点类型:1-港口,2-油库,3-炼厂,4-其他")
    @NotNull(message = "装货点类型不能为空")
    private Integer pointType;

    /** 国家编码 */
    @ApiModelProperty("国家编码")
    private String countryCode;

    /** 国家名称 */
    @ApiModelProperty("国家名称")
    private String countryName;

    /** 省份编码 */
    @ApiModelProperty("省份编码")
    private String provinceCode;

    /** 省份名称 */
    @ApiModelProperty("省份名称")
    private String provinceName;

    /** 城市编码 */
    @ApiModelProperty("城市编码")
    private String cityCode;

    /** 城市名称 */
    @ApiModelProperty("城市名称")
    private String cityName;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @ApiModelProperty("详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String detailAddress;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /** 营业时间 */
    @Excel(name = "营业时间")
    @ApiModelProperty("营业时间")
    private String operatingHours;

    /** 装货能力 */
    @Excel(name = "装货能力")
    @ApiModelProperty("装货能力")
    private String loadingCapacity;

    /** 特殊要求 */
    @Excel(name = "特殊要求")
    @ApiModelProperty("特殊要求")
    private String specialRequirements;

    /** 是否启用:1-启用,0-停用 */
    @Excel(name = "是否启用", readConverterExp = "1=启用,0=停用")
    @ApiModelProperty("是否启用:1-启用,0-停用")
    private Integer isActive;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 扩展字段
    /** 装货点类型文本 */
    @ApiModelProperty("装货点类型文本")
    private String pointTypeText;

    /** 完整地址 */
    @ApiModelProperty("完整地址")
    private String fullAddress;

    /** 是否启用文本 */
    @ApiModelProperty("是否启用文本")
    private String isActiveText;

    public String getPointTypeText() {
        if (pointType == null) {
            return "";
        }
        switch (pointType) {
            case 1:
                return "港口";
            case 2:
                return "油库";
            case 3:
                return "炼厂";
            case 4:
                return "其他";
            default:
                return "未知";
        }
    }

    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (countryName != null) sb.append(countryName);
        if (provinceName != null) sb.append(provinceName);
        if (cityName != null) sb.append(cityName);
        if (detailAddress != null) sb.append(detailAddress);
        return sb.toString();
    }

    public String getIsActiveText() {
        if (isActive == null) {
            return "";
        }
        return isActive == 1 ? "启用" : "停用";
    }
}
