package com.ruoyi.system.domain.transport;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 仓库信息对象 transport_warehouse
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("仓库信息")
public class TransportWarehouse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("唯一标识ID")
    private Long id;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    /** 仓库编码 */
    @Excel(name = "仓库编码")
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    /** 仓库地址 */
    @Excel(name = "仓库地址")
    @ApiModelProperty("仓库地址")
    private String warehouseAddress;

    /** 仓库类型:1-油库,2-中转库,3-临时库 */
    @Excel(name = "仓库类型", readConverterExp = "1=油库,2=中转库,3=临时库")
    @ApiModelProperty("仓库类型:1-油库,2-中转库,3-临时库")
    private Integer warehouseType;

    /** 仓库状态:1-正常,2-维护中,3-停用 */
    @Excel(name = "仓库状态", readConverterExp = "1=正常,2=维护中,3=停用")
    @ApiModelProperty("仓库状态:1-正常,2=维护中,3-停用")
    private Integer warehouseStatus;

    /** 总存储容量(升) */
    @Excel(name = "总存储容量(升)")
    @ApiModelProperty("总存储容量(升)")
    private BigDecimal storageCapacity;

    /** 仓库管理员 */
    @Excel(name = "仓库管理员")
    @ApiModelProperty("仓库管理员")
    private String managerName;

    /** 管理员电话 */
    @Excel(name = "管理员电话")
    @ApiModelProperty("管理员电话")
    private String managerPhone;

    /** 省份编码 */
    @Excel(name = "省份编码")
    @ApiModelProperty("省份编码")
    private String provinceCode;

    /** 省份名称 */
    @Excel(name = "省份名称")
    @ApiModelProperty("省份名称")
    private String provinceName;

    /** 城市编码 */
    @Excel(name = "城市编码")
    @ApiModelProperty("城市编码")
    private String cityCode;

    /** 城市名称 */
    @Excel(name = "城市名称")
    @ApiModelProperty("城市名称")
    private String cityName;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setWarehouseName(String warehouseName) 
    {
        this.warehouseName = warehouseName;
    }

    public String getWarehouseName() 
    {
        return warehouseName;
    }
    public void setWarehouseCode(String warehouseCode) 
    {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseCode() 
    {
        return warehouseCode;
    }
    public void setWarehouseAddress(String warehouseAddress) 
    {
        this.warehouseAddress = warehouseAddress;
    }

    public String getWarehouseAddress() 
    {
        return warehouseAddress;
    }
    public void setWarehouseType(Integer warehouseType) 
    {
        this.warehouseType = warehouseType;
    }

    public Integer getWarehouseType() 
    {
        return warehouseType;
    }
    public void setWarehouseStatus(Integer warehouseStatus) 
    {
        this.warehouseStatus = warehouseStatus;
    }

    public Integer getWarehouseStatus() 
    {
        return warehouseStatus;
    }
    public void setStorageCapacity(BigDecimal storageCapacity) 
    {
        this.storageCapacity = storageCapacity;
    }

    public BigDecimal getStorageCapacity() 
    {
        return storageCapacity;
    }
    public void setManagerName(String managerName) 
    {
        this.managerName = managerName;
    }

    public String getManagerName() 
    {
        return managerName;
    }
    public void setManagerPhone(String managerPhone) 
    {
        this.managerPhone = managerPhone;
    }

    public String getManagerPhone() 
    {
        return managerPhone;
    }
    public void setProvinceCode(String provinceCode) 
    {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCode() 
    {
        return provinceCode;
    }
    public void setProvinceName(String provinceName) 
    {
        this.provinceName = provinceName;
    }

    public String getProvinceName() 
    {
        return provinceName;
    }
    public void setCityCode(String cityCode) 
    {
        this.cityCode = cityCode;
    }

    public String getCityCode() 
    {
        return cityCode;
    }
    public void setCityName(String cityName) 
    {
        this.cityName = cityName;
    }

    public String getCityName() 
    {
        return cityName;
    }
    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("warehouseName", getWarehouseName())
            .append("warehouseCode", getWarehouseCode())
            .append("warehouseAddress", getWarehouseAddress())
            .append("warehouseType", getWarehouseType())
            .append("warehouseStatus", getWarehouseStatus())
            .append("storageCapacity", getStorageCapacity())
            .append("managerName", getManagerName())
            .append("managerPhone", getManagerPhone())
            .append("provinceCode", getProvinceCode())
            .append("provinceName", getProvinceName())
            .append("cityCode", getCityCode())
            .append("cityName", getCityName())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
