package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import java.math.BigDecimal;

/**
 * 油品商品表对象 transport_product
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
public class TransportProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键ID */
    private Long id;

    /** 归属类目编号 */
    @Excel(name = "归属类目编号")
    private String categoryCode;

    /** 归属类目名称 */
    @Excel(name = "归属类目名称")
    private String categoryName;

    /** 品名，品的名称 */
    @Excel(name = "品名，品的名称")
    private String name;

    /** 净含量/体积，油品的体积或重量值 */
    @Excel(name = "净含量/体积，油品的体积或重量值")
    private BigDecimal netContent;

    /** 单位，通常以升(L)或千克(kg)为单位 */
    @Excel(name = "单位，通常以升(L)或千克(kg)为单位")
    private String unit;

    /** 价格，油品的销售单价 */
    @Excel(name = "价格，油品的销售单价")
    private BigDecimal price;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    private Integer isDeleted;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCategoryCode(String categoryCode)
    {
        this.categoryCode = categoryCode;
    }

    public String getCategoryCode()
    {
        return categoryCode;
    }
    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName()
    {
        return categoryName;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setNetContent(BigDecimal netContent)
    {
        this.netContent = netContent;
    }

    public BigDecimal getNetContent()
    {
        return netContent;
    }
    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    public String getUnit()
    {
        return unit;
    }
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public BigDecimal getPrice()
    {
        return price;
    }
    public void setIsDeleted(Integer isDeleted)
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted()
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("categoryCode", getCategoryCode())
            .append("categoryName", getCategoryName())
            .append("name", getName())
            .append("netContent", getNetContent())
            .append("unit", getUnit())
            .append("price", getPrice())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
