package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ExportOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 订单是否报税，0表示否，1表示是
    private Integer isTaxReported;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 收货时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;

    // 供应商名称
    private String supplierName;

    // 订单编号，系统规则生成
    private String orderNo;

    // 客户名称
    private String customerName;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 商品备注
     */
    private String merDesc;

    // 客户备注，客户在订单中留下的特殊需求或注意事项
    private String customerRemarks;

    // 内部备注，处理订单过程中，内部人员添加的额外信息或提醒
    private String internalRemarks;

    // 主营业务-刨除折扣的原始订单金额
    private BigDecimal originalOrderAmount;

    // 详细地址
    private String address;

    // 订单折扣金额（分）
    private BigDecimal discount;

    // 订单单位折扣金额
    private BigDecimal unitDiscount;

    // 成本（分）
    private BigDecimal cost;

    private String carrier; // 运输公司

    private BigDecimal shippingCost; // 运费

    private String licensePlateNumber; // 车牌号

    private String invoiceNumber; // 发票号码

    // 实际利润金额（分）
    private BigDecimal actualProfit;

    // 单位净利润金额（分）
    private BigDecimal unitActualProfit;

    // 客户经理
    private String managerCode;

    // 仓库名称
    private String warehouseName;


}
