package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExportShippingRateVO {

    @ApiModelProperty(value = "唯一标识ID")
    private Long id;

    @ApiModelProperty(value = "运输公司名称")
    private String companyName;

    @ApiModelProperty(value = "起点位置")
    private String startLocation;

    @ApiModelProperty(value = "终点位置")
    private String endLocation;

    @ApiModelProperty(value = "运费单价")
    private BigDecimal freightUnit;

    @ApiModelProperty(value = "运费单价（含税）")
    private BigDecimal taxIncluded;

    @ApiModelProperty(value = "合同运费单价")
    private BigDecimal contractFreightUnit;

    @ApiModelProperty(value = "合同含税单价")
    private BigDecimal contractTaxIncluded;

    @ApiModelProperty(value = "最低起送量")
    private BigDecimal minimumVolume;

    @ApiModelProperty(value = "规则类型 0 默认 1 特定")
    private Integer rateType;

    @ApiModelProperty(value = "客户内部编号集合，逗号分隔")
    private String internalCodes;

}