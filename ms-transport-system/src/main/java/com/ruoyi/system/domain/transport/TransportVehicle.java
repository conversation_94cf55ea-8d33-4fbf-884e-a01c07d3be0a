package com.ruoyi.system.domain.transport;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆信息表 transport_vehicle
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("车辆信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportVehicle extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("车辆ID")
    private Long id;

    /** 车牌号 */
    @Excel(name = "车牌号")
    @ApiModelProperty("车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String licensePlate;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    @ApiModelProperty("车辆类型")
    private String vehicleType;

    /** 载重吨位 */
    @Excel(name = "载重吨位")
    @ApiModelProperty("载重吨位")
    private BigDecimal loadCapacity;

    /** 油箱容量(升) */
    @Excel(name = "油箱容量(升)")
    @ApiModelProperty("油箱容量(升)")
    private BigDecimal fuelTankCapacity;

    /** 车辆状态:1-空闲,2-运输中,3-维修中,4-报废 */
    @Excel(name = "车辆状态", readConverterExp = "1=空闲,2=运输中,3=维修中,4=报废")
    @ApiModelProperty("车辆状态:1-空闲,2-运输中,3-维修中,4-报废")
    @NotNull(message = "车辆状态不能为空")
    private Integer vehicleStatus;

    /** 购买日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "购买日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("购买日期")
    private Date purchaseDate;

    /** 年检日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "年检日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("年检日期")
    private Date annualInspectionDate;

    /** 保险到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保险到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("保险到期日期")
    private Date insuranceExpiryDate;

    /** 所属区域代码 */
    @Excel(name = "所属区域代码")
    @ApiModelProperty("所属区域代码")
    private String regionCode;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 扩展字段
    /** 车辆状态文本 */
    @ApiModelProperty("车辆状态文本")
    private String vehicleStatusText;

    /** 是否可用 */
    @ApiModelProperty("是否可用")
    private Boolean isAvailable;

    public String getVehicleStatusText() {
        if (vehicleStatus == null) {
            return "";
        }
        switch (vehicleStatus) {
            case 1:
                return "空闲";
            case 2:
                return "运输中";
            case 3:
                return "维修中";
            case 4:
                return "报废";
            default:
                return "未知";
        }
    }

    public Boolean getIsAvailable() {
        return vehicleStatus != null && vehicleStatus == 1;
    }
}
