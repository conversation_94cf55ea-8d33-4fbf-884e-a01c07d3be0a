package com.ruoyi.system.domain.transport;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆维护记录表 transport_vehicle_maintenance
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("车辆维护记录")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportVehicleMaintenance extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("维护记录ID")
    private Long id;

    /** 车辆ID */
    @ApiModelProperty("车辆ID")
    @NotNull(message = "车辆ID不能为空")
    private Long vehicleId;

    /** 维护类型 */
    @Excel(name = "维护类型")
    @ApiModelProperty("维护类型")
    private String maintenanceType;

    /** 维护日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "维护日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("维护日期")
    private Date maintenanceDate;

    /** 维护费用 */
    @Excel(name = "维护费用")
    @ApiModelProperty("维护费用")
    private BigDecimal maintenanceCost;

    /** 维护描述 */
    @Excel(name = "维护描述")
    @ApiModelProperty("维护描述")
    private String maintenanceDesc;

    /** 下次维护日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "下次维护日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("下次维护日期")
    private Date nextMaintenanceDate;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 扩展字段
    /** 车牌号 */
    @ApiModelProperty("车牌号")
    private String licensePlate;

    /** 车辆类型 */
    @ApiModelProperty("车辆类型")
    private String vehicleType;
}
