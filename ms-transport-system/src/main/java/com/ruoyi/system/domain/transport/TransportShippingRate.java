package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 运费定价信息对象 transport_shipping_rates
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("运费定价信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportShippingRate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    private Long id;

    /** 装货点ID */
    @ApiModelProperty(value = "装货点ID", required = true)
    private Long loadingPointId;

    /** 装货点名称 */
    @ApiModelProperty(value = "装货点名称")
    private String loadingPointName;

    /** 起点位置 */
    @Excel(name = "起点位置")
    @ApiModelProperty(value = "起点位置", required = true)
    private String startLocation;

    /** 终点位置 */
    @Excel(name = "终点位置")
    @ApiModelProperty(value = "终点位置", required = true)
    private String endLocation;

    /** 结束的市编码 */
    @Excel(name = "结束的市编码")
    @ApiModelProperty(value = "结束的市编码", required = true)
    private String endCityCode;

    /** 开始的市编码 */
    @Excel(name = "开始的市编码")
    @ApiModelProperty(value = "开始的市编码", required = true)
    private String startCityCode;

    /** 运费单价 */
    @Excel(name = "运费单价")
    @ApiModelProperty(value = "运费单价", required = true)
    private BigDecimal freightUnit;

    /** 含税单价 */
    @Excel(name = "含税单价")
    @ApiModelProperty(value = "含税单价", required = true)
    private BigDecimal taxIncluded;

    /** 最低起送量 */
    @Excel(name = "最低起送量")
    @ApiModelProperty("最低起送量")
    private BigDecimal minimumVolume;

    /** 客户内部编号集合 */
    @Excel(name = "客户内部编号集合")
    @ApiModelProperty("客户内部编号集合")
    private String internalCodes;

    /** 规则类型 0 默认 1 特定 */
    @Excel(name = "规则类型", readConverterExp = "0=默认,1=特定")
    @ApiModelProperty(value = "规则类型 0 默认 1 特定", required = true)
    private Integer rateType;

    /** 适用油品类型 */
    @Excel(name = "适用油品类型")
    @ApiModelProperty("适用油品类型")
    private String productType;

    /** 合同运费单价 */
    @Excel(name = "合同运费单价")
    @ApiModelProperty(value = "合同运费单价", required = true)
    private BigDecimal contractFreightUnit;

    /** 合同含税单价 */
    @Excel(name = "合同含税单价")
    @ApiModelProperty(value = "合同含税单价", required = true)
    private BigDecimal contractTaxIncluded;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    private Integer isDeleted;

    @ApiModelProperty(value = "杂费配置明细")
    private List<TransportShippingRateDetail> otherFeeList;

    /** 规则类型和内部编号的组合，用于前端展示 */
    @ApiModelProperty(value = "规则类型和客户")
    private String rateTypeAndInternalCodes;

    @ApiModelProperty(value = "收货方信息")
    private List<Map<String, Object>> consigneeInfo;
}
