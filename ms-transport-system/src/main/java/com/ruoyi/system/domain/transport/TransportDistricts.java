package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运输管理区县表 transport_districts
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("运输管理区县")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportDistricts extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 区县编码 */
    @ApiModelProperty("区县编码")
    private String code;

    /** 区县名称 */
    @Excel(name = "区县名称")
    @ApiModelProperty("区县名称")
    private String name;

    /** 城市编码 */
    @ApiModelProperty("城市编码")
    private String cityCode;
}
