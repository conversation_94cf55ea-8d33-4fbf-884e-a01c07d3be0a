package com.ruoyi.system.domain.transport;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 出入库记录主表对象 transport_inventory_record
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("出入库记录主表")
public class TransportInventoryRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("唯一标识ID")
    private Long id;

    /** 出入库单号 */
    @Excel(name = "出入库单号")
    @ApiModelProperty("出入库单号")
    private String recordNo;

    /** 仓库ID */
    @Excel(name = "仓库ID")
    @ApiModelProperty("仓库ID")
    private Long warehouseId;

    /** 单据类型:1-入库,2-出库,3-调拨,4-盘点 */
    @Excel(name = "单据类型", readConverterExp = "1=入库,2=出库,3=调拨,4=盘点")
    @ApiModelProperty("单据类型:1-入库,2-出库,3-调拨,4-盘点")
    private Integer recordType;

    /** 业务类型:1-采购入库,2-生产入库,3-销售出库,4-运输出库,5-其他 */
    @Excel(name = "业务类型", readConverterExp = "1=采购入库,2=生产入库,3=销售出库,4=运输出库,5=其他")
    @ApiModelProperty("业务类型:1-采购入库,2-生产入库,3-销售出库,4-运输出库,5-其他")
    private Integer businessType;

    /** 关联单号(运输单号等) */
    @Excel(name = "关联单号")
    @ApiModelProperty("关联单号(运输单号等)")
    private String relatedOrderNo;

    /** 单据状态:1-待审核,2-已审核,3-已完成,4-已取消 */
    @Excel(name = "单据状态", readConverterExp = "1=待审核,2=已审核,3=已完成,4=已取消")
    @ApiModelProperty("单据状态:1-待审核,2-已审核,3-已完成,4-已取消")
    private Integer recordStatus;

    /** 总数量 */
    @Excel(name = "总数量")
    @ApiModelProperty("总数量")
    private BigDecimal totalQuantity;

    /** 操作员 */
    @Excel(name = "操作员")
    @ApiModelProperty("操作员")
    private String operatorName;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty("审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 关联对象
    /** 仓库信息 */
    @ApiModelProperty("仓库信息")
    private TransportWarehouse warehouse;

    /** 出入库明细列表 */
    @ApiModelProperty("出入库明细列表")
    private List<TransportInventoryRecordDetail> detailList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRecordNo(String recordNo) 
    {
        this.recordNo = recordNo;
    }

    public String getRecordNo() 
    {
        return recordNo;
    }
    public void setWarehouseId(Long warehouseId) 
    {
        this.warehouseId = warehouseId;
    }

    public Long getWarehouseId() 
    {
        return warehouseId;
    }
    public void setRecordType(Integer recordType) 
    {
        this.recordType = recordType;
    }

    public Integer getRecordType() 
    {
        return recordType;
    }
    public void setBusinessType(Integer businessType) 
    {
        this.businessType = businessType;
    }

    public Integer getBusinessType() 
    {
        return businessType;
    }
    public void setRelatedOrderNo(String relatedOrderNo) 
    {
        this.relatedOrderNo = relatedOrderNo;
    }

    public String getRelatedOrderNo() 
    {
        return relatedOrderNo;
    }
    public void setRecordStatus(Integer recordStatus) 
    {
        this.recordStatus = recordStatus;
    }

    public Integer getRecordStatus() 
    {
        return recordStatus;
    }
    public void setTotalQuantity(BigDecimal totalQuantity) 
    {
        this.totalQuantity = totalQuantity;
    }

    public BigDecimal getTotalQuantity() 
    {
        return totalQuantity;
    }
    public void setOperatorName(String operatorName) 
    {
        this.operatorName = operatorName;
    }

    public String getOperatorName() 
    {
        return operatorName;
    }
    public void setAuditBy(String auditBy) 
    {
        this.auditBy = auditBy;
    }

    public String getAuditBy() 
    {
        return auditBy;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    public TransportWarehouse getWarehouse() 
    {
        return warehouse;
    }

    public void setWarehouse(TransportWarehouse warehouse) 
    {
        this.warehouse = warehouse;
    }

    public List<TransportInventoryRecordDetail> getDetailList() 
    {
        return detailList;
    }

    public void setDetailList(List<TransportInventoryRecordDetail> detailList) 
    {
        this.detailList = detailList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("recordNo", getRecordNo())
            .append("warehouseId", getWarehouseId())
            .append("recordType", getRecordType())
            .append("businessType", getBusinessType())
            .append("relatedOrderNo", getRelatedOrderNo())
            .append("recordStatus", getRecordStatus())
            .append("totalQuantity", getTotalQuantity())
            .append("operatorName", getOperatorName())
            .append("auditBy", getAuditBy())
            .append("auditTime", getAuditTime())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
