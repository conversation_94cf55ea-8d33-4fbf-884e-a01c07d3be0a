package com.ruoyi.system.domain.transport;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 商品信息对象 transport_product
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("商品信息")
public class TransportProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("唯一标识ID")
    private Long id;

    /** 商品名称 */
    @Excel(name = "商品名称")
    @ApiModelProperty("商品名称")
    private String productName;

    /** 商品编码 */
    @Excel(name = "商品编码")
    @ApiModelProperty("商品编码")
    private String productCode;

    /** 商品类型 */
    @Excel(name = "商品类型")
    @ApiModelProperty("商品类型")
    private String productType;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty("商品规格")
    private String productSpec;

    /** 计量单位 */
    @Excel(name = "计量单位")
    @ApiModelProperty("计量单位")
    private String unit;

    /** 密度(kg/L) */
    @Excel(name = "密度(kg/L)")
    @ApiModelProperty("密度(kg/L)")
    private BigDecimal density;

    /** 安全库存 */
    @Excel(name = "安全库存")
    @ApiModelProperty("安全库存")
    private BigDecimal safetyStock;

    /** 商品状态:1-正常,2-停用 */
    @Excel(name = "商品状态", readConverterExp = "1=正常,2=停用")
    @ApiModelProperty("商品状态:1-正常,2-停用")
    private Integer productStatus;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }
    public void setProductCode(String productCode) 
    {
        this.productCode = productCode;
    }

    public String getProductCode() 
    {
        return productCode;
    }
    public void setProductType(String productType) 
    {
        this.productType = productType;
    }

    public String getProductType() 
    {
        return productType;
    }
    public void setProductSpec(String productSpec) 
    {
        this.productSpec = productSpec;
    }

    public String getProductSpec() 
    {
        return productSpec;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setDensity(BigDecimal density) 
    {
        this.density = density;
    }

    public BigDecimal getDensity() 
    {
        return density;
    }
    public void setSafetyStock(BigDecimal safetyStock) 
    {
        this.safetyStock = safetyStock;
    }

    public BigDecimal getSafetyStock() 
    {
        return safetyStock;
    }
    public void setProductStatus(Integer productStatus) 
    {
        this.productStatus = productStatus;
    }

    public Integer getProductStatus() 
    {
        return productStatus;
    }
    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productName", getProductName())
            .append("productCode", getProductCode())
            .append("productType", getProductType())
            .append("productSpec", getProductSpec())
            .append("unit", getUnit())
            .append("density", getDensity())
            .append("safetyStock", getSafetyStock())
            .append("productStatus", getProductStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
