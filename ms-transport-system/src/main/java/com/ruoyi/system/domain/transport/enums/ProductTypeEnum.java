package com.ruoyi.system.domain.transport.enums;

import lombok.Getter;

/**
 * 油品类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ProductTypeEnum {

    GASOLINE("GASOLINE", "汽油"),
    DIESEL("DIESEL", "柴油"),
    KEROSENE("K<PERSON><PERSON><PERSON><PERSON>", "煤油"),
    OTHER("OTH<PERSON>", "其他");

    private final String code;
    private final String description;

    ProductTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ProductTypeEnum fromDescription(String description) {
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            if (value.description.equalsIgnoreCase(description)) {
                return value;
            }
        }
        return null; // Or throw an exception
    }
}
