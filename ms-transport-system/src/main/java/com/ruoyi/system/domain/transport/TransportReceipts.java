package com.ruoyi.system.domain.transport;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 运输对账表 transport_receipts
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("运输对账信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportReceipts extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("对账ID")
    private Long id;

    /** 收据号码/订单号 */
    @Excel(name = "收据号码")
    @ApiModelProperty("收据号码/订单号")
    @NotBlank(message = "收据号码不能为空")
    private String receiptNumber;

    /** 款项金额(分) */
    @Excel(name = "款项金额")
    @ApiModelProperty("款项金额(分)")
    private BigDecimal amount;

    /** 款项类型 预收款 0 应付款 1 红冲应收 2 红冲应付 3 */
    @Excel(name = "款项类型", readConverterExp = "0=预收款,1=应付款,2=红冲应收,3=红冲应付")
    @ApiModelProperty("款项类型 预收款 0 应付款 1 红冲应收 2 红冲应付 3")
    private Integer fundType;

    /** 描述 */
    @Excel(name = "描述")
    @ApiModelProperty("描述")
    private String description;

    /** 委托客户编号 */
    @Excel(name = "委托客户编号")
    @ApiModelProperty("委托客户编号")
    @NotBlank(message = "委托客户编号不能为空")
    private String customerCode;

    /** 内部编号 */
    @Excel(name = "内部编号")
    @ApiModelProperty("内部编号")
    private String internalCode;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @ApiModelProperty("客户名称")
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    /** 收款方式 */
    @Excel(name = "收款方式")
    @ApiModelProperty("收款方式")
    private String paymentMethod;

    /** 收款银行 */
    @Excel(name = "收款银行")
    @ApiModelProperty("收款银行")
    private String bankName;

    /** 收款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("收款时间")
    private Date receiptDate;

    /** 可用款项（分） */
    @Excel(name = "可用款项")
    @ApiModelProperty("可用款项（分）")
    @NotNull(message = "可用款项不能为空")
    private BigDecimal usedAmount;

    /** 总预存金额 */
    @Excel(name = "总预存金额")
    @ApiModelProperty("总预存金额")
    private BigDecimal totalPrepaidAmount;

    /** 总支出金额 */
    @Excel(name = "总支出金额")
    @ApiModelProperty("总支出金额")
    @NotNull(message = "总支出金额不能为空")
    private BigDecimal totalExpenseAmount;

    /** 总欠款金额 */
    @Excel(name = "总欠款金额")
    @ApiModelProperty("总欠款金额")
    @NotNull(message = "总欠款金额不能为空")
    private BigDecimal totalArrearsAmount;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 扩展字段
    /** 款项类型文本 */
    @ApiModelProperty("款项类型文本")
    private String fundTypeText;

    /** 是否启用文本 */
    @ApiModelProperty("是否启用文本")
    private String isActiveText;

    /** 查询开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("查询开始时间")
    private Date startDate;

    /** 查询结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("查询结束时间")
    private Date endDate;

    /**
     * 获取款项类型文本
     */
    public String getFundTypeText() {
        if (fundType == null) {
            return "";
        }
        switch (fundType) {
            case 0:
                return "预收款";
            case 1:
                return "应付款";
            case 2:
                return "红冲应收";
            case 3:
                return "红冲应付";
            default:
                return "未知";
        }
    }
}
