package com.ruoyi.system.domain.transport;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 司机信息表 transport_driver
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("司机信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportDriver extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("司机ID")
    private Long id;

    /** 司机姓名 */
    @Excel(name = "司机姓名")
    @ApiModelProperty("司机姓名")
    @NotBlank(message = "司机姓名不能为空")
    private String driverName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    private String driverPhone;

    /** 身份证号 */
    @Excel(name = "身份证号")
    @ApiModelProperty("身份证号")
    private String idCard;

    /** 驾照类型 */
    @Excel(name = "驾照类型")
    @ApiModelProperty("驾照类型")
    private String licenseType;

    /** 驾照号码 */
    @Excel(name = "驾照号码")
    @ApiModelProperty("驾照号码")
    private String licenseNumber;

    /** 驾龄(年) */
    @Excel(name = "驾龄(年)")
    @ApiModelProperty("驾龄(年)")
    private Integer drivingYears;

    /** 司机状态:1-在职,2-休假,3-离职 */
    @Excel(name = "司机状态", readConverterExp = "1=在职,2=休假,3=离职")
    @ApiModelProperty("司机状态:1-在职,2-休假,3=离职")
    @NotNull(message = "司机状态不能为空")
    private Integer driverStatus;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("入职日期")
    private Date hireDate;

    /** 紧急联系人 */
    @Excel(name = "紧急联系人")
    @ApiModelProperty("紧急联系人")
    private String emergencyContact;

    /** 紧急联系电话 */
    @Excel(name = "紧急联系电话")
    @ApiModelProperty("紧急联系电话")
    private String emergencyPhone;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 扩展字段
    /** 司机状态文本 */
    @ApiModelProperty("司机状态文本")
    private String driverStatusText;

    /** 是否可用 */
    @ApiModelProperty("是否可用")
    private Boolean isAvailable;

    public String getDriverStatusText() {
        if (driverStatus == null) {
            return "";
        }
        switch (driverStatus) {
            case 1:
                return "在职";
            case 2:
                return "休假";
            case 3:
                return "离职";
            default:
                return "未知";
        }
    }

    public Boolean getIsAvailable() {
        return driverStatus != null && driverStatus == 1;
    }
}
