package com.ruoyi.system.domain.transport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运费定价杂费细则信息表 transport_shipping_rate_detail
 *
 * <AUTHOR>
 * @date 2024-01-28
 */
@ApiModel("运费定价杂费细则信息表")
@Data
public class TransportShippingRateDetail {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    private Long id;

    /** 规则id */
    @ApiModelProperty(value = "规则id", required = true)
    private Long rateId;

    /** 车辆类型 */
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;

    /** 杂费 */
    @ApiModelProperty(value = "杂费", required = true)
    private BigDecimal otherExpenses;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updateBy;

    /** 修改时间 */
    private Date updateTime;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    private Integer isDeleted;
}
