package com.ruoyi.system.domain.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 运输对账Excel导入DTO
 *
 * <AUTHOR>
 */
@Data
public class TransportReceiptsDetails implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "唯一标识ID")
    private Long id;

    @Excel(name = "收据号码/订单号")
    private String receiptNumber;

    @Excel(name = "款项金额(分)")
    private BigDecimal amount;

    @Excel(name = "款项类型", readConverterExp = "0=预收款,1=应付款,2=红冲应收,3=红冲应付")
    private Integer fundType;

    @Excel(name = "描述")
    private String description;

    @Excel(name = "委托客户编号")
    private String customerCode;

    @Excel(name = "内部编号")
    private String internalCode;

    @Excel(name = "客户名称")
    private String customerName;

    @Excel(name = "收款方式")
    private String paymentMethod;

    @Excel(name = "收款银行")
    private String bankName;

    @Excel(name = "收款时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiptDate;

    @Excel(name = "可用款项（分）")
    private BigDecimal usedAmount;

    @Excel(name = "总预存金额")
    private BigDecimal totalPrepaidAmount;

    @Excel(name = "总支出金额")
    private BigDecimal totalExpenseAmount;

    @Excel(name = "总欠款金额")
    private BigDecimal totalArrearsAmount;

    @Excel(name = "创建人")
    private String createBy;

    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "修改人")
    private String updateBy;

    @Excel(name = "修改时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
