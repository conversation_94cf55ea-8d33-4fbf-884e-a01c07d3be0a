package com.ruoyi.system.domain.transport;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 库存信息对象 transport_inventory
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("库存信息")
public class TransportInventory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("唯一标识ID")
    private Long id;

    /** 仓库ID */
    @Excel(name = "仓库ID")
    @ApiModelProperty("仓库ID")
    private Long warehouseId;

    /** 商品ID */
    @Excel(name = "商品ID")
    @ApiModelProperty("商品ID")
    private Long productId;

    /** 当前库存 */
    @Excel(name = "当前库存")
    @ApiModelProperty("当前库存")
    private BigDecimal currentStock;

    /** 可用库存 */
    @Excel(name = "可用库存")
    @ApiModelProperty("可用库存")
    private BigDecimal availableStock;

    /** 冻结库存 */
    @Excel(name = "冻结库存")
    @ApiModelProperty("冻结库存")
    private BigDecimal frozenStock;

    /** 最后入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后入库时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最后入库时间")
    private Date lastInTime;

    /** 最后出库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后出库时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("最后出库时间")
    private Date lastOutTime;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 关联对象
    /** 仓库信息 */
    @ApiModelProperty("仓库信息")
    private TransportWarehouse warehouse;

    /** 商品信息 */
    @ApiModelProperty("商品信息")
    private com.ruoyi.system.domain.TransportProduct product;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setWarehouseId(Long warehouseId) 
    {
        this.warehouseId = warehouseId;
    }

    public Long getWarehouseId() 
    {
        return warehouseId;
    }
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setCurrentStock(BigDecimal currentStock) 
    {
        this.currentStock = currentStock;
    }

    public BigDecimal getCurrentStock() 
    {
        return currentStock;
    }
    public void setAvailableStock(BigDecimal availableStock) 
    {
        this.availableStock = availableStock;
    }

    public BigDecimal getAvailableStock() 
    {
        return availableStock;
    }
    public void setFrozenStock(BigDecimal frozenStock) 
    {
        this.frozenStock = frozenStock;
    }

    public BigDecimal getFrozenStock() 
    {
        return frozenStock;
    }
    public void setLastInTime(Date lastInTime) 
    {
        this.lastInTime = lastInTime;
    }

    public Date getLastInTime() 
    {
        return lastInTime;
    }
    public void setLastOutTime(Date lastOutTime) 
    {
        this.lastOutTime = lastOutTime;
    }

    public Date getLastOutTime() 
    {
        return lastOutTime;
    }
    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    public TransportWarehouse getWarehouse() 
    {
        return warehouse;
    }

    public void setWarehouse(TransportWarehouse warehouse) 
    {
        this.warehouse = warehouse;
    }

    public com.ruoyi.system.domain.TransportProduct getProduct() 
    {
        return product;
    }

    public void setProduct(com.ruoyi.system.domain.TransportProduct product) 
    {
        this.product = product;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("warehouseId", getWarehouseId())
            .append("productId", getProductId())
            .append("currentStock", getCurrentStock())
            .append("availableStock", getAvailableStock())
            .append("frozenStock", getFrozenStock())
            .append("lastInTime", getLastInTime())
            .append("lastOutTime", getLastOutTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
