package com.ruoyi.system.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单统计信息
 */
@Data
public class OrderStatistics {
    /**
     * 统计日期
     */
    private String orderDate;

    /**
     * 订单数量
     */
    private Integer orderCount;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    public OrderStatistics(String orderDate, Integer orderCount, BigDecimal totalAmount) {
        this.orderDate = orderDate;
        this.orderCount = orderCount;
        this.totalAmount = totalAmount;
    }
}