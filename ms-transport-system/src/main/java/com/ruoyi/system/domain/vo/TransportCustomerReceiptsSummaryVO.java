package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class TransportCustomerReceiptsSummaryVO {

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty("内部编号")
    private String internalCode;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户实时账期")
    private BigDecimal pendingPaymentAmount;

    @ApiModelProperty(value = "客户7日内账期")
    private BigDecimal payableWithinSevenDays;

    @ApiModelProperty(value = "客户7-14日内账期")
    private BigDecimal payableSevenToFourteenDays;

    @ApiModelProperty(value = "客户14-30日内账期")
    private BigDecimal payableFourteenToThirtyDays;

    @ApiModelProperty(value = "客户30日以上账期")
    private BigDecimal payableOverThirtyDays;
}
