package com.ruoyi.system.domain.transport;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.system.enums.OrderStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 运输单主表 transport_order
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("运输单信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("运输单ID")
    private Long id;

    /** 运输单号 */
    @Excel(name = "运输单号")
    @ApiModelProperty("运输单号")
    @NotBlank(message = "运输单号不能为空")
    private String orderNo;

    /** 内部编号 */
    @Excel(name = "内部编号")
    @ApiModelProperty("内部编号")
    @NotBlank(message = "内部编号不能为空")
    private String internalCode;

    /** 委托客户ID */
    @ApiModelProperty("委托客户ID")
    @NotNull(message = "委托客户不能为空")
    private Long customerId;

    /** 委托客户名称 */
    @Excel(name = "委托客户名称")
    @ApiModelProperty("委托客户名称")
    @NotBlank(message = "委托客户名称不能为空")
    private String customerName;

    /** 收货方ID */
    @ApiModelProperty("收货方ID")
    private Long consigneeId;

    /** 收货方名称 */
    @Excel(name = "收货方名称")
    @ApiModelProperty("收货方名称")
    @NotBlank(message = "收货方名称不能为空")
    private String consigneeName;

    /** 收货方国家编码 */
    @ApiModelProperty("收货方国家编码")
    private String consigneeCountryCode;

    /** 收货方国家 */
    @ApiModelProperty("收货方国家")
    private String consigneeCountry;

    /** 收货方省份编码 */
    @ApiModelProperty("收货方省份编码")
    private String consigneeProvinceCode;

    /** 收货方省份 */
    @ApiModelProperty("收货方省份")
    private String consigneeProvince;

    /** 收货方城市编码 */
    @ApiModelProperty("收货方城市编码")
    private String consigneeCityCode;

    /** 收货方城市 */
    @ApiModelProperty("收货方城市")
    private String consigneeCity;

    /** 收货方区县编码 */
    @ApiModelProperty("收货方区县编码")
    private String consigneeDistrictCode;

    /** 收货方区县 */
    @ApiModelProperty("收货方区县")
    private String consigneeDistrict;

    /** 收货详细地址 */
    @Excel(name = "收货地址")
    @ApiModelProperty("收货详细地址")
    @NotBlank(message = "收货地址不能为空")
    private String consigneeAddress;

    /** 收货方联系人 */
    @Excel(name = "收货方联系人")
    @ApiModelProperty("收货方联系人")
    private String consigneeContact;

    /** 收货方电话 */
    @Excel(name = "收货方电话")
    @ApiModelProperty("收货方电话")
    private String consigneePhone;

    /** 装货点ID */
    @ApiModelProperty("装货点ID")
    private Long loadingPointId;

    /** 装货点名称 */
    @Excel(name = "装货点名称")
    @ApiModelProperty("装货点名称")
    @NotBlank(message = "装货点名称不能为空")
    private String loadingPointName;

    /** 装货点国家编码 */
    @ApiModelProperty("装货点国家编码")
    private String loadingCountryCode;

    /** 装货点国家 */
    @ApiModelProperty("装货点国家")
    private String loadingCountry;

    /** 装货点省份编码 */
    @ApiModelProperty("装货点省份编码")
    private String loadingProvinceCode;

    /** 装货点省份 */
    @ApiModelProperty("装货点省份")
    private String loadingProvince;

    /** 装货点城市编码 */
    @ApiModelProperty("装货点城市编码")
    private String loadingCityCode;

    /** 装货点城市 */
    @ApiModelProperty("装货点城市")
    private String loadingCity;

    /** 装货点区县编码 */
    @ApiModelProperty("装货点区县编码")
    private String loadingDistrictCode;

    /** 装货点区县 */
    @ApiModelProperty("装货点区县")
    private String loadingDistrict;

    /** 装货详细地址 */
    @Excel(name = "装货地址")
    @ApiModelProperty("装货详细地址")
    @NotBlank(message = "装货地址不能为空")
    private String loadingAddress;

    /** 油品ID */
    @ApiModelProperty("油品ID")
    private Long productId;

    /** 油品名称 */
    @Excel(name = "油品名称")
    @ApiModelProperty("油品名称")
    private String productName;

    /** 运输数量(吨) */
    @Excel(name = "运输数量(吨)")
    @ApiModelProperty("运输数量(吨)")
    private BigDecimal productQuantity;

    /** 总体积(升) */
    @Excel(name = "总体积(升)")
    @ApiModelProperty("总体积(升)")
    private BigDecimal totalVolume;

    /** 运输距离(公里) */
    @Excel(name = "运输距离(公里)")
    @ApiModelProperty("运输距离(公里)")
    private BigDecimal transportDistance;

    /** 订单状态:1-待指派,2-已指派,3-前往装货,4-装货中,5-运输中,6-已送达,7-已对账 */
    @Excel(name = "订单状态", readConverterExp = "1=待指派,2=已指派,3=前往装货,4=装货中,5=运输中,6=已送达,7=已对账")
    @ApiModelProperty("订单状态:1-待指派,2-已指派,3-前往装货,4-装货中,5=运输中,6=已送达,7=已对账")
    @NotNull(message = "订单状态不能为空")
    private Integer orderStatus;

    /** 指派车辆ID */
    @ApiModelProperty("指派车辆ID")
    private Long vehicleId;

    /** 指派司机ID */
    @ApiModelProperty("指派司机ID")
    private Long driverId;

    /** 车牌号 */
    @Excel(name = "车牌号")
    @ApiModelProperty("车牌号")
    private String licensePlate;

    /** 计划装货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划装货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("计划装货时间")
    private Date plannedLoadingTime;

    /** 实际装货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际装货时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("实际装货时间")
    private Date actualLoadingTime;

    /** 计划送达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划送达时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("计划送达时间")
    private Date plannedDeliveryTime;

    /** 实际送达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际送达时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("实际送达时间")
    private Date actualDeliveryTime;

    /** 装货完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("装货完成时间")
    private Date loadingCompletedTime;

    /** 出发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("出发时间")
    private Date departureTime;

    /** 到达时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("到达时间")
    private Date arrivalTime;

    /** 运输费用 */
    @Excel(name = "运输费用")
    @ApiModelProperty("运输费用")
    @NotNull(message = "运输费用不能为空")
    private BigDecimal shippingCost;

    /** 杂费 */
    @Excel(name = "杂费")
    @ApiModelProperty("杂费")
    private BigDecimal otherExpenses;

    /** 含税单价 */
    @Excel(name = "含税单价")
    @ApiModelProperty("含税单价")
    private BigDecimal taxIncluded;

    /** 配送要求 */
    @ApiModelProperty("配送要求")
    private String deliveryRequirements;

    /** 特殊说明 */
    @ApiModelProperty("特殊说明")
    private String specialInstructions;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 扩展字段
    /** 订单状态文本 */
    @ApiModelProperty("订单状态文本")
    private String orderStatusText;

    /** 司机姓名 */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /** 完整收货地址 */
    @ApiModelProperty("完整收货地址")
    private String fullConsigneeAddress;

    /** 完整装货地址 */
    @ApiModelProperty("完整装货地址")
    private String fullLoadingAddress;

    public String getOrderStatusText() {
        if (orderStatus == null) {
            return "";
        }
        OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(this.orderStatus);
        return statusEnum != null ? statusEnum.getDescription() : "未知";
    }

    public String getFullConsigneeAddress() {
        StringBuilder sb = new StringBuilder();
        if (consigneeCountry != null) sb.append(consigneeCountry);
        if (consigneeProvince != null) sb.append(consigneeProvince);
        if (consigneeCity != null) sb.append(consigneeCity);
        if (consigneeDistrict != null) sb.append(consigneeDistrict);
        if (consigneeAddress != null) sb.append(consigneeAddress);
        return sb.toString();
    }

    public String getFullLoadingAddress() {
        StringBuilder sb = new StringBuilder();
        if (loadingCountry != null) sb.append(loadingCountry);
        if (loadingProvince != null) sb.append(loadingProvince);
        if (loadingCity != null) sb.append(loadingCity);
        if (loadingDistrict != null) sb.append(loadingDistrict);
        if (loadingAddress != null) sb.append(loadingAddress);
        return sb.toString();
    }
}
