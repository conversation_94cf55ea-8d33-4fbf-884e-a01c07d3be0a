package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 运输管理城市表 transport_cities
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("运输管理城市")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportCities extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 城市编码 */
    @ApiModelProperty("城市编码")
    private String code;

    /** 城市名称 */
    @Excel(name = "城市名称")
    @ApiModelProperty("城市名称")
    private String name;

    /** 省份编码 */
    @ApiModelProperty("省份编码")
    private String provinceCode;

    /** 区县列表 */
    @ApiModelProperty("区县列表")
    private List<TransportDistricts> districts;
}
