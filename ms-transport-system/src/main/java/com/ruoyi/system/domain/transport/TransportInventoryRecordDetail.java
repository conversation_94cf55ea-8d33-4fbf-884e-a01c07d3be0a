package com.ruoyi.system.domain.transport;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 出入库记录明细表对象 transport_inventory_record_detail
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("出入库记录明细表")
public class TransportInventoryRecordDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("唯一标识ID")
    private Long id;

    /** 出入库记录ID */
    @Excel(name = "出入库记录ID")
    @ApiModelProperty("出入库记录ID")
    private Long recordId;

    /** 商品ID */
    @Excel(name = "商品ID")
    @ApiModelProperty("商品ID")
    private Long productId;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty("数量")
    private BigDecimal quantity;

    /** 单价 */
    @Excel(name = "单价")
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /** 金额 */
    @Excel(name = "金额")
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /** 批次号 */
    @Excel(name = "批次号")
    @ApiModelProperty("批次号")
    private String batchNo;

    /** 生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生产日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("生产日期")
    private Date productionDate;

    /** 有效期至 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期至", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("有效期至")
    private Date expiryDate;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 关联对象
    /** 商品信息 */
    @ApiModelProperty("商品信息")
    private com.ruoyi.system.domain.TransportProduct product;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }
    public void setUnitPrice(BigDecimal unitPrice) 
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice() 
    {
        return unitPrice;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setBatchNo(String batchNo) 
    {
        this.batchNo = batchNo;
    }

    public String getBatchNo() 
    {
        return batchNo;
    }
    public void setProductionDate(Date productionDate) 
    {
        this.productionDate = productionDate;
    }

    public Date getProductionDate() 
    {
        return productionDate;
    }
    public void setExpiryDate(Date expiryDate) 
    {
        this.expiryDate = expiryDate;
    }

    public Date getExpiryDate() 
    {
        return expiryDate;
    }
    public void setIsDeleted(Integer isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Integer getIsDeleted() 
    {
        return isDeleted;
    }

    public com.ruoyi.system.domain.TransportProduct getProduct() 
    {
        return product;
    }

    public void setProduct(com.ruoyi.system.domain.TransportProduct product) 
    {
        this.product = product;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("recordId", getRecordId())
            .append("productId", getProductId())
            .append("quantity", getQuantity())
            .append("unitPrice", getUnitPrice())
            .append("amount", getAmount())
            .append("batchNo", getBatchNo())
            .append("productionDate", getProductionDate())
            .append("expiryDate", getExpiryDate())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDeleted", getIsDeleted())
            .toString();
    }
}
