package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 运输管理国家表 transport_countries
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("运输管理国家")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportCountries extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 国家编码 */
    @ApiModelProperty("国家编码")
    private String code;

    /** 国家名称 */
    @Excel(name = "国家名称")
    @ApiModelProperty("国家名称")
    private String name;

    /** 省份列表 */
    @ApiModelProperty("省份列表")
    private List<TransportProvinces> provinces;
}
