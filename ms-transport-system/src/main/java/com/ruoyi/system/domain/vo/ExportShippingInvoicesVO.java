package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ExportShippingInvoicesVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "运输公司名称")
    private String companyName;

    @ApiModelProperty(value = "起始地")
    private String startLocation;

    @ApiModelProperty(value = "目的地")
    private String endLocation;

    @ApiModelProperty(value = "总体积")
    private BigDecimal totalVolume;

    @ApiModelProperty(value = "运输费用")
    private BigDecimal shippingCost;

    @ApiModelProperty(value = "状态 10 待传单号 20 待确认 30 已确认")
    private Integer status;

    @ApiModelProperty(value = "发票号")
    private String invoiceNum;


}
