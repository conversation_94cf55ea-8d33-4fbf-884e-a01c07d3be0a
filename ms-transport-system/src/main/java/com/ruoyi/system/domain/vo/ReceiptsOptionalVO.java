package com.ruoyi.system.domain.vo;

import java.math.BigDecimal;

public class ReceiptsOptionalVO {

    /**
     * 收据号码/订单号。
     */
    private String receiptNumber;

    /**
     * 款项金额(分)。
     */
    private BigDecimal amount;

    /**
     * 可用款项金额(分)。
     */
    private BigDecimal availableAmount;

    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAvailableAmount() {
        return availableAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }

    @Override
    public String toString() {
        return "ReceiptsOptionalVO{" +
                "receiptNumber='" + receiptNumber + '\'' +
                ", amount=" + amount +
                ", availableAmount=" + availableAmount +
                '}';
    }
}
