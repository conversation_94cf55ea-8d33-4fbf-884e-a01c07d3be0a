package com.ruoyi.system.domain.vo;

import java.math.BigDecimal;
import java.util.Date;

public class ProductInfo {

    /**
     * 商品分类id
     */
    private Long categoryId;
    /**
     * 商品分类名称
     */
    private String categoryName;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 规格型号
     */
    private String model;
    /**
     * 等级
     */
    private String grade;
    /**
     * 包装形式
     */
    private String packaging;
    /**
     * 净含量/体积
     */
    private BigDecimal netContent;
    /**
     * 单位
     */
    private String unit;
    /**
     * 销售单价
     */
    private BigDecimal price;
    /**
     * 产地
     */
    private String origin;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 运输方式
     */
    private String shippingMethod;
    /**
     * 保质期
     */
    private Date shelfLife;
    /**
     * 状态，上架、下架
     */
    private String status;

    // 产品可用库存数量
    private Integer availableInventory;

    // 产品的成本价格
    private BigDecimal cost;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getPackaging() {
        return packaging;
    }

    public void setPackaging(String packaging) {
        this.packaging = packaging;
    }

    public BigDecimal getNetContent() {
        return netContent;
    }

    public void setNetContent(BigDecimal netContent) {
        this.netContent = netContent;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(String shippingMethod) {
        this.shippingMethod = shippingMethod;
    }

    public Date getShelfLife() {
        return shelfLife;
    }

    public void setShelfLife(Date shelfLife) {
        this.shelfLife = shelfLife;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getAvailableInventory() {
        return availableInventory;
    }

    public void setAvailableInventory(Integer availableInventory) {
        this.availableInventory = availableInventory;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    @Override
    public String toString() {
        return "ProductInfo{" +
                "categoryId=" + categoryId +
                ", categoryName='" + categoryName + '\'' +
                ", name='" + name + '\'' +
                ", model='" + model + '\'' +
                ", grade='" + grade + '\'' +
                ", packaging='" + packaging + '\'' +
                ", netContent=" + netContent +
                ", unit='" + unit + '\'' +
                ", price=" + price +
                ", origin='" + origin + '\'' +
                ", supplier='" + supplier + '\'' +
                ", shippingMethod='" + shippingMethod + '\'' +
                ", shelfLife=" + shelfLife +
                ", status='" + status + '\'' +
                ", availableInventory=" + availableInventory +
                ", cost=" + cost +
                '}';
    }
}
