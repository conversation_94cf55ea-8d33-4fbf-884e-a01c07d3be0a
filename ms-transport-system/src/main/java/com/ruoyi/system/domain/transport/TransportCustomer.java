package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 客户信息表 transport_customer
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("客户信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportCustomer extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("客户ID")
    private Long id;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @ApiModelProperty("客户名称")
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    /** 客户编码 */
    @Excel(name = "客户编码")
    @ApiModelProperty("客户编码")
    private String customerCode;

    /** 公司类型 */
    @Excel(name = "公司类型")
    @ApiModelProperty("公司类型")
    private String companyType;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @ApiModelProperty("联系邮箱")
    private String contactEmail;

    /** 公司地址 */
    @Excel(name = "公司地址")
    @ApiModelProperty("公司地址")
    private String companyAddress;

    /** 客户状态:1-正常合作,2-暂停合作,3-终止合作 */
    @Excel(name = "客户状态", readConverterExp = "1=正常合作,2=暂停合作,3=终止合作")
    @ApiModelProperty("客户状态:1-正常合作,2-暂停合作,3-终止合作")
    @NotNull(message = "客户状态不能为空")
    private Integer customerStatus;

    /** 信用等级:AAA,AA,A,B,C */
    @Excel(name = "信用等级")
    @ApiModelProperty("信用等级:AAA,AA,A,B,C")
    private String creditRating;

    /** 付款方式:1-月结,2-现结,3-预付 */
    @Excel(name = "付款方式", readConverterExp = "1=月结,2=现结,3=预付")
    @ApiModelProperty("付款方式:1-月结,2-现结,3-预付")
    private Integer paymentMethod;

    /** 账期天数 */
    @Excel(name = "账期天数")
    @ApiModelProperty("账期天数")
    private Integer paymentDays;

    /** 是否VIP客户 */
    @Excel(name = "是否VIP客户")
    @ApiModelProperty("是否VIP客户")
    private Boolean isVip;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    /** 收货方列表 */
    @ApiModelProperty("收货方列表")
    private List<TransportConsignee> consigneeList;

}
