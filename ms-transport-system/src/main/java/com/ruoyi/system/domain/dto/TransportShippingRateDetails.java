package com.ruoyi.system.domain.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 运费定价Excel导入DTO
 *
 * <AUTHOR>
 */
@Data
public class TransportShippingRateDetails implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "唯一标识ID")
    private Long id;

    @Excel(name = "起点")
    private String startLocation;

    @Excel(name = "终点")
    private String endLocation;

    @Excel(name = "运费单价")
    private BigDecimal freightUnit;

    @Excel(name = "含税单价")
    private BigDecimal taxIncluded;

    @Excel(name = "合同运费单价")
    private BigDecimal contractFreightUnit;

    @Excel(name = "合同含税单价")
    private BigDecimal contractTaxIncluded;

    @Excel(name = "最低起送量")
    private BigDecimal minimumVolume;

    @Excel(name = "客户内部编号(多个用逗号隔开)")
    private String internalCodes;

    @Excel(name = "适用油品类型")
    private String productType;
}
