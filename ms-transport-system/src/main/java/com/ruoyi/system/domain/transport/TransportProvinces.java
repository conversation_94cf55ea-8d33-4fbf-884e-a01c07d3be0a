package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 运输管理省份表 transport_provinces
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("运输管理省份")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportProvinces extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 省份编码 */
    @ApiModelProperty("省份编码")
    private String code;

    /** 省份名称 */
    @Excel(name = "省份名称")
    @ApiModelProperty("省份名称")
    private String name;

    /** 国家编码 */
    @ApiModelProperty("国家编码")
    private String countryCode;

    /** 城市列表 */
    @ApiModelProperty("城市列表")
    private List<TransportCities> cities;
}
