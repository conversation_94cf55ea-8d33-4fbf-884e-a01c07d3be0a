package com.ruoyi.system.domain.transport;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 终端收货方信息表 transport_consignee
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel("收货方信息")
@EqualsAndHashCode(callSuper = true)
@Data
public class TransportConsignee extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一标识ID */
    @ApiModelProperty("收货方ID")
    private Long id;

    /** 委托客户ID */
    @ApiModelProperty("委托客户ID")
    @NotNull(message = "委托客户不能为空")
    private Long customerId;

    /** 收货方名称 */
    @Excel(name = "收货方名称")
    @ApiModelProperty("收货方名称")
    @NotBlank(message = "收货方名称不能为空")
    private String consigneeName;

    /** 收货方编码 */
    @Excel(name = "收货方编码")
    @ApiModelProperty("收货方编码")
    private String consigneeCode;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty("联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /** 国家编码 */
    @ApiModelProperty("国家编码")
    private String countryCode;

    /** 国家名称 */
    @ApiModelProperty("国家名称")
    private String countryName;

    /** 省份编码 */
    @ApiModelProperty("省份编码")
    private String provinceCode;

    /** 省份名称 */
    @ApiModelProperty("省份名称")
    private String provinceName;

    /** 城市编码 */
    @ApiModelProperty("城市编码")
    private String cityCode;

    /** 城市名称 */
    @ApiModelProperty("城市名称")
    private String cityName;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @ApiModelProperty("详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String detailAddress;

    /** 收货方类型:1-加油站,2-工厂,3-仓库,4-其他 */
    @Excel(name = "收货方类型", readConverterExp = "1=加油站,2=工厂,3=仓库,4=其他")
    @ApiModelProperty("收货方类型:1-加油站,2-工厂,3=仓库,4=其他")
    private Integer consigneeType;

    /** 是否启用:1-启用,0-停用 */
    @Excel(name = "是否启用", readConverterExp = "1=启用,0=停用")
    @ApiModelProperty("是否启用:1-启用,0=停用")
    private Integer isActive;

    /** 逻辑删除标志，0表示未删除，1表示已删除 */
    @ApiModelProperty("逻辑删除标志")
    private Integer isDeleted;

    // 扩展字段
    /** 委托客户名称 */
    @ApiModelProperty("委托客户名称")
    private String customerName;

    /** 收货方类型文本 */
    @ApiModelProperty("收货方类型文本")
    private String consigneeTypeText;

    /** 完整地址 */
    @ApiModelProperty("完整地址")
    private String fullAddress;

    /** 是否启用文本 */
    @ApiModelProperty("是否启用文本")
    private String isActiveText;

    public String getConsigneeTypeText() {
        if (consigneeType == null) {
            return "";
        }
        switch (consigneeType) {
            case 1:
                return "加油站";
            case 2:
                return "工厂";
            case 3:
                return "仓库";
            case 4:
                return "其他";
            default:
                return "未知";
        }
    }

    public String getFullAddress() {
        StringBuilder sb = new StringBuilder();
        if (countryName != null) sb.append(countryName);
        if (provinceName != null) sb.append(provinceName);
        if (cityName != null) sb.append(cityName);
        if (detailAddress != null) sb.append(detailAddress);
        return sb.toString();
    }

    public String getIsActiveText() {
        if (isActive == null) {
            return "";
        }
        return isActive == 1 ? "启用" : "停用";
    }
}
