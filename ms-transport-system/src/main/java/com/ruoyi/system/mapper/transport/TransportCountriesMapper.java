package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportCountries;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 运输管理国家Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportCountriesMapper {
    
    /**
     * 查询运输管理国家列表
     * 
     * @return 运输管理国家集合
     */
    public List<TransportCountries> selectTransportCountriesList();

    /**
     * 根据国家编码查询国家信息
     * 
     * @param code 国家编码
     * @return 国家信息
     */
    public TransportCountries selectTransportCountriesByCode(String code);

    List<TransportCountries> selectList();
}
