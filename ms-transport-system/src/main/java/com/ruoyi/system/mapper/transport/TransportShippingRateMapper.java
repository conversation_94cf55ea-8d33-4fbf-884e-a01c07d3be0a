package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportShippingRate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运费定价信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface TransportShippingRateMapper 
{
    /**
     * 查询运费定价信息
     * 
     * @param id 运费定价信息主键
     * @return 运费定价信息
     */
    public TransportShippingRate selectTransportShippingRateById(Long id);

    /**
     * 根据ID列表查询运费定价信息列表
     * 
     * @param ids ID列表
     * @return 运费定价信息集合
     */
    List<TransportShippingRate> listByIds(@Param("ids") List<Long> ids);

    /**
     * 查询运费定价信息列表
     * 
     * @param transportShippingRate 运费定价信息
     * @return 运费定价信息集合
     */
    public List<TransportShippingRate> selectTransportShippingRateList(TransportShippingRate transportShippingRate);

    /**
     * 新增运费定价信息
     * 
     * @param transportShippingRate 运费定价信息
     * @return 结果
     */
    public int insertTransportShippingRate(TransportShippingRate transportShippingRate);

    /**
     * 修改运费定价信息
     * 
     * @param transportShippingRate 运费定价信息
     * @return 结果
     */
    public int updateTransportShippingRate(TransportShippingRate transportShippingRate);

    /**
     * 更新运价信息（用于批量导入）
     * 
     * @param transportShippingRate 运费定价信息
     * @return 结果
     */
    int updateRate(TransportShippingRate transportShippingRate);

    /**
     * 删除运费定价信息
     * 
     * @param id 运费定价信息主键
     * @return 结果
     */
    public int deleteTransportShippingRateById(Long id);

    /**
     * 批量删除运费定价信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTransportShippingRateByIds(Long[] ids);

    /**
     * 根据唯一约束查询运费定价信息
     * 
     * @param startLocation 起点
     * @param endLocation 终点
     * @param rateType 规则类型
     * @param internalCodes 客户内部编号
     * @return 运费定价信息
     */
    TransportShippingRate findByUniqueConstraint(@Param("startLocation") String startLocation, 
                                                 @Param("endLocation") String endLocation, 
                                                 @Param("rateType") Integer rateType, 
                                                 @Param("internalCodes") String internalCodes);

    /**
     * 根据路线查询运价规则
     *
     * @param startCityCode 开始城市编码
     * @param endCityCode   结束城市编码
     * @return 运费定价信息列表
     */
    List<TransportShippingRate> getRatesByRoute(@Param("startCityCode") String startCityCode, @Param("endCityCode") String endCityCode);

    /**
     * 查找适用的运价规则
     *
     * @param startLocation 起点
     * @param endLocation 终点
     * @param productType 油品类型
     * @param internalCodes 客户内部编号
     * @return 运费定价信息
     */
    TransportShippingRate findApplicableRate(@Param("startLocation") String startLocation,
                                             @Param("endLocation") String endLocation,
                                             @Param("productType") String productType,
                                             @Param("internalCodes") String internalCodes);

    /**
     * 查找特定运价规则
     *
     * @param loadingPointId 装货点ID
     * @param consigneeId 收货方ID
     * @return 运费定价信息
     */
    TransportShippingRate findSpecificRate(@Param("loadingPointId") Long loadingPointId, @Param("consigneeId") Long consigneeId);

    /**
     * 查找默认运价规则
     *
     * @param loadingPointId 装货点ID
     * @return 运费定价信息
     */
    TransportShippingRate findDefaultRate(@Param("loadingPointId") Long loadingPointId);
}
