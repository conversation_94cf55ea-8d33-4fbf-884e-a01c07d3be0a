package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运输单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportOrderMapper {
    
    /**
     * 查询运输单
     * 
     * @param id 运输单主键
     * @return 运输单
     */
    public TransportOrder selectTransportOrderById(Long id);

    /**
     * 查询运输单列表
     * 
     * @param transportOrder 运输单
     * @return 运输单集合
     */
    public List<TransportOrder> selectTransportOrderList(TransportOrder transportOrder);

    /**
     * 新增运输单
     * 
     * @param transportOrder 运输单
     * @return 结果
     */
    public int insertTransportOrder(TransportOrder transportOrder);

    /**
     * 修改运输单
     * 
     * @param transportOrder 运输单
     * @return 结果
     */
    public int updateTransportOrder(TransportOrder transportOrder);

    /**
     * 删除运输单
     * 
     * @param id 运输单主键
     * @return 结果
     */
    public int deleteTransportOrderById(Long id);

    /**
     * 批量删除运输单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTransportOrderByIds(Long[] ids);

    /**
     * 根据日期获取最大运输单号
     * 
     * @param dateStr 日期字符串(yyyyMMdd)
     * @return 最大运输单号
     */
    public String getMaxOrderNoByDate(@Param("dateStr") String dateStr);

    /**
     * 根据日期获取最大内部编号
     * 
     * @param dateStr 日期字符串(yyyyMMdd)
     * @return 最大内部编号
     */
    public String getMaxInternalCodeByDate(@Param("dateStr") String dateStr);

    /**
     * 更新运输单状态
     * 
     * @param id 运输单ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 结果
     */
    public int updateOrderStatus(@Param("id") Long id, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 指派车辆和司机
     * 
     * @param id 运输单ID
     * @param vehicleId 车辆ID
     * @param driverId 司机ID
     * @param licensePlate 车牌号
     * @param updateBy 更新人
     * @return 结果
     */
    public int assignVehicleAndDriver(@Param("id") Long id, 
                                    @Param("vehicleId") Long vehicleId, 
                                    @Param("driverId") Long driverId, 
                                    @Param("licensePlate") String licensePlate, 
                                    @Param("updateBy") String updateBy);

    /**
     * 根据车辆ID查询运输单
     * 
     * @param vehicleId 车辆ID
     * @return 运输单列表
     */
    public List<TransportOrder> selectOrdersByVehicleId(@Param("vehicleId") Long vehicleId);

    /**
     * 根据司机ID查询运输单
     * 
     * @param driverId 司机ID
     * @return 运输单列表
     */
    public List<TransportOrder> selectOrdersByDriverId(@Param("driverId") Long driverId);

    /**
     * 根据客户ID查询运输单
     * 
     * @param customerId 客户ID
     * @return 运输单列表
     */
    public List<TransportOrder> selectOrdersByCustomerId(@Param("customerId") Long customerId);

    /**
     * 统计各状态运输单数量
     * 
     * @return 统计结果
     */
    public List<TransportOrder> selectOrderStatusStatistics();

    /**
     * 查询待指派的运输单
     * 
     * @return 运输单列表
     */
    public List<TransportOrder> selectPendingOrders();

    /**
     * 查询运输中的运输单
     * 
     * @return 运输单列表
     */
    public List<TransportOrder> selectTransportingOrders();

    /**
     * 根据运输单号查询运输单
     * 
     * @param orderNo 运输单号
     * @return 运输单
     */
    public TransportOrder selectTransportOrderByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 检查运输单号是否存在
     * 
     * @param orderNo 运输单号
     * @return 数量
     */
    public int checkOrderNoExists(@Param("orderNo") String orderNo);

    /**
     * 检查内部编号是否存在
     *
     * @param internalCode 内部编号
     * @return 数量
     */
    public int checkInternalCodeExists(@Param("internalCode") String internalCode);

    /**
     * 更新实际装货时间
     *
     * @param id 运输单ID
     * @param actualLoadingTime 实际装货时间
     * @return 结果
     */
    public int updateActualLoadingTime(@Param("id") Long id, @Param("actualLoadingTime") java.util.Date actualLoadingTime);

    /**
     * 更新装货完成时间
     *
     * @param id 运输单ID
     * @param loadingCompletedTime 装货完成时间
     * @return 结果
     */
    public int updateLoadingCompletedTime(@Param("id") Long id, @Param("loadingCompletedTime") java.util.Date loadingCompletedTime);

    /**
     * 更新出发时间
     *
     * @param id 运输单ID
     * @param departureTime 出发时间
     * @return 结果
     */
    public int updateDepartureTime(@Param("id") Long id, @Param("departureTime") java.util.Date departureTime);

    /**
     * 更新到达时间
     *
     * @param id 运输单ID
     * @param arrivalTime 到达时间
     * @return 结果
     */
    public int updateArrivalTime(@Param("id") Long id, @Param("arrivalTime") java.util.Date arrivalTime);

    /**
     * 更新实际送达时间
     *
     * @param id 运输单ID
     * @param actualDeliveryTime 实际送达时间
     * @return 结果
     */
    public int updateActualDeliveryTime(@Param("id") Long id, @Param("actualDeliveryTime") java.util.Date actualDeliveryTime);
}
