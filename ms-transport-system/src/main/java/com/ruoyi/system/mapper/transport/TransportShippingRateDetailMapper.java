package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportShippingRateDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运费定价杂费明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-25
 */
public interface TransportShippingRateDetailMapper {
    /**
     * 根据运价规则ID查询杂费明细列表
     *
     * @param rateId 运价规则ID
     * @return 杂费明细列表
     */
    List<TransportShippingRateDetail> selectByRateId(Long rateId);

    /**
     * 新增单条杂费明细
     *
     * @param detail 杂费明细
     * @return 结果
     */
    int insert(TransportShippingRateDetail detail);

    /**
     * 批量新增杂费明细
     *
     * @param details 杂费明细列表
     * @return 结果
     */
    int insertBatch(@Param("details") List<TransportShippingRateDetail> details);

    /**
     * 更新单条杂费明细
     *
     * @param detail 杂费明细
     * @return 结果
     */
    int update(TransportShippingRateDetail detail);

    /**
     * 根据ID删除单条杂费明细
     *
     * @param id 杂费明细ID
     * @return 结果
     */
    int delete(Long id);

    /**
     * 根据运价规则ID删除杂费明细
     *
     * @param rateId 运价规则ID
     * @return 结果
     */
    int deleteByRateId(Long rateId);
}
