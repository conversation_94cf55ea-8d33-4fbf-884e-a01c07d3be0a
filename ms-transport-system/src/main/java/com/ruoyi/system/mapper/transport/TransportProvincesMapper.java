package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportProvinces;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运输管理省份Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportProvincesMapper {
    
    /**
     * 查询运输管理省份列表
     * 
     * @param countryCode 国家编码
     * @return 运输管理省份集合
     */
    public List<TransportProvinces> selectTransportProvincesList(@Param("countryCode") String countryCode);

    /**
     * 根据省份编码查询省份信息
     * 
     * @param code 省份编码
     * @return 省份信息
     */
    public TransportProvinces selectTransportProvincesByCode(String code);

    List<TransportProvinces> selectList();
}
