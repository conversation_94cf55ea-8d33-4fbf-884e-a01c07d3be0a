package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportLoadingPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 装货点信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportLoadingPointMapper {
    
    /**
     * 查询装货点信息
     * 
     * @param id 装货点信息主键
     * @return 装货点信息
     */
    public TransportLoadingPoint selectTransportLoadingPointById(Long id);

    /**
     * 查询装货点信息列表
     * 
     * @param transportLoadingPoint 装货点信息
     * @return 装货点信息集合
     */
    public List<TransportLoadingPoint> selectTransportLoadingPointList(TransportLoadingPoint transportLoadingPoint);

    /**
     * 新增装货点信息
     * 
     * @param transportLoadingPoint 装货点信息
     * @return 结果
     */
    public int insertTransportLoadingPoint(TransportLoadingPoint transportLoadingPoint);

    /**
     * 修改装货点信息
     * 
     * @param transportLoadingPoint 装货点信息
     * @return 结果
     */
    public int updateTransportLoadingPoint(TransportLoadingPoint transportLoadingPoint);

    /**
     * 删除装货点信息
     * 
     * @param id 装货点信息主键
     * @return 结果
     */
    public int deleteTransportLoadingPointById(Long id);

    /**
     * 批量删除装货点信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTransportLoadingPointByIds(Long[] ids);

    /**
     * 根据装货点编码查询装货点
     * 
     * @param pointCode 装货点编码
     * @return 装货点信息
     */
    public TransportLoadingPoint selectLoadingPointByCode(@Param("pointCode") String pointCode);

    /**
     * 检查装货点编码是否存在
     * 
     * @param pointCode 装货点编码
     * @param id 装货点ID(修改时排除自己)
     * @return 数量
     */
    public int checkPointCodeExists(@Param("pointCode") String pointCode, @Param("id") Long id);

    /**
     * 查询启用的装货点列表
     * 
     * @return 装货点信息集合
     */
    public List<TransportLoadingPoint> selectActiveLoadingPoints();

    /**
     * 更新装货点状态
     * 
     * @param id 装货点ID
     * @param isActive 是否启用
     * @param updateBy 更新人
     * @return 结果
     */
    public int updateLoadingPointStatus(@Param("id") Long id, @Param("isActive") Integer isActive, @Param("updateBy") String updateBy);

    /**
     * 根据装货点类型查询装货点
     * 
     * @param pointType 装货点类型
     * @return 装货点信息集合
     */
    public List<TransportLoadingPoint> selectLoadingPointsByType(@Param("pointType") Integer pointType);

    /**
     * 根据地区查询装货点
     * 
     * @param countryCode 国家编码
     * @param provinceCode 省份编码
     * @param cityCode 城市编码
     * @return 装货点信息集合
     */
    public List<TransportLoadingPoint> selectLoadingPointsByRegion(@Param("countryCode") String countryCode, @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode);

    /**
     * 查询装货点信息列表，用于下拉框
     *
     * @param transportLoadingPoint 装货点信息
     * @return 装货点信息集合
     */
    public List<TransportLoadingPoint> selectLoadingPointOptions(TransportLoadingPoint transportLoadingPoint);
}
