package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportDistricts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运输管理区县Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportDistrictsMapper {
    
    /**
     * 查询运输管理区县列表
     * 
     * @param cityCode 城市编码
     * @return 运输管理区县集合
     */
    public List<TransportDistricts> selectTransportDistrictsList(@Param("cityCode") String cityCode);

    /**
     * 根据区县编码查询区县信息
     * 
     * @param code 区县编码
     * @return 区县信息
     */
    public TransportDistricts selectTransportDistrictsByCode(String code);
}
