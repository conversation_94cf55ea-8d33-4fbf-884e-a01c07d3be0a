package com.ruoyi.system.mapper.transport;

import com.ruoyi.system.domain.transport.TransportCities;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运输管理城市Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface TransportCitiesMapper {
    
    /**
     * 查询运输管理城市列表
     * 
     * @param provinceCode 省份编码
     * @return 运输管理城市集合
     */
    public List<TransportCities> selectTransportCitiesList(@Param("provinceCode") String provinceCode);

    /**
     * 根据城市编码查询城市信息
     * 
     * @param code 城市编码
     * @return 城市信息
     */
    public TransportCities selectTransportCitiesByCode(String code);

    List<TransportCities> selectList();
}
