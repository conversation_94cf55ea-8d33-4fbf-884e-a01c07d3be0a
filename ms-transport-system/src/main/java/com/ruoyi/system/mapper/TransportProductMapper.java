package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.TransportProduct;

/**
 * 油品商品表Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
public interface TransportProductMapper 
{
    /**
     * 查询油品商品表
     * 
     * @param id 油品商品表主键
     * @return 油品商品表
     */
    public TransportProduct selectTransportProductById(Long id);

    /**
     * 查询油品商品表列表
     * 
     * @param transportProduct 油品商品表
     * @return 油品商品表集合
     */
    public List<TransportProduct> selectTransportProductList(TransportProduct transportProduct);

    /**
     * 新增油品商品表
     * 
     * @param transportProduct 油品商品表
     * @return 结果
     */
    public int insertTransportProduct(TransportProduct transportProduct);

    /**
     * 修改油品商品表
     * 
     * @param transportProduct 油品商品表
     * @return 结果
     */
    public int updateTransportProduct(TransportProduct transportProduct);

    /**
     * 删除油品商品表
     * 
     * @param id 油品商品表主键
     * @return 结果
     */
    public int deleteTransportProductById(Long id);

    /**
     * 批量删除油品商品表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTransportProductByIds(Long[] ids);
}
