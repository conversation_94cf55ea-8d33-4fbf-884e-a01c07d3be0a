package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.TransportDriverDropdownDto;
import com.ruoyi.system.domain.transport.TransportDriver;

import java.util.List;

/**
 * 司机信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportDriverService {
    
    /**
     * 查询司机信息
     * 
     * @param id 司机信息主键
     * @return 司机信息
     */
    public TransportDriver selectTransportDriverById(Long id);

    /**
     * 查询司机信息列表
     * 
     * @param transportDriver 司机信息
     * @return 司机信息集合
     */
    public List<TransportDriver> selectTransportDriverList(TransportDriver transportDriver);

    /**
     * 新增司机信息
     * 
     * @param transportDriver 司机信息
     * @return 结果
     */
    public int insertTransportDriver(TransportDriver transportDriver);

    /**
     * 修改司机信息
     * 
     * @param transportDriver 司机信息
     * @return 结果
     */
    public int updateTransportDriver(TransportDriver transportDriver);

    /**
     * 批量删除司机信息
     * 
     * @param ids 需要删除的司机信息主键集合
     * @return 结果
     */
    public int deleteTransportDriverByIds(Long[] ids);

    /**
     * 删除司机信息
     * 
     * @param id 司机信息主键
     * @return 结果
     */
    public int deleteTransportDriverById(Long id);

    /**
     * 根据手机号查询司机
     * 
     * @param driverPhone 手机号
     * @return 司机信息
     */
    public TransportDriver selectDriverByPhone(String driverPhone);

    /**
     * 检查手机号是否存在
     * 
     * @param driverPhone 手机号
     * @param id 司机ID(修改时排除自己)
     * @return 是否存在
     */
    public boolean checkPhoneExists(String driverPhone, Long id);

    /**
     * 检查身份证号是否存在
     * 
     * @param idCard 身份证号
     * @param id 司机ID(修改时排除自己)
     * @return 是否存在
     */
    public boolean checkIdCardExists(String idCard, Long id);

    /**
     * 查询可用司机列表
     * 
     * @return 司机信息集合
     */
    public List<TransportDriver> selectAvailableDrivers();

    /**
     * 更新司机状态
     * 
     * @param id 司机ID
     * @param status 新状态
     * @return 结果
     */
    public int updateDriverStatus(Long id, Integer status);

    /**
     * 统计各状态司机数量
     * 
     * @return 统计结果
     */
    public List<TransportDriver> selectDriverStatusStatistics();

    /**
     * 根据驾照类型查询司机
     * 
     * @param licenseType 驾照类型
     * @return 司机信息集合
     */
    public List<TransportDriver> selectDriversByLicenseType(String licenseType);

    /**
     * 查询经验丰富的司机(驾龄大于指定年数)
     * 
     * @param years 驾龄年数
     * @return 司机信息集合
     */
    public List<TransportDriver> selectExperiencedDrivers(Integer years);

    /**
     * 查询司机下拉列表
     *
     * @param keyword 搜索关键字 (司机姓名或手机号)
     * @return 司机下拉列表
     */
    List<TransportDriverDropdownDto> selectDriverDropdownList(String keyword);
}
