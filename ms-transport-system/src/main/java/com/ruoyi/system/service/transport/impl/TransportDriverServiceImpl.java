package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.TransportDriverDropdownDto;
import com.ruoyi.system.domain.transport.TransportDriver;
import com.ruoyi.system.exception.transport.TransportErrorCode;
import com.ruoyi.system.exception.transport.TransportBusinessException;
import com.ruoyi.system.mapper.transport.TransportDriverMapper;
import com.ruoyi.system.service.transport.ITransportDriverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 司机信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportDriverServiceImpl implements ITransportDriverService {
    
    @Autowired
    private TransportDriverMapper transportDriverMapper;

    /**
     * 查询司机信息
     * 
     * @param id 司机信息主键
     * @return 司机信息
     */
    @Override
    public TransportDriver selectTransportDriverById(Long id) {
        return transportDriverMapper.selectTransportDriverById(id);
    }

    /**
     * 查询司机信息列表
     * 
     * @param transportDriver 司机信息
     * @return 司机信息
     */
    @Override
    public List<TransportDriver> selectTransportDriverList(TransportDriver transportDriver) {
        return transportDriverMapper.selectTransportDriverList(transportDriver);
    }

    /**
     * 新增司机信息
     * 
     * @param transportDriver 司机信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTransportDriver(TransportDriver transportDriver) {
        // 检查手机号是否已存在
        if (StringUtils.isNotEmpty(transportDriver.getDriverPhone()) &&
            checkPhoneExists(transportDriver.getDriverPhone(), null)) {
            throw TransportErrorCode.DRIVER_PHONE_EXISTS.createException();
        }

        // 检查身份证号是否已存在
        if (StringUtils.isNotEmpty(transportDriver.getIdCard()) &&
            checkIdCardExists(transportDriver.getIdCard(), null)) {
            throw TransportErrorCode.ID_CARD_EXISTS.createException();
        }
        
        transportDriver.setCreateBy(SecurityUtils.getUsername());
        transportDriver.setCreateTime(DateUtils.getNowDate());
        transportDriver.setIsDeleted(0);
        
        return transportDriverMapper.insertTransportDriver(transportDriver);
    }

    /**
     * 修改司机信息
     * 
     * @param transportDriver 司机信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTransportDriver(TransportDriver transportDriver) {
        // 检查手机号是否已存在
        if (StringUtils.isNotEmpty(transportDriver.getDriverPhone()) && 
            checkPhoneExists(transportDriver.getDriverPhone(), transportDriver.getId())) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 检查身份证号是否已存在
        if (StringUtils.isNotEmpty(transportDriver.getIdCard()) && 
            checkIdCardExists(transportDriver.getIdCard(), transportDriver.getId())) {
            throw new RuntimeException("身份证号已存在");
        }
        
        transportDriver.setUpdateBy(SecurityUtils.getUsername());
        transportDriver.setUpdateTime(DateUtils.getNowDate());
        
        return transportDriverMapper.updateTransportDriver(transportDriver);
    }

    /**
     * 批量删除司机信息
     * 
     * @param ids 需要删除的司机信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportDriverByIds(Long[] ids) {
        return transportDriverMapper.deleteTransportDriverByIds(ids);
    }

    /**
     * 删除司机信息
     * 
     * @param id 司机信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportDriverById(Long id) {
        return transportDriverMapper.deleteTransportDriverById(id);
    }

    /**
     * 根据手机号查询司机
     * 
     * @param driverPhone 手机号
     * @return 司机信息
     */
    @Override
    public TransportDriver selectDriverByPhone(String driverPhone) {
        return transportDriverMapper.selectDriverByPhone(driverPhone);
    }

    /**
     * 检查手机号是否存在
     * 
     * @param driverPhone 手机号
     * @param id 司机ID(修改时排除自己)
     * @return 是否存在
     */
    @Override
    public boolean checkPhoneExists(String driverPhone, Long id) {
        return transportDriverMapper.checkPhoneExists(driverPhone, id) > 0;
    }

    /**
     * 检查身份证号是否存在
     * 
     * @param idCard 身份证号
     * @param id 司机ID(修改时排除自己)
     * @return 是否存在
     */
    @Override
    public boolean checkIdCardExists(String idCard, Long id) {
        return transportDriverMapper.checkIdCardExists(idCard, id) > 0;
    }

    /**
     * 查询可用司机列表
     * 
     * @return 司机信息集合
     */
    @Override
    public List<TransportDriver> selectAvailableDrivers() {
        return transportDriverMapper.selectAvailableDrivers();
    }

    /**
     * 更新司机状态
     * 
     * @param id 司机ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateDriverStatus(Long id, Integer status) {
        return transportDriverMapper.updateDriverStatus(id, status, SecurityUtils.getUsername());
    }

    /**
     * 统计各状态司机数量
     * 
     * @return 统计结果
     */
    @Override
    public List<TransportDriver> selectDriverStatusStatistics() {
        return transportDriverMapper.selectDriverStatusStatistics();
    }

    /**
     * 根据驾照类型查询司机
     * 
     * @param licenseType 驾照类型
     * @return 司机信息集合
     */
    @Override
    public List<TransportDriver> selectDriversByLicenseType(String licenseType) {
        return transportDriverMapper.selectDriversByLicenseType(licenseType);
    }

    /**
     * 查询经验丰富的司机(驾龄大于指定年数)
     * 
     * @param years 驾龄年数
     * @return 司机信息集合
     */
    @Override
    public List<TransportDriver> selectExperiencedDrivers(Integer years) {
        return transportDriverMapper.selectExperiencedDrivers(years);
    }

    @Override
    public List<TransportDriverDropdownDto> selectDriverDropdownList(String keyword) {
        return transportDriverMapper.selectDriverDropdownList(keyword);
    }
}
