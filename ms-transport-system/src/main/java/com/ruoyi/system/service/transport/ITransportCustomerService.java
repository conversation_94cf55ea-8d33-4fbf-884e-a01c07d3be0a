package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.TransportCustomerDropdownDto;
import com.ruoyi.system.domain.transport.TransportCustomer;

import java.util.List;
import java.util.Map;

/**
 * 客户信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportCustomerService {
    
    /**
     * 查询客户信息
     * 
     * @param id 客户信息主键
     * @return 客户信息
     */
    public TransportCustomer selectTransportCustomerById(Long id);

    /**
     * 查询客户信息列表
     * 
     * @param transportCustomer 客户信息
     * @return 客户信息集合
     */
    public List<TransportCustomer> selectTransportCustomerList(TransportCustomer transportCustomer);

    /**
     * 新增客户信息
     * 
     * @param transportCustomer 客户信息
     * @return 结果
     */
    public int insertTransportCustomer(TransportCustomer transportCustomer);

    /**
     * 修改客户信息
     * 
     * @param transportCustomer 客户信息
     * @return 结果
     */
    public int updateTransportCustomer(TransportCustomer transportCustomer);

    /**
     * 批量删除客户信息
     * 
     * @param ids 需要删除的客户信息主键集合
     * @return 结果
     */
    public int deleteTransportCustomerByIds(Long[] ids);

    /**
     * 删除客户信息
     * 
     * @param id 客户信息主键
     * @return 结果
     */
    public int deleteTransportCustomerById(Long id);

    /**
     * 检查客户编码是否存在
     *
     * @param customerCode 客户编码
     * @param id           客户ID(修改时排除自己)
     * @return 是否存在
     */
    boolean checkCustomerCodeExists(String customerCode, Long id);

    /**
     * 检查客户名称是否存在
     *
     * @param customerName 客户名称
     * @param id           客户ID(修改时排除自己)
     * @return 是否存在
     */
    boolean checkCustomerNameExists(String customerName, Long id);

    /**
     * 查询客户下拉列表
     *
     * @param keyword 搜索关键字 (客户编码或客户名称)
     * @return 客户下拉列表
     */
    List<TransportCustomerDropdownDto> selectCustomerDropdownList(String keyword);

    /**
     * 根据ID批量查询客户信息列表
     *
     * @param ids 客户ID列表
     * @return 客户信息集合
     */
    List<TransportCustomer> listCustomersByIds(List<Long> ids);

}
