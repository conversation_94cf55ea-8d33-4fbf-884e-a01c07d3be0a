package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.transport.TransportConsignee;

import java.util.List;

/**
 * 收货方信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportConsigneeService {
    
    /**
     * 查询收货方信息
     * 
     * @param id 收货方信息主键
     * @return 收货方信息
     */
    public TransportConsignee selectTransportConsigneeById(Long id);

    /**
     * 查询收货方信息列表
     * 
     * @param transportConsignee 收货方信息
     * @return 收货方信息集合
     */
    public List<TransportConsignee> selectTransportConsigneeList(TransportConsignee transportConsignee);

    /**
     * 新增收货方信息
     * 
     * @param transportConsignee 收货方信息
     * @return 结果
     */
    public int insertTransportConsignee(TransportConsignee transportConsignee);

    /**
     * 修改收货方信息
     * 
     * @param transportConsignee 收货方信息
     * @return 结果
     */
    public int updateTransportConsignee(TransportConsignee transportConsignee);

    /**
     * 批量删除收货方信息
     * 
     * @param ids 需要删除的收货方信息主键集合
     * @return 结果
     */
    public int deleteTransportConsigneeByIds(Long[] ids);

    /**
     * 删除收货方信息
     * 
     * @param id 收货方信息主键
     * @return 结果
     */
    public int deleteTransportConsigneeById(Long id);

    /**
     * 根据客户ID查询收货方列表
     * 
     * @param customerId 客户ID
     * @return 收货方信息集合
     */
    public List<TransportConsignee> selectConsigneesByCustomerId(Long customerId);

    /**
     * 根据收货方编码查询收货方
     * 
     * @param consigneeCode 收货方编码
     * @return 收货方信息
     */
    public TransportConsignee selectConsigneeByCode(String consigneeCode);

    /**
     * 检查收货方编码是否存在
     * 
     * @param consigneeCode 收货方编码
     * @param id 收货方ID(修改时排除自己)
     * @return 是否存在
     */
    public boolean checkConsigneeCodeExists(String consigneeCode, Long id);

    /**
     * 查询启用的收货方列表
     * 
     * @return 收货方信息集合
     */
    public List<TransportConsignee> selectActiveConsignees();

    /**
     * 更新收货方状态
     * 
     * @param id 收货方ID
     * @param isActive 是否启用
     * @return 结果
     */
    public int updateConsigneeStatus(Long id, Integer isActive);

    /**
     * 根据收货方类型查询收货方
     * 
     * @param consigneeType 收货方类型
     * @return 收货方信息集合
     */
    public List<TransportConsignee> selectConsigneesByType(Integer consigneeType);

    /**
     * 根据地区查询收货方
     * 
     * @param countryCode 国家编码
     * @param provinceCode 省份编码
     * @param cityCode 城市编码
     * @return 收货方信息集合
     */
    public List<TransportConsignee> selectConsigneesByRegion(String countryCode, String provinceCode, String cityCode);

    /**
     * 根据收货方编码查询收货方列表
     *
     * @param codes 收货方编码
     * @return 收货方信息
     */
    List<TransportConsignee> listConsigneeByCodes(List<String> codes);
}
