package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.dto.TransportReceiptsDetails;
import com.ruoyi.system.domain.dto.TransportShippingRateDetails;
import com.ruoyi.system.domain.transport.TransportReceipts;
import com.ruoyi.system.domain.transport.TransportShippingRate;
import com.ruoyi.system.domain.vo.TransportCustomerReceiptsSummaryVO;
import com.ruoyi.system.service.transport.ITransportExcelProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Excel处理服务实现类
 *
 * <AUTHOR>
 */
@Service
public class TransportExcelProcessServiceImpl implements ITransportExcelProcessService {

    private static final Logger log = LoggerFactory.getLogger(TransportExcelProcessServiceImpl.class);

    @Override
    public void exportShippingRate(List<TransportShippingRate> shippingRates, HttpServletResponse response) {
        try {
            ExcelUtil<TransportShippingRate> util = new ExcelUtil<>(TransportShippingRate.class);
            util.exportExcel(response, shippingRates, "运费定价数据");
        } catch (Exception e) {
            log.error("导出运费定价Excel异常", e);
            throw new ServiceException("导出Excel失败，请联系管理员");
        }
    }

    @Override
    public List<TransportShippingRateDetails> importExcelForShippingRate(MultipartFile file) {
        try {
            ExcelUtil<TransportShippingRateDetails> util = new ExcelUtil<>(TransportShippingRateDetails.class);
            return util.importExcel(file.getInputStream());
        } catch (Exception e) {
            log.error("导入运费定价Excel异常", e);
            throw new ServiceException("导入Excel失败，请联系管理员");
        }
    }

    @Override
    public void exportTransportReceipts(List<TransportReceipts> receipts, String customerCode, HttpServletResponse response) {
        try {
            ExcelUtil<TransportReceipts> util = new ExcelUtil<>(TransportReceipts.class);
            String fileName = "运输对账单";
            if (customerCode != null && !customerCode.isEmpty()) {
                fileName = customerCode + "_运输对账单";
            }
            util.exportExcel(response, receipts, fileName);
        } catch (Exception e) {
            log.error("导出运输对账Excel异常", e);
            throw new ServiceException("导出Excel失败，请联系管理员");
        }
    }

    @Override
    public List<TransportReceiptsDetails> importExcelForTransportReceipts(MultipartFile file) {
        try {
            ExcelUtil<TransportReceiptsDetails> util = new ExcelUtil<>(TransportReceiptsDetails.class);
            return util.importExcel(file.getInputStream());
        } catch (Exception e) {
            log.error("导入运输对账Excel异常", e);
            throw new ServiceException("导入Excel失败，请联系管理员");
        }
    }

    @Override
    public void exportCustomerPayableSummary(List<TransportCustomerReceiptsSummaryVO> dataList, HttpServletResponse response) {
        try {
            ExcelUtil<TransportCustomerReceiptsSummaryVO> util = new ExcelUtil<>(TransportCustomerReceiptsSummaryVO.class);
            util.exportExcel(response, dataList, "委托客户账期汇总");
        } catch (Exception e) {
            log.error("导出委托客户账期汇总Excel异常", e);
            throw new ServiceException("导出Excel失败，请联系管理员");
        }
    }
}
