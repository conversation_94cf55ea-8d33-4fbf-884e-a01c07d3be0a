package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.transport.TransportConsignee;
import com.ruoyi.system.domain.transport.TransportCustomer;
import com.ruoyi.system.mapper.transport.TransportConsigneeMapper;
import com.ruoyi.system.service.transport.ITransportConsigneeService;
import com.ruoyi.system.service.transport.ITransportCustomerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 收货方信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportConsigneeServiceImpl implements ITransportConsigneeService {
    
    @Autowired
    private TransportConsigneeMapper transportConsigneeMapper;

    @Autowired
    private ITransportCustomerService transportCustomerService;

    /**
     * 查询收货方信息
     * 
     * @param id 收货方信息主键
     * @return 收货方信息
     */
    @Override
    public TransportConsignee selectTransportConsigneeById(Long id) {
        return transportConsigneeMapper.selectTransportConsigneeById(id);
    }

    /**
     * 查询收货方信息列表
     * 
     * @param transportConsignee 收货方信息
     * @return 收货方信息
     */
    @Override
    public List<TransportConsignee> selectTransportConsigneeList(TransportConsignee transportConsignee) {
        List<TransportConsignee> consignees = transportConsigneeMapper.selectTransportConsigneeList(transportConsignee);
        if (CollectionUtils.isEmpty(consignees)) {
            return Collections.emptyList();
        }

        // 1. Get all customer IDs
        List<Long> customerIds = consignees.stream()
                .map(TransportConsignee::getCustomerId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(customerIds)) {
            return consignees;
        }

        // 2. Fetch all customers
        List<TransportCustomer> customers = transportCustomerService.listCustomersByIds(customerIds);
        if (CollectionUtils.isEmpty(customers)) {
            return consignees;
        }

        // 3. Create a map for easy lookup
        Map<Long, String> customerMap = customers.stream()
                .collect(Collectors.toMap(TransportCustomer::getId, TransportCustomer::getCustomerName));

        // 4. Populate customerName
        consignees.forEach(c -> {
            if (c.getCustomerId() != null) {
                c.setCustomerName(customerMap.get(c.getCustomerId()));
            }
        });

        return consignees;
    }

    /**
     * 新增收货方信息
     * 
     * @param transportConsignee 收货方信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTransportConsignee(TransportConsignee transportConsignee) {
        // 检查收货方编码是否已存在
        if (StringUtils.isNotEmpty(transportConsignee.getConsigneeCode()) && 
            checkConsigneeCodeExists(transportConsignee.getConsigneeCode(), null)) {
            throw new RuntimeException("收货方编码已存在");
        }
        
        transportConsignee.setCreateBy(SecurityUtils.getUsername());
        transportConsignee.setCreateTime(DateUtils.getNowDate());
        transportConsignee.setIsDeleted(0);
        
        return transportConsigneeMapper.insertTransportConsignee(transportConsignee);
    }

    /**
     * 修改收货方信息
     * 
     * @param transportConsignee 收货方信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTransportConsignee(TransportConsignee transportConsignee) {
        // 检查收货方编码是否已存在
        if (StringUtils.isNotEmpty(transportConsignee.getConsigneeCode()) && 
            checkConsigneeCodeExists(transportConsignee.getConsigneeCode(), transportConsignee.getId())) {
            throw new RuntimeException("收货方编码已存在");
        }
        
        transportConsignee.setUpdateBy(SecurityUtils.getUsername());
        transportConsignee.setUpdateTime(DateUtils.getNowDate());
        
        return transportConsigneeMapper.updateTransportConsignee(transportConsignee);
    }

    /**
     * 批量删除收货方信息
     * 
     * @param ids 需要删除的收货方信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportConsigneeByIds(Long[] ids) {
        return transportConsigneeMapper.deleteTransportConsigneeByIds(ids);
    }

    /**
     * 删除收货方信息
     * 
     * @param id 收货方信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportConsigneeById(Long id) {
        return transportConsigneeMapper.deleteTransportConsigneeById(id);
    }

    /**
     * 根据客户ID查询收货方列表
     * 
     * @param customerId 客户ID
     * @return 收货方信息集合
     */
    @Override
    public List<TransportConsignee> selectConsigneesByCustomerId(Long customerId) {
        return transportConsigneeMapper.selectConsigneesByCustomerId(customerId);
    }

    /**
     * 根据收货方编码查询收货方
     * 
     * @param consigneeCode 收货方编码
     * @return 收货方信息
     */
    @Override
    public TransportConsignee selectConsigneeByCode(String consigneeCode) {
        return transportConsigneeMapper.selectConsigneeByCode(consigneeCode);
    }

    /**
     * 检查收货方编码是否存在
     * 
     * @param consigneeCode 收货方编码
     * @param id 收货方ID(修改时排除自己)
     * @return 是否存在
     */
    @Override
    public boolean checkConsigneeCodeExists(String consigneeCode, Long id) {
        return transportConsigneeMapper.checkConsigneeCodeExists(consigneeCode, id) > 0;
    }

    /**
     * 查询启用的收货方列表
     * 
     * @return 收货方信息集合
     */
    @Override
    public List<TransportConsignee> selectActiveConsignees() {
        return transportConsigneeMapper.selectActiveConsignees();
    }

    /**
     * 更新收货方状态
     * 
     * @param id 收货方ID
     * @param isActive 是否启用
     * @return 结果
     */
    @Override
    @Transactional
    public int updateConsigneeStatus(Long id, Integer isActive) {
        return transportConsigneeMapper.updateConsigneeStatus(id, isActive, SecurityUtils.getUsername());
    }

    /**
     * 根据收货方类型查询收货方
     * 
     * @param consigneeType 收货方类型
     * @return 收货方信息集合
     */
    @Override
    public List<TransportConsignee> selectConsigneesByType(Integer consigneeType) {
        return transportConsigneeMapper.selectConsigneesByType(consigneeType);
    }

    /**
     * 根据地区查询收货方
     * 
     * @param countryCode 国家编码
     * @param provinceCode 省份编码
     * @param cityCode 城市编码
     * @return 收货方信息集合
     */
    @Override
    public List<TransportConsignee> selectConsigneesByRegion(String countryCode, String provinceCode, String cityCode) {
        return transportConsigneeMapper.selectConsigneesByRegion(countryCode, provinceCode, cityCode);
    }

    @Override
    public List<TransportConsignee> listConsigneeByCodes(List<String> codes) {
        return transportConsigneeMapper.listConsigneeByCodes(codes);
    }
}
