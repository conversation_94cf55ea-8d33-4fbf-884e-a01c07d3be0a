package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.transport.TransportShippingRateDetail;
import com.ruoyi.system.mapper.transport.TransportShippingRateDetailMapper;
import com.ruoyi.system.service.transport.ITransportShippingRateDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 运费定价杂费细则信息表 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-28
 */
@Service
public class TransportShippingRateDetailServiceImpl implements ITransportShippingRateDetailService {
    @Autowired
    private TransportShippingRateDetailMapper transportShippingRateDetailMapper;

    @Override
    public List<TransportShippingRateDetail> selectByRateId(Long rateId) {
        return transportShippingRateDetailMapper.selectByRateId(rateId);
    }

    @Override
    public int insertTransportShippingRateDetail(TransportShippingRateDetail detail) {
        detail.setCreateTime(DateUtils.getNowDate());
        return transportShippingRateDetailMapper.insert(detail);
    }

    @Override
    public int updateTransportShippingRateDetail(TransportShippingRateDetail detail) {
        detail.setUpdateTime(DateUtils.getNowDate());
        return transportShippingRateDetailMapper.update(detail);
    }

    @Override
    public int deleteTransportShippingRateDetailById(Long id) {
        return transportShippingRateDetailMapper.delete(id);
    }

    @Override
    public int deleteByRateId(Long rateId) {
        return transportShippingRateDetailMapper.deleteByRateId(rateId);
    }
}
