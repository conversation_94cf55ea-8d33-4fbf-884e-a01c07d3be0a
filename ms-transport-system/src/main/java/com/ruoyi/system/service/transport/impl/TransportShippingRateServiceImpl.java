package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.RateDetailsResult;
import com.ruoyi.system.domain.dto.TransportShippingRateDetails;
import com.ruoyi.system.domain.transport.TransportConsignee;
import com.ruoyi.system.domain.transport.TransportCustomer;
import com.ruoyi.system.domain.transport.TransportLoadingPoint;
import com.ruoyi.system.domain.transport.TransportShippingRate;
import com.ruoyi.system.domain.transport.TransportShippingRateDetail;
import com.ruoyi.system.mapper.transport.TransportShippingRateDetailMapper;
import com.ruoyi.system.mapper.transport.TransportShippingRateMapper;
import com.ruoyi.system.service.transport.ITransportConsigneeService;
import com.ruoyi.system.service.transport.ITransportCustomerService;
import com.ruoyi.system.service.transport.ITransportLoadingPointService;
import com.ruoyi.system.service.transport.ITransportShippingRateService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 运费定价信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportShippingRateServiceImpl implements ITransportShippingRateService {
    private final static String DEFAULT_INTERNAL_CODE = "默认";

    private final TransportShippingRateMapper transportShippingRateMapper;
    private final TransportShippingRateDetailMapper transportShippingRateDetailMapper;
    private final ITransportConsigneeService transportConsigneeService;
    private final ITransportLoadingPointService transportLoadingPointService;
    private final ITransportCustomerService transportCustomerService;

    public TransportShippingRateServiceImpl(TransportShippingRateMapper transportShippingRateMapper,
                                          TransportShippingRateDetailMapper transportShippingRateDetailMapper,
                                          ITransportConsigneeService transportConsigneeService,
                                          ITransportLoadingPointService transportLoadingPointService,
                                          ITransportCustomerService transportCustomerService) {
        this.transportShippingRateMapper = transportShippingRateMapper;
        this.transportShippingRateDetailMapper = transportShippingRateDetailMapper;
        this.transportConsigneeService = transportConsigneeService;
        this.transportLoadingPointService = transportLoadingPointService;
        this.transportCustomerService = transportCustomerService;
    }

    @Override
    public TransportShippingRate findById(Long id) {
        TransportShippingRate rate = transportShippingRateMapper.selectTransportShippingRateById(id);
        if (rate == null) {
            throw new ServiceException("运费规则不存在");
        }
        rate.setOtherFeeList(transportShippingRateDetailMapper.selectByRateId(id));

        // 获取 internalCodes 字段
        String internalCodes = rate.getInternalCodes();
        if (StringUtils.isNotBlank(internalCodes)) {
            // 分割 internalCodes 字符串
            List<String> consigneeCodes = Arrays.stream(internalCodes.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(consigneeCodes)) {
                // 根据收货方编号查询收货方信息
                List<TransportConsignee> consignees = transportConsigneeService.listConsigneeByCodes(consigneeCodes);

                if (CollectionUtils.isNotEmpty(consignees)) {
                    // 转换成前端需要的格式
                    List<Map<String, Object>> consigneeInfoList = consignees.stream()
                            .map(consignee -> {
                                Map<String, Object> info = new HashMap<>();
                                info.put("customerId", consignee.getCustomerId());
                                info.put("consigneeCode", consignee.getConsigneeCode());
                                return info;
                            })
                            .collect(Collectors.toList());
                    rate.setConsigneeInfo(consigneeInfoList);
                }
            }
        }

        return rate;
    }

    @Override
    public List<TransportShippingRate> findAllByCondition(TransportShippingRate transportShippingRate) {
        List<TransportShippingRate> list = transportShippingRateMapper.selectTransportShippingRateList(transportShippingRate);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> internalCodes = list.stream()
                .map(TransportShippingRate::getInternalCodes)
                .filter(StringUtils::isNotBlank)
                .flatMap(code -> Arrays.stream(code.split(",")))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(internalCodes)) {
            return defaultTransportShippingRateList(list);
        }

        List<TransportConsignee> consignees = transportConsigneeService.listConsigneeByCodes(internalCodes);
        if (CollectionUtils.isEmpty(consignees)) {
            return defaultTransportShippingRateList(list);
        }

        // Get all customer IDs from consignees
        Set<Long> customerIds = consignees.stream()
                .map(TransportConsignee::getCustomerId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, String> customerMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(customerIds)) {
            // Fetch all customers in one go
            List<TransportCustomer> customers = transportCustomerService.listCustomersByIds(new ArrayList<>(customerIds));
            if (CollectionUtils.isNotEmpty(customers)) {
                customerMap = customers.stream()
                        .collect(Collectors.toMap(TransportCustomer::getId, TransportCustomer::getCustomerName));
            }
        }

        // Create a map from consignee code to "customerName-consigneeName" string.
        Map<Long, String> finalCustomerMap = customerMap;
        Map<String, String> consigneeDisplayMap = consignees.stream()
                .collect(Collectors.toMap(
                        TransportConsignee::getConsigneeCode,
                        c -> {
                            String customerName = finalCustomerMap.get(c.getCustomerId());
                            String consigneeName = c.getConsigneeName();
                            if (StringUtils.isNotBlank(customerName)) {
                                return customerName + "-" + consigneeName;
                            }
                            return consigneeName; // Fallback to just consignee name
                        },
                        (v1, v2) -> v1 // In case of duplicate consignee codes
                ));

        if (MapUtils.isEmpty(consigneeDisplayMap)) {
            return defaultTransportShippingRateList(list);
        }

        list.forEach(rate -> {
            if (Constants.ONE.equals(rate.getRateType())) {
                String codes = rate.getInternalCodes();
                if (StringUtils.isBlank(codes)) {
                    rate.setRateTypeAndInternalCodes("");
                    return;
                }
                String rateTypeAndInternalCodes = Arrays.stream(codes.split(","))
                        .map(String::trim)
                        .map(consigneeDisplayMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                rate.setRateTypeAndInternalCodes(rateTypeAndInternalCodes);
            } else {
                rate.setRateTypeAndInternalCodes(DEFAULT_INTERNAL_CODE);
            }
        });
        return list;
    }

    private List<TransportShippingRate> defaultTransportShippingRateList(List<TransportShippingRate> list) {
        list.forEach(rate -> rate.setRateTypeAndInternalCodes(DEFAULT_INTERNAL_CODE));
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(TransportShippingRate transportShippingRate) {
        populateLoadingPointInfo(transportShippingRate);
        checkRate(transportShippingRate);
        int rows = transportShippingRateMapper.insertTransportShippingRate(transportShippingRate);
        if (rows > 0 && CollectionUtils.isNotEmpty(transportShippingRate.getOtherFeeList())) {
            for (TransportShippingRateDetail detail : transportShippingRate.getOtherFeeList()) {
                detail.setRateId(transportShippingRate.getId());
            }
            transportShippingRateDetailMapper.insertBatch(transportShippingRate.getOtherFeeList());
        }
        return rows;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TransportShippingRate transportShippingRate) {
        populateLoadingPointInfo(transportShippingRate);
        checkRate(transportShippingRate);
        Long rateId = transportShippingRate.getId();
        List<TransportShippingRateDetail> existingDetails = transportShippingRateDetailMapper.selectByRateId(rateId);
        List<TransportShippingRateDetail> newDetails = transportShippingRate.getOtherFeeList();

        Map<Long, TransportShippingRateDetail> existingMap = existingDetails.stream().collect(Collectors.toMap(TransportShippingRateDetail::getId, Function.identity()));
        Map<Long, TransportShippingRateDetail> newMap = newDetails.stream().filter(d -> d.getId() != null).collect(Collectors.toMap(TransportShippingRateDetail::getId, Function.identity()));

        // Delete details not in new list
        for (TransportShippingRateDetail existing : existingDetails) {
            if (!newMap.containsKey(existing.getId())) {
                transportShippingRateDetailMapper.delete(existing.getId());
            }
        }

        // Insert new details
        for (TransportShippingRateDetail newDetail : newDetails) {
            if (newDetail.getId() == null) {
                newDetail.setRateId(rateId);
                transportShippingRateDetailMapper.insert(newDetail);
            }
        }

        // Update existing details
        for (TransportShippingRateDetail newDetail : newDetails) {
            if (newDetail.getId() != null && existingMap.containsKey(newDetail.getId())) {
                transportShippingRateDetailMapper.update(newDetail);
            }
        }

        return transportShippingRateMapper.updateTransportShippingRate(transportShippingRate);
    }

    private void populateLoadingPointInfo(TransportShippingRate transportShippingRate) {
        if (transportShippingRate.getLoadingPointId() != null) {
            TransportLoadingPoint loadingPoint = transportLoadingPointService.selectTransportLoadingPointById(transportShippingRate.getLoadingPointId());
            if (loadingPoint != null) {
                transportShippingRate.setLoadingPointName(loadingPoint.getPointName());
                transportShippingRate.setStartLocation(loadingPoint.getDetailAddress());
                transportShippingRate.setStartCityCode(loadingPoint.getCityCode());
            } else {
                throw new ServiceException("指定的装货点不存在");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteTransportShippingRateByIds(Long[] ids) {
        for (Long id : ids) {
            transportShippingRateDetailMapper.deleteByRateId(id);
        }
        return transportShippingRateMapper.deleteTransportShippingRateByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        transportShippingRateDetailMapper.deleteByRateId(id);
        return transportShippingRateMapper.deleteTransportShippingRateById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<TransportShippingRateDetails> details) {
        for (TransportShippingRateDetails detail : details) {
            if (Objects.isNull(detail.getId())) {
                throw new ServiceException("批量修改运价信息时，ID不能为空");
            }
        }
        List<Long> ids = details.stream().map(TransportShippingRateDetails::getId).collect(Collectors.toList());
        List<TransportShippingRate> rates = transportShippingRateMapper.listByIds(ids);
        if (CollectionUtils.isEmpty(rates)) {
            throw new ServiceException("批量修改运价信息时，未找到对应的运价规则");
        }
        Map<Long, TransportShippingRate> rateMap = rates.stream().collect(Collectors.toMap(TransportShippingRate::getId, Function.identity()));
        for (TransportShippingRateDetails detail : details) {
            TransportShippingRate rate = rateMap.get(detail.getId());
            if (rate != null) {
                rate.setFreightUnit(detail.getFreightUnit());
                rate.setTaxIncluded(detail.getTaxIncluded());
                rate.setMinimumVolume(detail.getMinimumVolume());
                rate.setContractFreightUnit(detail.getContractFreightUnit());
                rate.setContractTaxIncluded(detail.getContractTaxIncluded());
                if (transportShippingRateMapper.updateRate(rate) < 1) {
                    throw new ServiceException("批量修改运价信息失败");
                }
            }
        }
    }

    @Override
    public List<TransportShippingRate> listShippingRateData(TransportShippingRate condition) {
        return this.findAllByCondition(condition);
    }

    @Override
    public TransportShippingRate findApplicableRate(String startLocation, String endLocation, String productType, String internalCodes) {
        return transportShippingRateMapper.findApplicableRate(startLocation, endLocation, productType, internalCodes);
    }

    @Override
    public RateDetailsResult getRateDetails(Long loadingPointId, Long consigneeId, BigDecimal volume) {
        TransportShippingRate rate = findShippingRate(loadingPointId, consigneeId);
        if (rate == null) {
            throw new ServiceException("未找到适用的运价规则");
        }

        BigDecimal freightUnit = rate.getTaxIncluded();
        if (freightUnit == null || freightUnit.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("运价规则中的单价配置不正确");
        }

        BigDecimal minimumVolume = rate.getMinimumVolume();
        BigDecimal effectiveVolume = volume;
        if (minimumVolume != null && volume.compareTo(minimumVolume) < 0) {
            effectiveVolume = minimumVolume;
        }

        BigDecimal totalPrice = effectiveVolume.multiply(freightUnit);
        return new RateDetailsResult(totalPrice, rate.getId());
    }

    private TransportShippingRate findShippingRate(Long loadingPointId, Long consigneeId) {
        // 1. 查找特定规则
        TransportShippingRate specificRate = transportShippingRateMapper.findSpecificRate(loadingPointId, consigneeId);
        if (specificRate != null) {
            // 找到特定规则，返回
            specificRate.setOtherFeeList(transportShippingRateDetailMapper.selectByRateId(specificRate.getId()));
            return specificRate;
        }

        // 2. 如果没有找到特定规则，查找默认规则
        TransportShippingRate defaultRate = transportShippingRateMapper.findDefaultRate(loadingPointId);
        if (defaultRate != null) {
            defaultRate.setOtherFeeList(transportShippingRateDetailMapper.selectByRateId(defaultRate.getId()));
        }
        return defaultRate;
    }

    private void checkRate(TransportShippingRate oilShippingRate) {
        Integer rateType = oilShippingRate.getRateType();
        if (null == rateType) {
            throw new ServiceException("运费定价标签不能为空");
        }
        String startCityCode = oilShippingRate.getStartCityCode();
        String endCityCode = oilShippingRate.getEndCityCode();
        if (StringUtils.isBlank(startCityCode) || StringUtils.isBlank(endCityCode)) {
            throw new ServiceException("运费定价路线不能为空");
        }
        List<TransportShippingRate> rates = transportShippingRateMapper.getRatesByRoute(startCityCode, endCityCode);
        if (CollectionUtils.isEmpty(rates)) {
            if (rateType == 1) {
                throw new ServiceException("当前线路没有配置默认的定价规则，请先配置");
            }
            return;
        }
        validateRule(oilShippingRate, rates);
    }

    public void validateRule(TransportShippingRate newRate, List<TransportShippingRate> existingRates) {
        List<TransportShippingRate> filteredRates = existingRates.stream()
                .filter(rate -> !rate.getId().equals(newRate.getId()))
                .collect(Collectors.toList());

        if (newRate.getRateType() == 0) {
            for (TransportShippingRate existingRate : filteredRates) {
                if (isSameRoute(newRate, existingRate) && existingRate.getRateType() == 0) {
                    throw new ServiceException("当前线路已经配置了默认规则，请先删除默认规则");
                }
            }
        } else if (newRate.getRateType() == 1) {
            for (TransportShippingRate existingRate : filteredRates) {
                if (isSameRoute(newRate, existingRate)) {
                    List<String> newCustomers = getCustomerList(newRate.getInternalCodes());
                    List<String> existingCustomers = getCustomerList(existingRate.getInternalCodes());
                    if (hasCommonCustomers(newCustomers, existingCustomers)) {
                        throw new ServiceException("当前线路的特定运价规则中的客户已经在配置过了，请检查后再配置！");
                    }
                }
            }
        }
    }

    private boolean isSameRoute(TransportShippingRate rate1, TransportShippingRate rate2) {
        return rate1.getStartCityCode().equals(rate2.getStartCityCode())
                && rate1.getEndCityCode().equals(rate2.getEndCityCode());
    }

    private List<String> getCustomerList(String internalCodes) {
        if (StringUtils.isEmpty(internalCodes)) {
            return new ArrayList<>();
        }
        return Arrays.asList(internalCodes.split(","));
    }

    private boolean hasCommonCustomers(List<String> list1, List<String> list2) {
        for (String customer : list1) {
            if (list2.contains(customer)) {
                return true;
            }
        }
        return false;
    }
}
