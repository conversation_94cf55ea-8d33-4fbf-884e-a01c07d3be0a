package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.AddressDTO;
import com.ruoyi.system.domain.transport.TransportCities;
import com.ruoyi.system.domain.transport.TransportCountries;
import com.ruoyi.system.domain.transport.TransportDistricts;
import com.ruoyi.system.domain.transport.TransportProvinces;

import java.util.List;

/**
 * 运输管理地址Service接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface TransportCountriesService {

    /**
     * 查询国家列表
     *
     * @return 国家列表
     */
    List<TransportCountries> selectCountriesList();

    /**
     * 查询省份列表
     *
     * @param countryCode 国家编码
     * @return 省份列表
     */
    List<TransportProvinces> selectProvincesList(String countryCode);

    /**
     * 查询城市列表
     *
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    List<TransportCities> selectCitiesList(String provinceCode);

    /**
     * 查询区县列表
     *
     * @param cityCode 城市编码
     * @return 区县列表
     */
    List<TransportDistricts> selectDistrictsList(String cityCode);

    /**
     * 根据国家编码查询国家信息
     *
     * @param code 国家编码
     * @return 国家信息
     */
    TransportCountries selectCountriesByCode(String code);

    /**
     * 根据省份编码查询省份信息
     *
     * @param code 省份编码
     * @return 省份信息
     */
    TransportProvinces selectProvincesByCode(String code);

    /**
     * 根据城市编码查询城市信息
     *
     * @param code 城市编码
     * @return 城市信息
     */
    TransportCities selectCitiesByCode(String code);

    /**
     * 根据区县编码查询区县信息
     *
     * @param code 区县编码
     * @return 区县信息
     */
    TransportDistricts selectDistrictsByCode(String code);

    List<AddressDTO> listAddress();
}
