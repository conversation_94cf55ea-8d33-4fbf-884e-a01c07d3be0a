package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.transport.TransportOrder;

/**
 * 运输单状态管理服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportOrderStatusService {
    
    /**
     * 运输单状态枚举
     */
    enum OrderStatus {
        PENDING_ASSIGN(1, "待指派"),
        ASSIGNED(2, "已指派"),
        TO_LOADING(3, "前往装货"),
        LOADING(4, "装货中"),
        TRANSPORTING(5, "运输中"),
        DELIVERED(6, "已送达"),
        BILLED(7, "已对账");
        
        private final int code;
        private final String desc;
        
        OrderStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
        public int getCode() {
            return code;
        }
        
        public String getDesc() {
            return desc;
        }
        
        public static OrderStatus fromCode(int code) {
            for (OrderStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的订单状态: " + code);
        }
    }
    
    /**
     * 检查状态流转是否合法
     * 
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否合法
     */
    boolean isValidStatusTransition(Integer currentStatus, Integer targetStatus);
    
    /**
     * 更新运输单状态
     * 
     * @param orderId 运输单ID
     * @param targetStatus 目标状态
     * @return 结果
     */
    int updateOrderStatus(Long orderId, Integer targetStatus);
    
    /**
     * 开始装货
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    int startLoading(Long orderId);
    
    /**
     * 完成装货
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    int completeLoading(Long orderId);
    
    /**
     * 开始运输
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    int startTransporting(Long orderId);
    
    /**
     * 完成配送
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    int completeDelivery(Long orderId);
    
    /**
     * 完成对账
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    int completeBilling(Long orderId);
    
    /**
     * 获取状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    String getStatusDescription(Integer status);
    
    /**
     * 获取下一个可能的状态列表
     * 
     * @param currentStatus 当前状态
     * @return 下一个可能的状态列表
     */
    OrderStatus[] getNextPossibleStatuses(Integer currentStatus);
}
