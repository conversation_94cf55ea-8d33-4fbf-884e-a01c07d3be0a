package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TransportProductMapper;
import com.ruoyi.system.domain.TransportProduct;
import com.ruoyi.system.service.ITransportProductService;

/**
 * 油品商品表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
@Service
public class TransportProductServiceImpl implements ITransportProductService 
{
    @Autowired
    private TransportProductMapper transportProductMapper;

    /**
     * 查询油品商品表
     * 
     * @param id 油品商品表主键
     * @return 油品商品表
     */
    @Override
    public TransportProduct selectTransportProductById(Long id)
    {
        return transportProductMapper.selectTransportProductById(id);
    }

    /**
     * 查询油品商品表列表
     * 
     * @param transportProduct 油品商品表
     * @return 油品商品表
     */
    @Override
    public List<TransportProduct> selectTransportProductList(TransportProduct transportProduct)
    {
        return transportProductMapper.selectTransportProductList(transportProduct);
    }

    /**
     * 新增油品商品表
     * 
     * @param transportProduct 油品商品表
     * @return 结果
     */
    @Override
    public int insertTransportProduct(TransportProduct transportProduct)
    {
        transportProduct.setCreateTime(DateUtils.getNowDate());
        return transportProductMapper.insertTransportProduct(transportProduct);
    }

    /**
     * 修改油品商品表
     * 
     * @param transportProduct 油品商品表
     * @return 结果
     */
    @Override
    public int updateTransportProduct(TransportProduct transportProduct)
    {
        transportProduct.setUpdateTime(DateUtils.getNowDate());
        return transportProductMapper.updateTransportProduct(transportProduct);
    }

    /**
     * 批量删除油品商品表
     * 
     * @param ids 需要删除的油品商品表主键
     * @return 结果
     */
    @Override
    public int deleteTransportProductByIds(Long[] ids)
    {
        return transportProductMapper.deleteTransportProductByIds(ids);
    }

    /**
     * 删除油品商品表信息
     * 
     * @param id 油品商品表主键
     * @return 结果
     */
    @Override
    public int deleteTransportProductById(Long id)
    {
        return transportProductMapper.deleteTransportProductById(id);
    }
}
