package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.transport.TransportShippingRateDetail;

import java.util.List;

/**
 * 运费定价杂费细则信息表 Service接口
 *
 * <AUTHOR>
 * @date 2024-01-28
 */
public interface ITransportShippingRateDetailService {
    /**
     * 根据运价规则ID查询杂费明细列表
     *
     * @param rateId 运价规则ID
     * @return 杂费明细列表
     */
    public List<TransportShippingRateDetail> selectByRateId(Long rateId);

    /**
     * 新增单条杂费明细
     *
     * @param detail 杂费明细
     * @return 结果
     */
    public int insertTransportShippingRateDetail(TransportShippingRateDetail detail);

    /**
     * 更新单条杂费明细
     *
     * @param detail 杂费明细
     * @return 结果
     */
    public int updateTransportShippingRateDetail(TransportShippingRateDetail detail);

    /**
     * 根据ID删除单条杂费明细
     *
     * @param id 杂费明细ID
     * @return 结果
     */
    public int deleteTransportShippingRateDetailById(Long id);

    /**
     * 根据运价规则ID删除杂费明细
     *
     * @param rateId 运价规则ID
     * @return 结果
     */
    public int deleteByRateId(Long rateId);
}
