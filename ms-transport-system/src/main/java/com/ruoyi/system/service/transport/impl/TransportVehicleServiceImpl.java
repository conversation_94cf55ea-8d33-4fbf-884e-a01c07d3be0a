package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.TransportVehicleDropdownDto;
import com.ruoyi.system.domain.transport.TransportVehicle;
import com.ruoyi.system.mapper.transport.TransportVehicleMapper;
import com.ruoyi.system.service.transport.ITransportVehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 车辆信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportVehicleServiceImpl implements ITransportVehicleService {
    
    @Autowired
    private TransportVehicleMapper transportVehicleMapper;

    /**
     * 查询车辆信息
     * 
     * @param id 车辆信息主键
     * @return 车辆信息
     */
    @Override
    public TransportVehicle selectTransportVehicleById(Long id) {
        return transportVehicleMapper.selectTransportVehicleById(id);
    }

    /**
     * 查询车辆信息列表
     * 
     * @param transportVehicle 车辆信息
     * @return 车辆信息
     */
    @Override
    public List<TransportVehicle> selectTransportVehicleList(TransportVehicle transportVehicle) {
        return transportVehicleMapper.selectTransportVehicleList(transportVehicle);
    }

    /**
     * 新增车辆信息
     * 
     * @param transportVehicle 车辆信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTransportVehicle(TransportVehicle transportVehicle) {
        // 检查车牌号是否已存在
        if (checkLicensePlateExists(transportVehicle.getLicensePlate(), null)) {
            throw new RuntimeException("车牌号已存在");
        }
        
        transportVehicle.setCreateBy(SecurityUtils.getUsername());
        transportVehicle.setCreateTime(DateUtils.getNowDate());
        transportVehicle.setIsDeleted(0);
        
        return transportVehicleMapper.insertTransportVehicle(transportVehicle);
    }

    /**
     * 修改车辆信息
     * 
     * @param transportVehicle 车辆信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTransportVehicle(TransportVehicle transportVehicle) {
        // 检查车牌号是否已存在
        if (checkLicensePlateExists(transportVehicle.getLicensePlate(), transportVehicle.getId())) {
            throw new RuntimeException("车牌号已存在");
        }
        
        transportVehicle.setUpdateBy(SecurityUtils.getUsername());
        transportVehicle.setUpdateTime(DateUtils.getNowDate());
        
        return transportVehicleMapper.updateTransportVehicle(transportVehicle);
    }

    /**
     * 批量删除车辆信息
     * 
     * @param ids 需要删除的车辆信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportVehicleByIds(Long[] ids) {
        return transportVehicleMapper.deleteTransportVehicleByIds(ids);
    }

    /**
     * 删除车辆信息
     * 
     * @param id 车辆信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportVehicleById(Long id) {
        return transportVehicleMapper.deleteTransportVehicleById(id);
    }

    /**
     * 根据车牌号查询车辆
     * 
     * @param licensePlate 车牌号
     * @return 车辆信息
     */
    @Override
    public TransportVehicle selectVehicleByLicensePlate(String licensePlate) {
        return transportVehicleMapper.selectVehicleByLicensePlate(licensePlate);
    }

    /**
     * 检查车牌号是否存在
     * 
     * @param licensePlate 车牌号
     * @param id 车辆ID(修改时排除自己)
     * @return 是否存在
     */
    @Override
    public boolean checkLicensePlateExists(String licensePlate, Long id) {
        return transportVehicleMapper.checkLicensePlateExists(licensePlate, id) > 0;
    }

    /**
     * 查询可用车辆列表
     * 
     * @return 车辆信息集合
     */
    @Override
    public List<TransportVehicle> selectAvailableVehicles() {
        return transportVehicleMapper.selectAvailableVehicles();
    }

    /**
     * 更新车辆状态
     * 
     * @param id 车辆ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateVehicleStatus(Long id, Integer status) {
        return transportVehicleMapper.updateVehicleStatus(id, status, SecurityUtils.getUsername());
    }

    /**
     * 根据区域代码查询车辆
     * 
     * @param regionCode 区域代码
     * @return 车辆信息集合
     */
    @Override
    public List<TransportVehicle> selectVehiclesByRegion(String regionCode) {
        return transportVehicleMapper.selectVehiclesByRegion(regionCode);
    }

    /**
     * 统计各状态车辆数量
     * 
     * @return 统计结果
     */
    @Override
    public List<TransportVehicle> selectVehicleStatusStatistics() {
        return transportVehicleMapper.selectVehicleStatusStatistics();
    }

    /**
     * 查询即将到期的车辆(年检、保险)
     * 
     * @param days 提前天数
     * @return 车辆信息集合
     */
    @Override
    public List<TransportVehicle> selectExpiringVehicles(Integer days) {
        return transportVehicleMapper.selectExpiringVehicles(days);
    }

    @Override
    public List<TransportVehicleDropdownDto> selectVehicleDropdownList(String keyword) {
        return transportVehicleMapper.selectVehicleDropdownList(keyword);
    }
}
