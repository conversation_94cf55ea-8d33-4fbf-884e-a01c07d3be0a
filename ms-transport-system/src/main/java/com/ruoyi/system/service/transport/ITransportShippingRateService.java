package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.TransportShippingRateDetails;
import com.ruoyi.system.domain.transport.TransportShippingRate;
import com.ruoyi.system.domain.dto.RateDetailsResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运费定价信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportShippingRateService 
{
    /**
     * 查询运费定价信息
     * 
     * @param id 运费定价信息主键
     * @return 运费定价信息
     */
    public TransportShippingRate findById(Long id);

    /**
     * 查询运费定价信息列表
     * 
     * @param transportShippingRate 运费定价信息
     * @return 运费定价信息集合
     */
    public List<TransportShippingRate> findAllByCondition(TransportShippingRate transportShippingRate);

    /**
     * 新增运费定价信息
     * 
     * @param transportShippingRate 运费定价信息
     * @return 结果
     */
    public int insert(TransportShippingRate transportShippingRate);

    /**
     * 修改运费定价信息
     * 
     * @param transportShippingRate 运费定价信息
     * @return 结果
     */
    public int update(TransportShippingRate transportShippingRate);

    /**
     * 批量删除运费定价信息
     * 
     * @param ids 需要删除的运费定价信息主键集合
     * @return 结果
     */
    public int deleteTransportShippingRateByIds(Long[] ids);

    /**
     * 删除运费定价信息信息
     * 
     * @param id 运费定价信息主键
     * @return 结果
     */
    public int delete(Long id);

    /**
     * 批量更新运费定价信息（用于Excel导入）
     *
     * @param details 运费定价详情列表
     */
    void batchUpdate(List<TransportShippingRateDetails> details);

    /**
     * 查询用于导出的运费定价数据
     *
     * @param condition 查询条件
     * @return 运费定价信息集合
     */
    List<TransportShippingRate> listShippingRateData(TransportShippingRate condition);

    /**
     * 查找适用的运价规则
     *
     * @param startLocation 起点
     * @param endLocation 终点
     * @param productType 油品类型
     * @param internalCodes 客户内部编号
     * @return 运费定价信息
     */
    TransportShippingRate findApplicableRate(String startLocation,
                                             String endLocation,
                                             String productType,
                                             String internalCodes);

    /**
     * 根据装货点id和收货方id获取运价规则明细并计算运费
     *
     * @param loadingPointId 装货点id
     * @param consigneeId 收货方id
     * @param volume 运输体积
     * @return 计算后的运费和命中的规则id
     */
    RateDetailsResult getRateDetails(Long loadingPointId, Long consigneeId, BigDecimal volume);
}
