package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.TransportCustomerDropdownDto;
import com.ruoyi.system.domain.transport.TransportCustomer;
import com.ruoyi.system.mapper.transport.TransportCustomerMapper;
import com.ruoyi.system.service.transport.ITransportCustomerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * 客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportCustomerServiceImpl implements ITransportCustomerService {

    @Autowired
    private TransportCustomerMapper transportCustomerMapper;

    /**
     * 查询客户信息
     *
     * @param id 客户信息主键
     * @return 客户信息
     */
    @Override
    public TransportCustomer selectTransportCustomerById(Long id) {
        return transportCustomerMapper.selectTransportCustomerById(id);
    }

    /**
     * 查询客户信息列表
     *
     * @param transportCustomer 客户信息
     * @return 客户信息
     */
    @Override
    public List<TransportCustomer> selectTransportCustomerList(TransportCustomer transportCustomer) {
        return transportCustomerMapper.selectTransportCustomerList(transportCustomer);
    }

    /**
     * 新增客户信息
     *
     * @param transportCustomer 客户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTransportCustomer(TransportCustomer transportCustomer) {
        // 检查客户编码是否已存在
        if (StringUtils.isNotEmpty(transportCustomer.getCustomerCode()) &&
                checkCustomerCodeExists(transportCustomer.getCustomerCode(), null)) {
            throw new RuntimeException("客户编码已存在");
        }

        // 检查客户名称是否已存在
        if (checkCustomerNameExists(transportCustomer.getCustomerName(), null)) {
            throw new RuntimeException("客户名称已存在");
        }

        transportCustomer.setCreateBy(SecurityUtils.getUsername());
        transportCustomer.setCreateTime(DateUtils.getNowDate());
        transportCustomer.setIsDeleted(0);

        return transportCustomerMapper.insertTransportCustomer(transportCustomer);
    }

    /**
     * 修改客户信息
     *
     * @param transportCustomer 客户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTransportCustomer(TransportCustomer transportCustomer) {
        // 检查客户编码是否已存在
        if (StringUtils.isNotEmpty(transportCustomer.getCustomerCode()) &&
                checkCustomerCodeExists(transportCustomer.getCustomerCode(), transportCustomer.getId())) {
            throw new RuntimeException("客户编码已存在");
        }

        // 检查客户名称是否已存在
        if (checkCustomerNameExists(transportCustomer.getCustomerName(), transportCustomer.getId())) {
            throw new RuntimeException("客户名称已存在");
        }

        transportCustomer.setUpdateBy(SecurityUtils.getUsername());
        transportCustomer.setUpdateTime(DateUtils.getNowDate());

        return transportCustomerMapper.updateTransportCustomer(transportCustomer);
    }

    /**
     * 批量删除客户信息
     *
     * @param ids 需要删除的客户信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportCustomerByIds(Long[] ids) {
        return transportCustomerMapper.deleteTransportCustomerByIds(ids);
    }

    /**
     * 删除客户信息
     *
     * @param id 客户信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportCustomerById(Long id) {
        return transportCustomerMapper.deleteTransportCustomerById(id);
    }

    /**
     * 检查客户编码是否存在
     *
     * @param customerCode 客户编码
     * @param id           客户ID(修改时排除自己)
     * @return 是否存在
     */
    @Override
    public boolean checkCustomerCodeExists(String customerCode, Long id) {
        return transportCustomerMapper.checkCustomerCodeExists(customerCode, id) > 0;
    }

    /**
     * 检查客户名称是否存在
     *
     * @param customerName 客户名称
     * @param id           客户ID(修改时排除自己)
     * @return 是否存在
     */
    @Override
    public boolean checkCustomerNameExists(String customerName, Long id) {
        return transportCustomerMapper.checkCustomerNameExists(customerName, id) > 0;
    }

    @Override
    public List<TransportCustomerDropdownDto> selectCustomerDropdownList(String keyword) {
        return transportCustomerMapper.selectCustomerDropdownList(keyword);
    }

    @Override
    public List<TransportCustomer> listCustomersByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return transportCustomerMapper.selectCustomersByIds(ids);
    }
}
