package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.domain.dto.AddressDTO;
import com.ruoyi.system.domain.transport.TransportCities;
import com.ruoyi.system.domain.transport.TransportCountries;
import com.ruoyi.system.domain.transport.TransportDistricts;
import com.ruoyi.system.domain.transport.TransportProvinces;
import com.ruoyi.system.mapper.transport.TransportCitiesMapper;
import com.ruoyi.system.mapper.transport.TransportCountriesMapper;
import com.ruoyi.system.mapper.transport.TransportDistrictsMapper;
import com.ruoyi.system.mapper.transport.TransportProvincesMapper;
import com.ruoyi.system.service.transport.TransportCountriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 运输管理地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportCountriesServiceImpl implements TransportCountriesService {

    @Autowired
    private TransportCountriesMapper transportCountriesMapper;

    @Autowired
    private TransportProvincesMapper transportProvincesMapper;

    @Autowired
    private TransportCitiesMapper transportCitiesMapper;

    @Autowired
    private TransportDistrictsMapper transportDistrictsMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询国家列表
     *
     * @return 国家列表
     */
    @Override
    public List<TransportCountries> selectCountriesList() {
        List<TransportCountries> countries = transportCountriesMapper.selectTransportCountriesList();
        for (TransportCountries country : countries) {
            List<TransportProvinces> provinces = transportProvincesMapper.selectTransportProvincesList(country.getCode());
            country.setProvinces(provinces);

            for (TransportProvinces province : provinces) {
                List<TransportCities> cities = transportCitiesMapper.selectTransportCitiesList(province.getCode());
                province.setCities(cities);

                for (TransportCities city : cities) {
                    List<TransportDistricts> districts = transportDistrictsMapper.selectTransportDistrictsList(city.getCode());
                    city.setDistricts(districts);
                }
            }
        }
        return countries;
    }

    /**
     * 查询省份列表
     *
     * @param countryCode 国家编码
     * @return 省份列表
     */
    @Override
    public List<TransportProvinces> selectProvincesList(String countryCode) {
        List<TransportProvinces> provinces = transportProvincesMapper.selectTransportProvincesList(countryCode);
        for (TransportProvinces province : provinces) {
            List<TransportCities> cities = transportCitiesMapper.selectTransportCitiesList(province.getCode());
            province.setCities(cities);

            for (TransportCities city : cities) {
                List<TransportDistricts> districts = transportDistrictsMapper.selectTransportDistrictsList(city.getCode());
                city.setDistricts(districts);
            }
        }
        return provinces;
    }

    /**
     * 查询城市列表
     *
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    @Override
    public List<TransportCities> selectCitiesList(String provinceCode) {
        List<TransportCities> cities = transportCitiesMapper.selectTransportCitiesList(provinceCode);
        for (TransportCities city : cities) {
            List<TransportDistricts> districts = transportDistrictsMapper.selectTransportDistrictsList(city.getCode());
            city.setDistricts(districts);
        }
        return cities;
    }

    /**
     * 查询区县列表
     *
     * @param cityCode 城市编码
     * @return 区县列表
     */
    @Override
    public List<TransportDistricts> selectDistrictsList(String cityCode) {
        return transportDistrictsMapper.selectTransportDistrictsList(cityCode);
    }

    /**
     * 根据国家编码查询国家信息
     *
     * @param code 国家编码
     * @return 国家信息
     */
    @Override
    public TransportCountries selectCountriesByCode(String code) {
        return transportCountriesMapper.selectTransportCountriesByCode(code);
    }

    /**
     * 根据省份编码查询省份信息
     *
     * @param code 省份编码
     * @return 省份信息
     */
    @Override
    public TransportProvinces selectProvincesByCode(String code) {
        return transportProvincesMapper.selectTransportProvincesByCode(code);
    }

    /**
     * 根据城市编码查询城市信息
     *
     * @param code 城市编码
     * @return 城市信息
     */
    @Override
    public TransportCities selectCitiesByCode(String code) {
        return transportCitiesMapper.selectTransportCitiesByCode(code);
    }

    /**
     * 根据区县编码查询区县信息
     *
     * @param code 区县编码
     * @return 区县信息
     */
    @Override
    public TransportDistricts selectDistrictsByCode(String code) {
        return transportDistrictsMapper.selectTransportDistrictsByCode(code);
    }

    @Override
    public List<AddressDTO> listAddress() {
        List<AddressDTO> countryNodes = redisCache.getCacheObject(CacheConstants.ADDRESS_TREE_KEY);
        if (countryNodes != null) {
            return countryNodes;
        }

        // 查询所有国家数据
        List<TransportCountries> allCountries = transportCountriesMapper.selectList();
        // 查询所有省份数据
        List<TransportProvinces> allProvinces = transportProvincesMapper.selectList();
        // 查询所有城市数据
        List<TransportCities> allCities = transportCitiesMapper.selectList();
        // 用于快速查找国家、省份的 AddressDTO 对象
        Map<String, AddressDTO> countryMap = new HashMap<>();
        Map<String, AddressDTO> provinceMap = new HashMap<>();

        // 构建国家节点
        countryNodes = new ArrayList<>();
        for (TransportCountries country : allCountries) {
            AddressDTO countryDTO = new AddressDTO(country.getCode(), country.getName());
            countryNodes.add(countryDTO);
            countryMap.put(country.getCode(), countryDTO);
        }

        // 构建省份节点并添加到对应的国家节点下
        for (TransportProvinces province : allProvinces) {
            AddressDTO provinceDTO = new AddressDTO(province.getCode(), province.getName());
            provinceMap.put(province.getCode(), provinceDTO);

            AddressDTO countryDTO = countryMap.get(province.getCountryCode());
            if (countryDTO != null) {
                countryDTO.getChildren().add(provinceDTO);
            }
        }

        // 构建城市节点并添加到对应的省份节点下
        for (TransportCities city : allCities) {
            AddressDTO cityDTO = new AddressDTO(city.getCode(), city.getName());

            AddressDTO provinceDTO = provinceMap.get(city.getProvinceCode());
            if (provinceDTO != null) {
                provinceDTO.getChildren().add(cityDTO);
            }
        }

       // redisCache.setCacheObject(CacheConstants.ADDRESS_TREE_KEY, countryNodes, 24, TimeUnit.HOURS);
        return countryNodes;
    }
}
