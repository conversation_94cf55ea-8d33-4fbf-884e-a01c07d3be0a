package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.TransportReceiptsDetails;
import com.ruoyi.system.domain.dto.TransportShippingRateDetails;
import com.ruoyi.system.domain.transport.TransportReceipts;
import com.ruoyi.system.domain.transport.TransportShippingRate;
import com.ruoyi.system.domain.vo.TransportCustomerReceiptsSummaryVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Excel处理服务接口
 *
 * <AUTHOR>
 */
public interface ITransportExcelProcessService {

    /**
     * 导出运费定价
     *
     * @param shippingRates 运费定价列表
     * @param response      HttpServletResponse
     */
    void exportShippingRate(List<TransportShippingRate> shippingRates, HttpServletResponse response);

    /**
     * 导入运费定价
     *
     * @param file Excel文件
     * @return 运费定价详情列表
     */
    List<TransportShippingRateDetails> importExcelForShippingRate(MultipartFile file);

    /**
     * 导出运输对账
     *
     * @param receipts 运输对账列表
     * @param customerCode 客户编号
     * @param response HttpServletResponse
     */
    void exportTransportReceipts(List<TransportReceipts> receipts, String customerCode, HttpServletResponse response);

    /**
     * 导入运输对账
     *
     * @param file Excel文件
     * @return 运输对账详情列表
     */
    List<TransportReceiptsDetails> importExcelForTransportReceipts(MultipartFile file);

    /**
     * 导出客户账期汇总数据
     *
     * @param dataList 数据列表
     * @param response HttpServletResponse
     */
    void exportCustomerPayableSummary(List<TransportCustomerReceiptsSummaryVO> dataList, HttpServletResponse response);
}
