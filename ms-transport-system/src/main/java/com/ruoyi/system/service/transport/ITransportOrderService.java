package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.transport.TransportOrder;

import java.util.List;

/**
 * 运输单Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportOrderService {
    
    /**
     * 查询运输单
     * 
     * @param id 运输单主键
     * @return 运输单
     */
    public TransportOrder selectTransportOrderById(Long id);

    /**
     * 查询运输单列表
     * 
     * @param transportOrder 运输单
     * @return 运输单集合
     */
    public List<TransportOrder> selectTransportOrderList(TransportOrder transportOrder);

    /**
     * 新增运输单
     * 
     * @param transportOrder 运输单
     * @return 结果
     */
    public int insertTransportOrder(TransportOrder transportOrder);

    /**
     * 修改运输单
     * 
     * @param transportOrder 运输单
     * @return 结果
     */
    public int updateTransportOrder(TransportOrder transportOrder);

    /**
     * 批量删除运输单
     * 
     * @param ids 需要删除的运输单主键集合
     * @return 结果
     */
    public int deleteTransportOrderByIds(Long[] ids);

    /**
     * 删除运输单信息
     * 
     * @param id 运输单主键
     * @return 结果
     */
    public int deleteTransportOrderById(Long id);

    /**
     * 生成运输单号
     * 
     * @return 运输单号
     */
    public String generateOrderNo();

    /**
     * 生成内部编号
     * 
     * @return 内部编号
     */
    public String generateInternalCode();

    /**
     * 更新运输单状态
     * 
     * @param id 运输单ID
     * @param status 新状态
     * @return 结果
     */
    public int updateOrderStatus(Long id, Integer status);

    /**
     * 指派车辆和司机
     * 
     * @param id 运输单ID
     * @param vehicleId 车辆ID
     * @param driverId 司机ID
     * @return 结果
     */
    public int assignVehicleAndDriver(Long id, Long vehicleId, Long driverId);

    /**
     * 根据车辆ID查询运输单
     * 
     * @param vehicleId 车辆ID
     * @return 运输单列表
     */
    public List<TransportOrder> selectOrdersByVehicleId(Long vehicleId);

    /**
     * 根据司机ID查询运输单
     * 
     * @param driverId 司机ID
     * @return 运输单列表
     */
    public List<TransportOrder> selectOrdersByDriverId(Long driverId);

    /**
     * 根据客户ID查询运输单
     * 
     * @param customerId 客户ID
     * @return 运输单列表
     */
    public List<TransportOrder> selectOrdersByCustomerId(Long customerId);

    /**
     * 统计各状态运输单数量
     * 
     * @return 统计结果
     */
    public List<TransportOrder> selectOrderStatusStatistics();

    /**
     * 查询待指派的运输单
     * 
     * @return 运输单列表
     */
    public List<TransportOrder> selectPendingOrders();

    /**
     * 查询运输中的运输单
     * 
     * @return 运输单列表
     */
    public List<TransportOrder> selectTransportingOrders();

    /**
     * 根据运输单号查询运输单
     * 
     * @param orderNo 运输单号
     * @return 运输单
     */
    public TransportOrder selectTransportOrderByOrderNo(String orderNo);

    /**
     * 检查运输单号是否存在
     * 
     * @param orderNo 运输单号
     * @return 是否存在
     */
    public boolean checkOrderNoExists(String orderNo);

    /**
     * 检查内部编号是否存在
     * 
     * @param internalCode 内部编号
     * @return 是否存在
     */
    public boolean checkInternalCodeExists(String internalCode);

    /**
     * 验证运输单数据
     * 
     * @param transportOrder 运输单
     * @return 验证结果
     */
    public String validateTransportOrder(TransportOrder transportOrder);
}
