package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.transport.TransportCustomer;
import com.ruoyi.system.domain.transport.TransportOrder;
import com.ruoyi.system.domain.transport.TransportReceipts;
import com.ruoyi.system.mapper.transport.TransportCustomerMapper;
import com.ruoyi.system.mapper.transport.TransportOrderMapper;
import com.ruoyi.system.mapper.transport.TransportVehicleMapper;
import com.ruoyi.system.service.transport.ITransportOrderStatusService;
import com.ruoyi.system.service.transport.ITransportReceiptsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 运输单状态管理服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportOrderStatusServiceImpl implements ITransportOrderStatusService {

    private static final Logger log = LoggerFactory.getLogger(TransportOrderStatusServiceImpl.class);

    @Autowired
    private TransportOrderMapper transportOrderMapper;

    @Autowired
    private TransportVehicleMapper transportVehicleMapper;

    @Autowired
    private TransportCustomerMapper transportCustomerMapper;

    @Autowired
    private ITransportReceiptsService transportReceiptsService;

    /**
     * 检查状态流转是否合法
     * 
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否合法
     */
    @Override
    public boolean isValidStatusTransition(Integer currentStatus, Integer targetStatus) {
        if (currentStatus == null || targetStatus == null) {
            return false;
        }
        
        // 定义合法的状态流转规则
        switch (currentStatus) {
            case 1: // 待指派
                return targetStatus == 2; // 只能到已指派
            case 2: // 已指派
                return targetStatus == 3; // 只能到前往装货
            case 3: // 前往装货
                return targetStatus == 4; // 只能到装货中
            case 4: // 装货中
                return targetStatus == 5; // 只能到运输中
            case 5: // 运输中
                return targetStatus == 6; // 只能到已送达
            case 6: // 已送达
                return targetStatus == 7; // 只能到已对账
            case 7: // 已对账
                return false; // 终态，不能再流转
            default:
                return false;
        }
    }

    /**
     * 更新运输单状态
     * 
     * @param orderId 运输单ID
     * @param targetStatus 目标状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderStatus(Long orderId, Integer targetStatus) {
        // 1. 查询当前运输单
        TransportOrder order = transportOrderMapper.selectTransportOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("运输单不存在");
        }
        
        // 2. 检查状态流转是否合法
        if (!isValidStatusTransition(order.getOrderStatus(), targetStatus)) {
            throw new RuntimeException("状态流转不合法，当前状态：" + getStatusDescription(order.getOrderStatus()) + 
                                     "，目标状态：" + getStatusDescription(targetStatus));
        }
        
        // 3. 更新状态
        int result = transportOrderMapper.updateOrderStatus(orderId, targetStatus, SecurityUtils.getUsername());
        
        // 4. 根据状态更新相关时间字段
        updateTimeFields(orderId, targetStatus);
        
        // 5. 更新车辆状态
        updateVehicleStatus(order, targetStatus);
        
        return result;
    }
    
    /**
     * 根据状态更新时间字段
     */
    private void updateTimeFields(Long orderId, Integer status) {
        Date now = DateUtils.getNowDate();
        
        switch (status) {
            case 4: // 装货中
                transportOrderMapper.updateActualLoadingTime(orderId, now);
                break;
            case 5: // 运输中
                transportOrderMapper.updateLoadingCompletedTime(orderId, now);
                transportOrderMapper.updateDepartureTime(orderId, now);
                break;
            case 6: // 已送达
                transportOrderMapper.updateArrivalTime(orderId, now);
                transportOrderMapper.updateActualDeliveryTime(orderId, now);
                break;
        }
    }
    
    /**
     * 更新车辆状态
     */
    private void updateVehicleStatus(TransportOrder order, Integer orderStatus) {
        if (order.getVehicleId() == null) {
            return;
        }
        
        Integer vehicleStatus;
        switch (orderStatus) {
            case 2: // 已指派
            case 3: // 前往装货
            case 4: // 装货中
            case 5: // 运输中
                vehicleStatus = 2; // 运输中
                break;
            case 6: // 已送达
            case 7: // 已对账
                vehicleStatus = 1; // 空闲
                break;
            default:
                return;
        }
        
        transportVehicleMapper.updateVehicleStatus(order.getVehicleId(), vehicleStatus, SecurityUtils.getUsername());
    }

    /**
     * 开始装货
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int startLoading(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.LOADING.getCode());
    }

    /**
     * 完成装货
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeLoading(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.TRANSPORTING.getCode());
    }

    /**
     * 开始运输
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int startTransporting(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.TRANSPORTING.getCode());
    }

    /**
     * 完成配送
     *
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeDelivery(Long orderId) {
        // 1. 更新运输单状态为已送达
        int result = updateOrderStatus(orderId, OrderStatus.DELIVERED.getCode());

        // 2. 如果状态更新成功，自动生成对账单信息
        if (result > 0) {
            try {
                generateReceiptsForOrder(orderId);
                log.info("运输单[{}]配送完成，已自动生成对账单", orderId);
            } catch (Exception e) {
                log.error("运输单[{}]配送完成后生成对账单失败", orderId, e);
                // 注意：这里不抛出异常，避免影响主流程
                // 对账单生成失败不应该回滚运输单状态更新
            }
        }

        return result;
    }

    /**
     * 完成对账
     * 
     * @param orderId 运输单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeBilling(Long orderId) {
        return updateOrderStatus(orderId, OrderStatus.BILLED.getCode());
    }

    /**
     * 获取状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    @Override
    public String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        try {
            return OrderStatus.fromCode(status).getDesc();
        } catch (IllegalArgumentException e) {
            return "未知";
        }
    }

    /**
     * 获取下一个可能的状态列表
     * 
     * @param currentStatus 当前状态
     * @return 下一个可能的状态列表
     */
    @Override
    public OrderStatus[] getNextPossibleStatuses(Integer currentStatus) {
        if (currentStatus == null) {
            return new OrderStatus[0];
        }
        
        switch (currentStatus) {
            case 1: // 待指派
                return new OrderStatus[]{OrderStatus.ASSIGNED};
            case 2: // 已指派
                return new OrderStatus[]{OrderStatus.TO_LOADING};
            case 3: // 前往装货
                return new OrderStatus[]{OrderStatus.LOADING};
            case 4: // 装货中
                return new OrderStatus[]{OrderStatus.TRANSPORTING};
            case 5: // 运输中
                return new OrderStatus[]{OrderStatus.DELIVERED};
            case 6: // 已送达
                return new OrderStatus[]{OrderStatus.BILLED};
            case 7: // 已对账
                return new OrderStatus[0]; // 终态
            default:
                return new OrderStatus[0];
        }
    }

    /**
     * 为运输单生成对账单信息
     *
     * @param orderId 运输单ID
     */
    private void generateReceiptsForOrder(Long orderId) {
        // 1. 查询运输单详细信息
        TransportOrder order = transportOrderMapper.selectTransportOrderById(orderId);
        if (order == null) {
            log.warn("运输单[{}]不存在，无法生成对账单", orderId);
            return;
        }

        // 2. 查询客户信息获取客户编号
        TransportCustomer customer = null;
        if (order.getCustomerId() != null) {
            customer = transportCustomerMapper.selectTransportCustomerById(order.getCustomerId());
        }

        String customerCode;
        if (customer != null && StringUtils.isNotBlank(customer.getCustomerCode())) {
            customerCode = customer.getCustomerCode();
        } else {
            // 如果客户编号为空，使用客户ID作为编号
            customerCode = "CUST" + String.format("%06d", order.getCustomerId());
            log.warn("客户[{}]编号为空，使用默认编号[{}]", order.getCustomerId(), customerCode);
        }

        // 3. 生成收据号码
        String receiptNumber = generateReceiptNumber(order);

        // 4. 检查收据号码是否已存在
        TransportReceipts existingReceipts = transportReceiptsService.findByReceiptNumber(receiptNumber);
        if (existingReceipts != null) {
            log.warn("收据号码[{}]已存在，跳过生成对账单", receiptNumber);
            return;
        }

        // 5. 创建对账单信息
        TransportReceipts receipts = getTransportReceipts(receiptNumber, order, customerCode);

        // 6. 保存对账单信息
        int result = transportReceiptsService.insert(receipts);
        if (result > 0) {
            log.info("运输单[{}]对账单生成成功，收据号码[{}]", order.getOrderNo(), receiptNumber);
        } else {
            log.error("运输单[{}]对账单生成失败", order.getOrderNo());
        }
    }

    private static TransportReceipts getTransportReceipts(String receiptNumber, TransportOrder order, String customerCode) {
        TransportReceipts receipts = new TransportReceipts();
        receipts.setReceiptNumber(receiptNumber);
        receipts.setAmount(order.getShippingCost()); // 运输费用
        receipts.setFundType(1); // 应付款
        receipts.setDescription("运输单[" + order.getOrderNo() + "]配送完成自动生成");
        receipts.setCustomerCode(customerCode);
        receipts.setInternalCode(order.getInternalCode());
        receipts.setCustomerName(order.getCustomerName());
        receipts.setPaymentMethod("待确认");
        receipts.setBankName("");
        receipts.setReceiptDate(order.getActualDeliveryTime() != null ? order.getActualDeliveryTime() : new Date());
        receipts.setUsedAmount(BigDecimal.ZERO);
        receipts.setTotalPrepaidAmount(BigDecimal.ZERO);
        receipts.setTotalExpenseAmount(order.getShippingCost() != null ? order.getShippingCost() : BigDecimal.ZERO);
        receipts.setTotalArrearsAmount(BigDecimal.ZERO);
        receipts.setCreateBy(SecurityUtils.getUsername());
        return receipts;
    }

    /**
     * 生成收据号码
     * 格式：TR + yyyyMMdd + 4位序号
     *
     * @param order 运输单信息
     * @return 收据号码
     */
    private String generateReceiptNumber(TransportOrder order) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());

        // 使用运输单ID的后4位作为序号，确保唯一性
        String sequence = String.format("%04d", order.getId() % 10000);

        return "TR" + dateStr + sequence;
    }
}
