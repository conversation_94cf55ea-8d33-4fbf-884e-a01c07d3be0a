package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.transport.TransportDriver;
import com.ruoyi.system.domain.transport.TransportOrder;
import com.ruoyi.system.domain.transport.TransportShippingRate;
import com.ruoyi.system.domain.transport.TransportVehicle;
import com.ruoyi.system.exception.transport.TransportBusinessException;
import com.ruoyi.system.exception.transport.TransportErrorCode;
import com.ruoyi.system.exception.transport.TransportExceptionUtils;
import com.ruoyi.system.mapper.transport.TransportDriverMapper;
import com.ruoyi.system.mapper.transport.TransportOrderMapper;
import com.ruoyi.system.mapper.transport.TransportVehicleMapper;
import com.ruoyi.system.service.transport.ITransportOrderService;
import com.ruoyi.system.service.transport.ITransportShippingRateService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运输单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class TransportOrderServiceImpl implements ITransportOrderService {
    
    @Autowired
    private TransportOrderMapper transportOrderMapper;

    @Autowired
    private TransportVehicleMapper transportVehicleMapper;

    @Autowired
    private TransportDriverMapper transportDriverMapper;

    @Autowired
    private ITransportShippingRateService transportShippingRateService;

    /**
     * 查询运输单
     * 
     * @param id 运输单主键
     * @return 运输单
     */
    @Override
    public TransportOrder selectTransportOrderById(Long id) {
        return transportOrderMapper.selectTransportOrderById(id);
    }

    /**
     * 查询运输单列表
     * 
     * @param transportOrder 运输单
     * @return 运输单
     */
    @Override
    public List<TransportOrder> selectTransportOrderList(TransportOrder transportOrder) {
        return transportOrderMapper.selectTransportOrderList(transportOrder);
    }

    /**
     * 新增运输单
     * 
     * @param transportOrder 运输单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTransportOrder(TransportOrder transportOrder) {
        // 1. 数据验证
        String validationResult = validateTransportOrder(transportOrder);
        if (StringUtils.isNotEmpty(validationResult)) {
            throw new TransportBusinessException.ValidationException(validationResult);
        }
        
        // 2. 生成单号
        if (StringUtils.isEmpty(transportOrder.getOrderNo())) {
            transportOrder.setOrderNo(generateOrderNo());
        }
        if (StringUtils.isEmpty(transportOrder.getInternalCode())) {
            transportOrder.setInternalCode(generateInternalCode());
        }

        // 3. 自动计算运费和杂费（如果未设置）
        if (transportOrder.getShippingCost() == null || transportOrder.getShippingCost().compareTo(BigDecimal.ZERO) == 0) {
            calculateShippingCostAndOtherExpenses(transportOrder);
        }

        // 4. 设置默认值
        if (transportOrder.getOrderStatus() == null) {
            transportOrder.setOrderStatus(1); // 默认待指派
        }

        transportOrder.setCreateBy(SecurityUtils.getUsername());
        transportOrder.setCreateTime(DateUtils.getNowDate());
        transportOrder.setIsDeleted(0);

        return transportOrderMapper.insertTransportOrder(transportOrder);
    }

    /**
     * 修改运输单
     * 
     * @param transportOrder 运输单
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTransportOrder(TransportOrder transportOrder) {
        // 1. 数据验证
        String validationResult = validateTransportOrder(transportOrder);
        if (StringUtils.isNotEmpty(validationResult)) {
            throw new RuntimeException(validationResult);
        }

        transportOrder.setUpdateBy(SecurityUtils.getUsername());
        transportOrder.setUpdateTime(DateUtils.getNowDate());
        
        return transportOrderMapper.updateTransportOrder(transportOrder);
    }

    private void calculateShippingCostAndOtherExpenses(TransportOrder transportOrder) {
        try {
            TransportShippingRate applicableRate = transportShippingRateService.findApplicableRate(
                transportOrder.getLoadingPointName(),
                transportOrder.getConsigneeAddress(), // 使用收货地址作为终点
                transportOrder.getProductName(),
                transportOrder.getInternalCode()
            );
            if (applicableRate != null) {
                BigDecimal calculatedCost = applicableRate.getFreightUnit().multiply(transportOrder.getProductQuantity());
                transportOrder.setShippingCost(calculatedCost);

                // 从运价规则计算杂费
                if (CollectionUtils.isNotEmpty(applicableRate.getOtherFeeList())) {
                    BigDecimal totalOtherExpenses = applicableRate.getOtherFeeList().stream()
                        .map(fee -> fee.getOtherExpenses() != null ? fee.getOtherExpenses() : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    transportOrder.setOtherExpenses(totalOtherExpenses);
                } else {
                    transportOrder.setOtherExpenses(BigDecimal.ZERO);
                }
            } else {
                throw new TransportBusinessException.PricingException("未找到适用的运价规则，请手动输入运费");
            }
        } catch (Exception e) {
            if (transportOrder.getShippingCost() == null) {
                if (e instanceof TransportBusinessException.PricingException) {
                    throw e;
                }
                throw new TransportBusinessException.PricingException("运输费用计算失败，请手动输入运费");
            }
        }
    }

    /**
     * 批量删除运输单
     * 
     * @param ids 需要删除的运输单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportOrderByIds(Long[] ids) {
        return transportOrderMapper.deleteTransportOrderByIds(ids);
    }

    /**
     * 删除运输单信息
     * 
     * @param id 运输单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTransportOrderById(Long id) {
        return transportOrderMapper.deleteTransportOrderById(id);
    }

    /**
     * 生成运输单号
     * 
     * @return 运输单号
     */
    @Override
    public String generateOrderNo() {
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String maxOrderNo = transportOrderMapper.getMaxOrderNoByDate(dateStr);
        
        int sequence = 1;
        if (StringUtils.isNotEmpty(maxOrderNo) && maxOrderNo.length() >= 12) {
            String seqStr = maxOrderNo.substring(10);
            try {
                sequence = Integer.parseInt(seqStr) + 1;
            } catch (NumberFormatException e) {
                sequence = 1;
            }
        }
        
        return "YS" + dateStr + String.format("%04d", sequence);
    }

    /**
     * 生成内部编号
     * 
     * @return 内部编号
     */
    @Override
    public String generateInternalCode() {
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String maxInternalCode = transportOrderMapper.getMaxInternalCodeByDate(dateStr);
        
        int sequence = 1;
        if (StringUtils.isNotEmpty(maxInternalCode) && maxInternalCode.length() >= 12) {
            String seqStr = maxInternalCode.substring(10);
            try {
                sequence = Integer.parseInt(seqStr) + 1;
            } catch (NumberFormatException e) {
                sequence = 1;
            }
        }
        
        return "NB" + dateStr + String.format("%04d", sequence);
    }

    /**
     * 更新运输单状态
     * 
     * @param id 运输单ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderStatus(Long id, Integer status) {
        return transportOrderMapper.updateOrderStatus(id, status, SecurityUtils.getUsername());
    }

    /**
     * 指派车辆和司机
     *
     * @param id 运输单ID
     * @param vehicleId 车辆ID
     * @param driverId 司机ID
     * @return 结果
     */
    @Override
    @Transactional
    public int assignVehicleAndDriver(Long id, Long vehicleId, Long driverId) {
        // 1. 检查运输单是否存在
        TransportOrder order = transportOrderMapper.selectTransportOrderById(id);
        TransportExceptionUtils.checkOrderExists(order);

        // 2. 检查运输单状态
        TransportExceptionUtils.assertTrue(order.getOrderStatus() == 1,
            TransportErrorCode.ORDER_STATUS_ERROR, "只有待指派状态的运输单才能指派车辆和司机");

        // 3. 检查车辆可用性
        TransportVehicle vehicle = transportVehicleMapper.selectTransportVehicleById(vehicleId);
        TransportExceptionUtils.checkVehicleExists(vehicle);
        TransportExceptionUtils.checkVehicleAvailable(vehicle.getVehicleStatus());

        // 4. 检查司机可用性
        TransportDriver driver = transportDriverMapper.selectTransportDriverById(driverId);
        TransportExceptionUtils.checkDriverExists(driver);
        TransportExceptionUtils.checkDriverAvailable(driver.getDriverStatus());

        // 5. 执行指派操作
        int result = transportOrderMapper.assignVehicleAndDriver(id, vehicleId, driverId,
                                                               vehicle.getLicensePlate(), SecurityUtils.getUsername());

        // 6. 更新车辆和司机状态为运输中
        if (result > 0) {
            transportVehicleMapper.updateVehicleStatus(vehicleId, 2, SecurityUtils.getUsername());
            transportDriverMapper.updateDriverStatus(driverId, 1, SecurityUtils.getUsername()); // 司机保持在职状态
        }

        return result;
    }

    /**
     * 根据车辆ID查询运输单
     * 
     * @param vehicleId 车辆ID
     * @return 运输单列表
     */
    @Override
    public List<TransportOrder> selectOrdersByVehicleId(Long vehicleId) {
        return transportOrderMapper.selectOrdersByVehicleId(vehicleId);
    }

    /**
     * 根据司机ID查询运输单
     * 
     * @param driverId 司机ID
     * @return 运输单列表
     */
    @Override
    public List<TransportOrder> selectOrdersByDriverId(Long driverId) {
        return transportOrderMapper.selectOrdersByDriverId(driverId);
    }

    /**
     * 根据客户ID查询运输单
     * 
     * @param customerId 客户ID
     * @return 运输单列表
     */
    @Override
    public List<TransportOrder> selectOrdersByCustomerId(Long customerId) {
        return transportOrderMapper.selectOrdersByCustomerId(customerId);
    }

    /**
     * 统计各状态运输单数量
     * 
     * @return 统计结果
     */
    @Override
    public List<TransportOrder> selectOrderStatusStatistics() {
        return transportOrderMapper.selectOrderStatusStatistics();
    }

    /**
     * 查询待指派的运输单
     * 
     * @return 运输单列表
     */
    @Override
    public List<TransportOrder> selectPendingOrders() {
        return transportOrderMapper.selectPendingOrders();
    }

    /**
     * 查询运输中的运输单
     * 
     * @return 运输单列表
     */
    @Override
    public List<TransportOrder> selectTransportingOrders() {
        return transportOrderMapper.selectTransportingOrders();
    }

    /**
     * 根据运输单号查询运输单
     * 
     * @param orderNo 运输单号
     * @return 运输单
     */
    @Override
    public TransportOrder selectTransportOrderByOrderNo(String orderNo) {
        return transportOrderMapper.selectTransportOrderByOrderNo(orderNo);
    }

    /**
     * 检查运输单号是否存在
     * 
     * @param orderNo 运输单号
     * @return 是否存在
     */
    @Override
    public boolean checkOrderNoExists(String orderNo) {
        return transportOrderMapper.checkOrderNoExists(orderNo) > 0;
    }

    /**
     * 检查内部编号是否存在
     * 
     * @param internalCode 内部编号
     * @return 是否存在
     */
    @Override
    public boolean checkInternalCodeExists(String internalCode) {
        return transportOrderMapper.checkInternalCodeExists(internalCode) > 0;
    }

    /**
     * 验证运输单数据
     * 
     * @param transportOrder 运输单
     * @return 验证结果
     */
    @Override
    public String validateTransportOrder(TransportOrder transportOrder) {
        if (transportOrder == null) {
            return "运输单信息不能为空";
        }
        
        if (StringUtils.isEmpty(transportOrder.getCustomerName())) {
            return "委托客户名称不能为空";
        }
        
        if (StringUtils.isEmpty(transportOrder.getConsigneeName())) {
            return "收货方名称不能为空";
        }
        
        if (StringUtils.isEmpty(transportOrder.getLoadingPointName())) {
            return "装货点名称不能为空";
        }
        
        if (transportOrder.getShippingCost() == null || transportOrder.getShippingCost().doubleValue() < 0) {
            return "运输费用必须大于等于0";
        }
        
        // 检查运输单号唯一性
        if (StringUtils.isNotEmpty(transportOrder.getOrderNo())) {
            TransportOrder existingOrder = transportOrderMapper.selectTransportOrderByOrderNo(transportOrder.getOrderNo());
            if (existingOrder != null && !existingOrder.getId().equals(transportOrder.getId())) {
                return "运输单号已存在";
            }
        }
        
        return null;
    }
}
