package com.ruoyi.system.service.transport.impl;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.transport.TransportLoadingPoint;
import com.ruoyi.system.mapper.transport.TransportLoadingPointMapper;
import com.ruoyi.system.service.transport.ITransportLoadingPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TransportLoadingPointServiceImpl implements ITransportLoadingPointService {

    @Autowired
    private TransportLoadingPointMapper transportLoadingPointMapper;

    @Override
    public TransportLoadingPoint selectTransportLoadingPointById(Long id) {
        return transportLoadingPointMapper.selectTransportLoadingPointById(id);
    }

    @Override
    public List<TransportLoadingPoint> selectTransportLoadingPointList(TransportLoadingPoint transportLoadingPoint) {
        return transportLoadingPointMapper.selectTransportLoadingPointList(transportLoadingPoint);
    }

    @Override
    public int insertTransportLoadingPoint(TransportLoadingPoint transportLoadingPoint) {
        transportLoadingPoint.setCreateBy(SecurityUtils.getUsername());
        transportLoadingPoint.setUpdateBy(SecurityUtils.getUsername());
        return transportLoadingPointMapper.insertTransportLoadingPoint(transportLoadingPoint);
    }

    @Override
    public int updateTransportLoadingPoint(TransportLoadingPoint transportLoadingPoint) {
        transportLoadingPoint.setUpdateBy(SecurityUtils.getUsername());
        return transportLoadingPointMapper.updateTransportLoadingPoint(transportLoadingPoint);
    }

    @Override
    public int deleteTransportLoadingPointByIds(Long[] ids) {
        return transportLoadingPointMapper.deleteTransportLoadingPointByIds(ids);
    }

    @Override
    public int deleteTransportLoadingPointById(Long id) {
        return transportLoadingPointMapper.deleteTransportLoadingPointById(id);
    }

    @Override
    public TransportLoadingPoint selectLoadingPointByCode(String pointCode) {
        return transportLoadingPointMapper.selectLoadingPointByCode(pointCode);
    }

    @Override
    public boolean checkPointCodeExists(String pointCode, Long id) {
        return transportLoadingPointMapper.checkPointCodeExists(pointCode, id) > 0;
    }

    @Override
    public List<TransportLoadingPoint> selectActiveLoadingPoints() {
        return transportLoadingPointMapper.selectActiveLoadingPoints();
    }

    @Override
    public int updateLoadingPointStatus(Long id, Integer isActive) {
        return transportLoadingPointMapper.updateLoadingPointStatus(id, isActive, SecurityUtils.getUsername());
    }

    @Override
    public List<TransportLoadingPoint> selectLoadingPointsByType(Integer pointType) {
        return transportLoadingPointMapper.selectLoadingPointsByType(pointType);
    }

    @Override
    public List<TransportLoadingPoint> selectLoadingPointsByRegion(String countryCode, String provinceCode, String cityCode) {
        return transportLoadingPointMapper.selectLoadingPointsByRegion(countryCode, provinceCode, cityCode);
    }

    @Override
    public List<TransportLoadingPoint> selectLoadingPointOptions(TransportLoadingPoint transportLoadingPoint) {
        return transportLoadingPointMapper.selectLoadingPointOptions(transportLoadingPoint);
    }
}
