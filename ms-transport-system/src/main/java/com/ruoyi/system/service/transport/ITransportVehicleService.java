package com.ruoyi.system.service.transport;

import com.ruoyi.system.domain.dto.TransportVehicleDropdownDto;
import com.ruoyi.system.domain.transport.TransportVehicle;

import java.util.List;

/**
 * 车辆信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ITransportVehicleService {
    
    /**
     * 查询车辆信息
     * 
     * @param id 车辆信息主键
     * @return 车辆信息
     */
    public TransportVehicle selectTransportVehicleById(Long id);

    /**
     * 查询车辆信息列表
     * 
     * @param transportVehicle 车辆信息
     * @return 车辆信息集合
     */
    public List<TransportVehicle> selectTransportVehicleList(TransportVehicle transportVehicle);

    /**
     * 新增车辆信息
     * 
     * @param transportVehicle 车辆信息
     * @return 结果
     */
    public int insertTransportVehicle(TransportVehicle transportVehicle);

    /**
     * 修改车辆信息
     * 
     * @param transportVehicle 车辆信息
     * @return 结果
     */
    public int updateTransportVehicle(TransportVehicle transportVehicle);

    /**
     * 批量删除车辆信息
     * 
     * @param ids 需要删除的车辆信息主键集合
     * @return 结果
     */
    public int deleteTransportVehicleByIds(Long[] ids);

    /**
     * 删除车辆信息
     * 
     * @param id 车辆信息主键
     * @return 结果
     */
    public int deleteTransportVehicleById(Long id);

    /**
     * 根据车牌号查询车辆
     * 
     * @param licensePlate 车牌号
     * @return 车辆信息
     */
    public TransportVehicle selectVehicleByLicensePlate(String licensePlate);

    /**
     * 检查车牌号是否存在
     * 
     * @param licensePlate 车牌号
     * @param id 车辆ID(修改时排除自己)
     * @return 是否存在
     */
    public boolean checkLicensePlateExists(String licensePlate, Long id);

    /**
     * 查询可用车辆列表
     * 
     * @return 车辆信息集合
     */
    public List<TransportVehicle> selectAvailableVehicles();

    /**
     * 更新车辆状态
     * 
     * @param id 车辆ID
     * @param status 新状态
     * @return 结果
     */
    public int updateVehicleStatus(Long id, Integer status);

    /**
     * 根据区域代码查询车辆
     * 
     * @param regionCode 区域代码
     * @return 车辆信息集合
     */
    public List<TransportVehicle> selectVehiclesByRegion(String regionCode);

    /**
     * 统计各状态车辆数量
     * 
     * @return 统计结果
     */
    public List<TransportVehicle> selectVehicleStatusStatistics();

    /**
     * 查询即将到期的车辆(年检、保险)
     * 
     * @param days 提前天数
     * @return 车辆信息集合
     */
    public List<TransportVehicle> selectExpiringVehicles(Integer days);

    /**
     * 查询车辆下拉列表
     *
     * @param keyword 搜索关键字 (车牌号)
     * @return 车辆下拉列表
     */
    List<TransportVehicleDropdownDto> selectVehicleDropdownList(String keyword);
}
