# 运输对账Excel导入模板说明

## 模板字段说明

### 必填字段
| 列名 | 字段说明 | 数据类型 | 示例值 | 备注 |
|------|----------|----------|--------|------|
| 收据号码/订单号 | 唯一标识的收据编号 | 文本 | TR202401001 | 必填，系统内唯一 |
| 委托客户编号 | 客户的唯一编码 | 文本 | CUST001 | 必填 |
| 客户名称 | 客户的完整名称 | 文本 | 上海物流有限公司 | 必填 |

### 可选字段
| 列名 | 字段说明 | 数据类型 | 示例值 | 备注 |
|------|----------|----------|--------|------|
| 款项金额(分) | 交易金额，单位为分 | 数值 | 50000.00 | 可选，支持小数 |
| 款项类型 | 资金类型 | 数值 | 0 | 0=预收款,1=应付款,2=红冲应收,3=红冲应付 |
| 描述 | 交易描述信息 | 文本 | 预收运输费用 | 可选 |
| 内部编号 | 内部管理编号 | 文本 | INT001 | 可选 |
| 收款方式 | 付款方式 | 文本 | 银行转账 | 可选 |
| 收款银行 | 收款银行名称 | 文本 | 中国银行 | 可选 |
| 收款时间 | 收款的具体时间 | 日期时间 | 2024-01-15 10:30:00 | 格式：yyyy-MM-dd HH:mm:ss |
| 可用款项（分） | 当前可用金额 | 数值 | 50000.00 | 默认为0 |
| 总预存金额 | 预存资金总额 | 数值 | 50000.00 | 可选 |
| 总支出金额 | 支出资金总额 | 数值 | 0.00 | 默认为0 |
| 总欠款金额 | 欠款资金总额 | 数值 | 0.00 | 默认为0 |

## 款项类型说明

| 类型值 | 类型名称 | 说明 | 对余额影响 |
|--------|----------|------|------------|
| 0 | 预收款 | 客户预先支付的费用 | 增加余额 |
| 1 | 应付款 | 需要支付的费用 | 减少余额 |
| 2 | 红冲应收 | 冲减应收账款 | 增加余额 |
| 3 | 红冲应付 | 冲减应付账款 | 减少余额 |

## Excel模板示例

```
收据号码/订单号 | 款项金额(分) | 款项类型 | 描述 | 委托客户编号 | 内部编号 | 客户名称 | 收款方式 | 收款银行 | 收款时间 | 可用款项（分） | 总预存金额 | 总支出金额 | 总欠款金额
TR202401001 | 50000.00 | 0 | 预收运输费用 | CUST001 | INT001 | 上海物流有限公司 | 银行转账 | 中国银行 | 2024-01-15 10:30:00 | 50000.00 | 50000.00 | 0.00 | 0.00
TR202401002 | 30000.00 | 1 | 应付运输费用 | CUST002 | INT002 | 北京运输集团 | 现金支付 | 工商银行 | 2024-01-16 14:20:00 | 0.00 | 0.00 | 30000.00 | 0.00
TR202401003 | 25000.00 | 0 | 预收货运费 | CUST003 | INT003 | 广州货运公司 | 支票支付 | 建设银行 | 2024-01-17 09:15:00 | 25000.00 | 25000.00 | 0.00 | 0.00
```

## 数据验证规则

### 1. 必填字段验证
- 收据号码/订单号：不能为空
- 委托客户编号：不能为空
- 客户名称：不能为空

### 2. 数据格式验证
- 款项金额：必须为数值类型，支持小数
- 款项类型：必须为0、1、2、3中的一个
- 收款时间：必须符合日期时间格式 yyyy-MM-dd HH:mm:ss

### 3. 业务规则验证
- 收据号码：系统内必须唯一，不能重复
- 客户编号：建议与系统中的客户信息保持一致

### 4. 数据长度限制
- 收据号码：最大32个字符
- 客户编号：最大32个字符
- 客户名称：最大255个字符
- 描述：最大255个字符
- 内部编号：最大32个字符
- 收款方式：最大32个字符
- 收款银行：最大255个字符

## 导入注意事项

### 1. 文件格式
- 支持.xlsx和.xls格式
- 建议使用.xlsx格式以获得更好的兼容性
- 文件大小建议不超过10MB

### 2. 数据准备
- 确保Excel第一行为列标题
- 数据从第二行开始
- 删除空行和无效数据
- 检查数据格式是否正确

### 3. 导入流程
1. 准备Excel文件，确保数据格式正确
2. 登录系统，进入运输对账管理页面
3. 点击"导入"按钮，选择Excel文件
4. 系统自动验证数据格式和业务规则
5. 验证通过后，数据批量导入系统
6. 查看导入结果，处理可能的错误信息

### 4. 错误处理
- 如果导入失败，系统会提示具体的错误信息
- 根据错误信息修正Excel文件中的数据
- 重新导入修正后的文件

### 5. 性能建议
- 单次导入建议不超过1000条记录
- 大批量数据建议分批导入
- 导入前建议先备份现有数据

## 导出功能说明

### 1. 导出条件
- 可按客户编号筛选
- 可按时间范围筛选
- 可按款项类型筛选

### 2. 导出格式
- 导出的Excel文件包含所有字段
- 文件名格式：客户编号_运输对账单_日期.xlsx
- 如：CUST001_运输对账单_20240115.xlsx

### 3. 导出用途
- 客户对账单生成
- 财务数据分析
- 数据备份和归档
- 报表制作

## 常见问题

### Q1: 导入时提示"收据号码已存在"怎么办？
A1: 检查Excel中的收据号码是否与系统中已有数据重复，修改为唯一的收据号码后重新导入。

### Q2: 款项类型应该如何选择？
A2: 根据实际业务情况选择：客户预付费用选择0（预收款），需要支付给客户的费用选择1（应付款），红冲业务选择2或3。

### Q3: 时间格式不正确怎么办？
A3: 确保时间格式为 yyyy-MM-dd HH:mm:ss，如：2024-01-15 10:30:00。

### Q4: 导入后如何验证数据是否正确？
A4: 可以通过查询功能检查导入的数据，也可以导出数据进行对比验证。

### Q5: 可以修改已导入的数据吗？
A5: 可以，通过编辑功能可以修改已导入的对账信息，但收据号码不能修改为已存在的号码。
