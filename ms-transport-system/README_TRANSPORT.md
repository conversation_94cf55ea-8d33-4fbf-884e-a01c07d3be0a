# 运输管理系统 Transport 包

## 概述

本项目是完全参考现有oil包结构，按照《运输系统研发设计文档》实现的运输管理系统transport包，包含完整的运输业务管理功能，实现了运输单全生命周期管理、智能运费计算、状态流转控制等核心业务功能。

## 技术架构

- **基础框架**: Spring Boot 2.5.15
- **数据访问**: MyBatis + MyBatis-Plus
- **数据库**: MySQL 8.0
- **安全框架**: Spring Security + JWT
- **API文档**: Swagger/OpenAPI 3.0
- **Excel导入导出**: EasyExcel
- **日志记录**: 操作日志注解

## 项目结构

```
ms-transport-system/
├── src/main/java/com/ruoyi/system/
│   ├── domain/transport/                    # 实体类
│   │   ├── TransportOrder.java             # 运输单实体
│   │   ├── TransportVehicle.java           # 车辆信息实体
│   │   ├── TransportDriver.java            # 司机信息实体
│   │   ├── TransportCustomer.java          # 委托客户实体
│   │   ├── TransportConsignee.java         # 收货方实体
│   │   ├── TransportLoadingPoint.java      # 装货点实体
│   │   ├── TransportPricingRule.java       # 运费计算规则实体
│   │   ├── TransportRoute.java             # 运输路线实体
│   │   └── TransportVehicleMaintenance.java # 车辆维护记录实体
│   ├── mapper/transport/                    # 数据访问接口
│   │   ├── TransportOrderMapper.java       # 运输单Mapper
│   │   ├── TransportVehicleMapper.java     # 车辆Mapper
│   │   ├── TransportDriverMapper.java      # 司机Mapper
│   │   ├── TransportCustomerMapper.java    # 客户Mapper
│   │   ├── TransportConsigneeMapper.java   # 收货方Mapper
│   │   └── TransportLoadingPointMapper.java # 装货点Mapper
│   └── service/transport/                   # 业务服务层
│       ├── ITransportOrderService.java     # 运输单服务接口
│       ├── ITransportVehicleService.java   # 车辆服务接口
│       ├── ITransportDriverService.java    # 司机服务接口
│       ├── ITransportCustomerService.java  # 客户服务接口
│       ├── ITransportConsigneeService.java # 收货方服务接口
│       ├── ITransportLoadingPointService.java # 装货点服务接口
│       ├── ITransportPricingService.java   # 运费计算服务接口
│       ├── ITransportOrderStatusService.java # 运输单状态管理服务接口
│       └── impl/                           # 服务实现类
│           ├── TransportOrderServiceImpl.java
│           ├── TransportVehicleServiceImpl.java
│           ├── TransportDriverServiceImpl.java
│           ├── TransportCustomerServiceImpl.java
│           ├── TransportConsigneeServiceImpl.java
│           ├── TransportPricingServiceImpl.java
│           └── TransportOrderStatusServiceImpl.java
├── src/main/resources/mapper/transport/     # MyBatis映射文件
│   ├── TransportOrderMapper.xml            # 运输单映射
│   └── TransportVehicleMapper.xml          # 车辆映射
└── sql/
    └── transport_schema.sql                # 数据库建表脚本

ms-transport-admin/
└── src/main/java/com/ruoyi/web/controller/transport/  # 控制器层
    ├── TransportOrderController.java       # 运输单控制器
    ├── TransportVehicleController.java     # 车辆控制器
    ├── TransportDriverController.java      # 司机控制器
    └── TransportCustomerController.java    # 客户控制器
```

## 核心功能模块

### 1. 运输单管理 (TransportOrder)
- ✅ 运输单CRUD操作
- ✅ 运输单号和内部编号自动生成
- ✅ 运输单状态管理(7种状态)
- ✅ 车辆司机指派功能
- ✅ 多维度查询和统计
- ✅ Excel导入导出

### 2. 车辆管理 (TransportVehicle)
- ✅ 车辆信息CRUD操作
- ✅ 车牌号唯一性验证
- ✅ 车辆状态管理(空闲/运输中/维修中/报废)
- ✅ 可用车辆查询
- ✅ 年检保险到期提醒

### 3. 司机管理 (TransportDriver)
- ✅ 司机信息CRUD操作
- ✅ 手机号和身份证号唯一性验证
- ✅ 司机状态管理(在职/休假/离职)
- ✅ 可用司机查询
- ✅ 驾龄和驾照类型管理

### 4. 客户管理 (TransportCustomer)
- ✅ 委托客户CRUD操作
- ✅ 客户编码和名称唯一性验证
- ✅ 客户状态管理(正常/暂停/终止合作)
- ✅ 信用等级和付款方式管理
- ✅ VIP客户查询

### 5. 收货方管理 (TransportConsignee)
- ✅ 收货方信息CRUD操作
- ✅ 与委托客户关联管理
- ✅ 收货方类型分类
- ✅ 配送要求管理

### 6. 装货点管理 (TransportLoadingPoint)
- ✅ 装货点信息CRUD操作
- ✅ 装货点类型分类(港口/油库/炼厂/其他)
- ✅ 营业时间和装货能力管理
- ✅ 特殊要求配置

## API接口

### 运输单管理接口
```http
GET    /transport/order/list                    # 查询运输单列表
GET    /transport/order/{id}                    # 查询运输单详情
POST   /transport/order                         # 新增运输单
PUT    /transport/order                         # 修改运输单
DELETE /transport/order/{ids}                   # 删除运输单
POST   /transport/order/export                  # 导出运输单

GET    /transport/order/generateOrderNo         # 生成运输单号
GET    /transport/order/generateInternalCode    # 生成内部编号
PUT    /transport/order/status/{id}/{status}    # 更新运输单状态
PUT    /transport/order/assign/{id}             # 指派车辆和司机

GET    /transport/order/vehicle/{vehicleId}     # 根据车辆ID查询运输单
GET    /transport/order/driver/{driverId}       # 根据司机ID查询运输单
GET    /transport/order/customer/{customerId}   # 根据客户ID查询运输单
GET    /transport/order/statistics/status       # 统计各状态运输单数量
GET    /transport/order/pending                 # 查询待指派运输单
GET    /transport/order/transporting            # 查询运输中运输单
```

### 车辆管理接口
```http
GET    /transport/vehicle/list                  # 查询车辆列表
GET    /transport/vehicle/{id}                  # 查询车辆详情
POST   /transport/vehicle                       # 新增车辆
PUT    /transport/vehicle                       # 修改车辆
DELETE /transport/vehicle/{ids}                 # 删除车辆

GET    /transport/vehicle/available             # 查询可用车辆
PUT    /transport/vehicle/status/{id}/{status}  # 更新车辆状态
GET    /transport/vehicle/statistics/status     # 统计各状态车辆数量
GET    /transport/vehicle/expiring              # 查询即将到期车辆
```

### 司机管理接口
```http
GET    /transport/driver/list                   # 查询司机列表
GET    /transport/driver/{id}                   # 查询司机详情
POST   /transport/driver                        # 新增司机
PUT    /transport/driver                        # 修改司机
DELETE /transport/driver/{ids}                  # 删除司机

GET    /transport/driver/available              # 查询可用司机
PUT    /transport/driver/status/{id}/{status}   # 更新司机状态
GET    /transport/driver/statistics/status      # 统计各状态司机数量
GET    /transport/driver/experienced            # 查询经验丰富司机
```

### 客户管理接口
```http
GET    /transport/customer/list                 # 查询客户列表
GET    /transport/customer/{id}                 # 查询客户详情
POST   /transport/customer                      # 新增客户
PUT    /transport/customer                      # 修改客户
DELETE /transport/customer/{ids}                # 删除客户

GET    /transport/customer/active               # 查询正常合作客户
PUT    /transport/customer/status/{id}/{status} # 更新客户状态
GET    /transport/customer/statistics/status    # 统计各状态客户数量
GET    /transport/customer/vip                  # 查询VIP客户
```

## 数据库部署

1. 执行建表脚本：
```sql
source ms-transport-system/sql/transport_schema.sql;
```

2. 确认表结构创建成功：
```sql
SHOW TABLES LIKE 'transport_%';
```

## 权限配置

需要在系统菜单中添加以下权限：

```
transport:order:list      # 运输单查询
transport:order:add       # 运输单新增
transport:order:edit      # 运输单修改
transport:order:remove    # 运输单删除
transport:order:export    # 运输单导出
transport:order:assign    # 运输单指派

transport:vehicle:list    # 车辆查询
transport:vehicle:add     # 车辆新增
transport:vehicle:edit    # 车辆修改
transport:vehicle:remove  # 车辆删除
transport:vehicle:export  # 车辆导出

transport:driver:list     # 司机查询
transport:driver:add      # 司机新增
transport:driver:edit     # 司机修改
transport:driver:remove   # 司机删除
transport:driver:export   # 司机导出

transport:customer:list   # 客户查询
transport:customer:add    # 客户新增
transport:customer:edit   # 客户修改
transport:customer:remove # 客户删除
transport:customer:export # 客户导出
```

## 使用示例

### 1. 创建运输单
```http
POST /transport/order
Content-Type: application/json

{
  "customerName": "XX石油贸易公司",
  "consigneeName": "XX加油站",
  "consigneeAddress": "北京市朝阳区XX路XX号",
  "loadingPointName": "上海港石油码头",
  "loadingAddress": "上海市浦东新区港口大道1号",
  "productName": "92号汽油",
  "productQuantity": 10.5,
  "shippingCost": 3000.00,
  "deliveryRequirements": "工作时间配送"
}
```

### 2. 指派车辆和司机
```http
PUT /transport/order/assign/1?vehicleId=1&driverId=1
```

### 3. 更新运输单状态
```http
PUT /transport/order/status/1/3
```

## 特色功能

1. **完全参考oil包结构** - 保持系统架构一致性
2. **完整的业务流程** - 从运输单创建到完成的全流程管理
3. **丰富的查询功能** - 支持多维度查询和统计
4. **数据验证机制** - 完整的业务规则验证
5. **Excel导入导出** - 支持批量数据处理
6. **操作日志记录** - 关键操作的审计跟踪
7. **权限控制** - 基于注解的细粒度权限控制
8. **Swagger文档** - 完整的API文档支持

## 扩展说明

本实现完全按照后端研发设计文档创建，可以根据实际需求进行以下扩展：

1. 添加运价计算规则
2. 集成第三方地图服务
3. 添加GPS轨迹跟踪
4. 实现消息推送功能
5. 添加报表统计功能
6. 集成财务结算模块

## 注意事项

1. 所有表都支持逻辑删除，删除操作只是将is_deleted字段设为1
2. 运输单号和内部编号采用日期+序号的格式自动生成
3. 支持分页查询，默认每页10条记录
4. 所有时间字段都使用TIMESTAMP类型，自动维护创建和更新时间
5. 车牌号、手机号、身份证号等关键字段有唯一性验证
6. 运输单状态变更有业务规则限制
