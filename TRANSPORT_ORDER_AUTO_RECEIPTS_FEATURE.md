# 运输单配送完成自动生成对账单功能

## 功能概述

在运输单配送完成时，系统会自动生成对应的对账单信息，实现运输业务与财务对账的无缝衔接。

## 实现原理

### 触发时机
- 当运输单状态从"运输中(5)"更新为"已送达(6)"时
- 通过调用`TransportOrderController.completeDelivery()`接口触发

### 处理流程
1. **更新运输单状态**：将运输单状态更新为"已送达"
2. **获取运输单信息**：查询运输单的详细信息
3. **获取客户编号**：根据客户ID查询客户编号
4. **生成收据号码**：按照规则生成唯一的收据号码
5. **检查重复性**：验证收据号码是否已存在
6. **创建对账单**：生成对账单记录并保存到数据库

## 技术实现

### 修改的文件
1. **TransportOrderStatusServiceImpl.java** - 核心业务逻辑
2. **TransportOrderController.java** - API接口层
3. **新增测试类** - 功能验证

### 核心方法

#### 1. completeDelivery() 方法增强
```java
@Override
@Transactional
public int completeDelivery(Long orderId) {
    // 1. 更新运输单状态为已送达
    int result = updateOrderStatus(orderId, OrderStatus.DELIVERED.getCode());
    
    // 2. 如果状态更新成功，自动生成对账单信息
    if (result > 0) {
        try {
            generateReceiptsForOrder(orderId);
            log.info("运输单[{}]配送完成，已自动生成对账单", orderId);
        } catch (Exception e) {
            log.error("运输单[{}]配送完成后生成对账单失败", orderId, e);
            // 注意：这里不抛出异常，避免影响主流程
        }
    }
    
    return result;
}
```

#### 2. generateReceiptsForOrder() 私有方法
```java
private void generateReceiptsForOrder(Long orderId) {
    // 1. 查询运输单详细信息
    // 2. 查询客户信息获取客户编号
    // 3. 生成收据号码
    // 4. 检查收据号码是否已存在
    // 5. 创建对账单信息
    // 6. 保存对账单信息
}
```

#### 3. generateReceiptNumber() 收据号码生成
```java
private String generateReceiptNumber(TransportOrder order) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    String dateStr = sdf.format(new Date());
    String sequence = String.format("%04d", order.getId() % 10000);
    return "TR" + dateStr + sequence;
}
```

## 对账单生成规则

### 收据号码格式
- **格式**：`TR + yyyyMMdd + 4位序号`
- **示例**：`TR202401150001`
- **说明**：
  - `TR`：运输单对账单前缀
  - `yyyyMMdd`：当前日期
  - `4位序号`：运输单ID的后4位，确保唯一性

### 对账单字段映射

| 对账单字段 | 数据来源 | 说明 |
|------------|----------|------|
| receiptNumber | 自动生成 | TR + 日期 + 序号 |
| amount | order.shippingCost | 运输费用 |
| fundType | 固定值：1 | 应付款 |
| description | 自动生成 | 包含运输单号的描述 |
| customerCode | customer.customerCode | 客户编号，如为空则生成默认值 |
| internalCode | order.internalCode | 运输单内部编号 |
| customerName | order.customerName | 客户名称 |
| paymentMethod | 固定值："待确认" | 待后续确认 |
| receiptDate | order.actualDeliveryTime | 实际送达时间 |
| usedAmount | 固定值：0 | 初始可用金额 |
| totalExpenseAmount | order.shippingCost | 总支出金额 |

## 异常处理机制

### 1. 容错设计
- 对账单生成失败不会影响运输单状态更新
- 采用独立的try-catch块处理对账单生成异常
- 记录详细的错误日志便于问题排查

### 2. 重复性检查
- 生成对账单前检查收据号码是否已存在
- 如果存在相同收据号码，跳过生成并记录警告日志
- 避免重复执行导致的数据重复问题

### 3. 数据完整性
- 客户编号为空时自动生成默认编号：`CUST + 6位客户ID`
- 运输费用为空时设置为0
- 必填字段都有默认值或从运输单获取

## API接口变更

### completeDelivery 接口增强
- **路径**：`PUT /order/completeDelivery/{id}`
- **功能**：完成配送并自动生成对账单
- **返回**：增加对账单生成成功的提示信息

```json
{
  "code": 200,
  "msg": "配送完成，已自动生成对账单信息",
  "data": null
}
```

## 使用说明

### 1. 正常流程
1. 运输单状态流转到"运输中"
2. 调用完成配送接口：`PUT /order/completeDelivery/{orderId}`
3. 系统自动：
   - 更新运输单状态为"已送达"
   - 生成对应的对账单记录
   - 返回成功信息

### 2. 查看生成的对账单
- 通过运输对账管理界面查看
- 根据收据号码或客户编号查询
- 收据号码格式：`TR + 日期 + 序号`

### 3. 异常情况处理
- 如果对账单生成失败，运输单状态仍会正常更新
- 可通过日志查看失败原因
- 可手动创建对账单记录

## 配置要求

### 1. 数据库依赖
- 需要`transport_receipts`表已创建
- 需要相关的索引和约束

### 2. 服务依赖
- `ITransportReceiptsService`服务正常运行
- `TransportCustomerMapper`数据访问正常
- 事务管理器配置正确

## 测试验证

### 1. 单元测试
- `TransportOrderCompleteDeliveryTest`类
- 测试正常流程、重复生成、异常情况等场景

### 2. 集成测试
- 完整的运输单流程测试
- 对账单数据完整性验证
- 异常情况恢复测试

## 监控和日志

### 1. 关键日志
- 对账单生成成功：`INFO`级别
- 对账单生成失败：`ERROR`级别
- 收据号码重复：`WARN`级别
- 客户编号为空：`WARN`级别

### 2. 监控指标
- 对账单生成成功率
- 对账单生成耗时
- 异常情况统计

## 后续扩展

### 1. 功能增强
- 支持多种对账单类型
- 支持自定义收据号码格式
- 支持对账单模板配置

### 2. 业务扩展
- 与财务系统集成
- 支持自动对账流程
- 支持对账单审批流程

### 3. 性能优化
- 异步生成对账单
- 批量处理优化
- 缓存机制引入

## 注意事项

1. **事务边界**：对账单生成失败不会回滚运输单状态更新
2. **幂等性**：重复调用不会生成重复的对账单
3. **数据一致性**：确保运输单和对账单数据的一致性
4. **性能影响**：对账单生成会增加接口响应时间
5. **权限控制**：使用现有的运输单编辑权限

## 总结

该功能实现了运输业务与财务对账的自动化衔接，提高了业务处理效率，减少了手工操作错误。通过完善的异常处理和容错机制，确保了系统的稳定性和数据的完整性。
