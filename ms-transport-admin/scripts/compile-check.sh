#!/bin/bash

# 编译检查脚本
# 用于验证删除oil包后项目是否能正常编译

echo "=========================================="
echo "项目编译检查"
echo "=========================================="

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 进入项目根目录
cd "$(dirname "$0")/.."

echo "当前目录: $(pwd)"
echo ""

echo "1. 清理项目..."
echo "----------------------------------------"
mvn clean
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 项目清理成功${NC}"
else
    echo -e "${RED}❌ 项目清理失败${NC}"
    exit 1
fi

echo ""
echo "2. 编译项目..."
echo "----------------------------------------"
mvn compile -q
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 项目编译成功${NC}"
else
    echo -e "${RED}❌ 项目编译失败${NC}"
    echo ""
    echo "编译失败可能的原因："
    echo "1. 还有oil包的引用未清理"
    echo "2. Transport包的某些类有问题"
    echo "3. 依赖关系有问题"
    echo ""
    echo "请检查编译错误信息并修复。"
    exit 1
fi

echo ""
echo "3. 编译测试代码..."
echo "----------------------------------------"
mvn test-compile -q
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 测试代码编译成功${NC}"
else
    echo -e "${YELLOW}⚠️ 测试代码编译有问题${NC}"
    echo "这可能是正常的，因为删除了oil相关的测试文件"
fi

echo ""
echo "4. 检查依赖..."
echo "----------------------------------------"
mvn dependency:resolve -q
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 依赖解析成功${NC}"
else
    echo -e "${RED}❌ 依赖解析失败${NC}"
    exit 1
fi

echo ""
echo "=========================================="
echo "编译检查完成"
echo "=========================================="
echo -e "${GREEN}🎉 项目编译检查通过！${NC}"
echo ""
echo "Oil包删除成功，项目可以正常编译。"
echo ""
echo "建议下一步操作："
echo "1. 运行验证脚本: ./scripts/verify-oil-removal.sh"
echo "2. 启动应用测试: mvn spring-boot:run"
echo "3. 测试Transport功能是否正常"
