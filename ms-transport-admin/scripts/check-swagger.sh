#!/bin/bash

# Swagger访问检查脚本
# 用于检查应用启动后Swagger是否可以正常访问

echo "=========================================="
echo "Swagger访问检查脚本"
echo "=========================================="

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置参数
HOST="localhost"
PORT="6877"
CONTEXT_PATH="/transport"
API_PATH="/dev-api"

# 基础URL
BASE_URL="http://${HOST}:${PORT}${CONTEXT_PATH}"
API_BASE_URL="${BASE_URL}${API_PATH}"

echo "检查配置："
echo "- 主机: $HOST"
echo "- 端口: $PORT"
echo "- 上下文路径: $CONTEXT_PATH"
echo "- API路径: $API_PATH"
echo "- 基础URL: $BASE_URL"
echo ""

# 检查函数
check_url() {
    local url="$1"
    local description="$2"
    local expected_status="${3:-200}"
    
    echo -n "检查 $description ... "
    
    # 使用curl检查URL
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✓ 成功 (HTTP $response)${NC}"
        return 0
    else
        echo -e "${RED}✗ 失败 (HTTP $response)${NC}"
        return 1
    fi
}

# 检查应用是否启动
echo "1. 检查应用基础服务..."
echo "----------------------------------------"

check_url "$BASE_URL/common/health/check" "应用健康检查"
health_status=$?

if [ $health_status -ne 0 ]; then
    echo -e "${RED}应用未正常启动，请检查：${NC}"
    echo "1. 应用是否已启动"
    echo "2. 端口 $PORT 是否被占用"
    echo "3. 上下文路径是否正确"
    echo ""
    echo "启动命令示例："
    echo "java -jar ms-transport-admin.jar"
    exit 1
fi

echo ""
echo "2. 检查Swagger相关服务..."
echo "----------------------------------------"

# 检查API文档
check_url "$API_BASE_URL/v2/api-docs" "API文档接口"
api_docs_status=$?

# 检查Swagger UI资源
check_url "$API_BASE_URL/swagger-resources" "Swagger资源配置"
swagger_resources_status=$?

# 检查Swagger UI页面
check_url "$API_BASE_URL/swagger-ui/index.html" "Swagger UI页面"
swagger_ui_status=$?

# 检查备用Swagger UI页面
if [ $swagger_ui_status -ne 0 ]; then
    check_url "$BASE_URL/swagger-ui/index.html" "备用Swagger UI页面"
    swagger_ui_backup_status=$?
else
    swagger_ui_backup_status=0
fi

echo ""
echo "3. 检查Transport业务接口..."
echo "----------------------------------------"

# 检查Transport相关接口
check_url "$API_BASE_URL/transport/countries/list" "地址管理接口" "200"
transport_status=$?

echo ""
echo "=========================================="
echo "检查结果汇总"
echo "=========================================="

# 统计结果
total_checks=5
passed_checks=0

if [ $health_status -eq 0 ]; then ((passed_checks++)); fi
if [ $api_docs_status -eq 0 ]; then ((passed_checks++)); fi
if [ $swagger_resources_status -eq 0 ]; then ((passed_checks++)); fi
if [ $swagger_ui_status -eq 0 ] || [ $swagger_ui_backup_status -eq 0 ]; then ((passed_checks++)); fi
if [ $transport_status -eq 0 ]; then ((passed_checks++)); fi

echo "通过检查: $passed_checks/$total_checks"

if [ $passed_checks -eq $total_checks ]; then
    echo -e "${GREEN}🎉 所有检查都通过了！${NC}"
    echo ""
    echo "Swagger访问地址："
    if [ $swagger_ui_status -eq 0 ]; then
        echo -e "${GREEN}✓ 主要地址: $API_BASE_URL/swagger-ui/index.html${NC}"
    fi
    if [ $swagger_ui_backup_status -eq 0 ]; then
        echo -e "${GREEN}✓ 备用地址: $BASE_URL/swagger-ui/index.html${NC}"
    fi
    echo ""
    echo "其他有用的地址："
    echo "- API文档: $API_BASE_URL/v2/api-docs"
    echo "- 健康检查: $BASE_URL/common/health/check"
    echo "- 应用信息: $BASE_URL/common/health/info"
    
else
    echo -e "${RED}❌ 发现 $((total_checks - passed_checks)) 个问题${NC}"
    echo ""
    echo "问题排查建议："
    
    if [ $health_status -ne 0 ]; then
        echo "1. 应用未正常启动，请检查应用日志"
    fi
    
    if [ $api_docs_status -ne 0 ]; then
        echo "2. API文档接口异常，检查Swagger配置"
    fi
    
    if [ $swagger_resources_status -ne 0 ]; then
        echo "3. Swagger资源配置异常，检查ResourcesConfig"
    fi
    
    if [ $swagger_ui_status -ne 0 ] && [ $swagger_ui_backup_status -ne 0 ]; then
        echo "4. Swagger UI页面无法访问，检查静态资源配置"
    fi
    
    if [ $transport_status -ne 0 ]; then
        echo "5. Transport业务接口异常，检查Controller配置"
    fi
    
    echo ""
    echo "详细排查步骤请参考: docs/swagger-access-guide.md"
fi

echo ""
echo "检查完成时间: $(date)"
