@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ==========================================
echo Swagger访问检查脚本 (Windows版本)
echo ==========================================

:: 配置参数
set HOST=localhost
set PORT=6877
set CONTEXT_PATH=/transport
set API_PATH=/dev-api

:: 基础URL
set BASE_URL=http://%HOST%:%PORT%%CONTEXT_PATH%
set API_BASE_URL=%BASE_URL%%API_PATH%

echo 检查配置：
echo - 主机: %HOST%
echo - 端口: %PORT%
echo - 上下文路径: %CONTEXT_PATH%
echo - API路径: %API_PATH%
echo - 基础URL: %BASE_URL%
echo.

:: 检查函数
:check_url
set url=%1
set description=%2
set expected_status=%3
if "%expected_status%"=="" set expected_status=200

echo 检查 %description% ...

:: 使用PowerShell检查URL
powershell -Command "try { $response = Invoke-WebRequest -Uri '%url%' -UseBasicParsing -TimeoutSec 10; if($response.StatusCode -eq %expected_status%) { Write-Host '✓ 成功 (HTTP' $response.StatusCode ')' -ForegroundColor Green; exit 0 } else { Write-Host '✗ 失败 (HTTP' $response.StatusCode ')' -ForegroundColor Red; exit 1 } } catch { Write-Host '✗ 失败 (连接错误)' -ForegroundColor Red; exit 1 }"

goto :eof

echo 1. 检查应用基础服务...
echo ----------------------------------------

call :check_url "%BASE_URL%/common/health/check" "应用健康检查"
if errorlevel 1 (
    echo 应用未正常启动，请检查：
    echo 1. 应用是否已启动
    echo 2. 端口 %PORT% 是否被占用
    echo 3. 上下文路径是否正确
    echo.
    echo 启动命令示例：
    echo java -jar ms-transport-admin.jar
    pause
    exit /b 1
)

echo.
echo 2. 检查Swagger相关服务...
echo ----------------------------------------

call :check_url "%API_BASE_URL%/v2/api-docs" "API文档接口"
call :check_url "%API_BASE_URL%/swagger-resources" "Swagger资源配置"
call :check_url "%API_BASE_URL%/swagger-ui/index.html" "Swagger UI页面"

echo.
echo 3. 检查Transport业务接口...
echo ----------------------------------------

call :check_url "%API_BASE_URL%/transport/countries/list" "地址管理接口"

echo.
echo ==========================================
echo 检查完成
echo ==========================================

echo.
echo Swagger访问地址：
echo 主要地址: %API_BASE_URL%/swagger-ui/index.html
echo 备用地址: %BASE_URL%/swagger-ui/index.html
echo.
echo 其他有用的地址：
echo - API文档: %API_BASE_URL%/v2/api-docs
echo - 健康检查: %BASE_URL%/common/health/check
echo - 应用信息: %BASE_URL%/common/health/info
echo.
echo 如果仍然无法访问，请参考: docs\swagger-access-guide.md
echo.

pause
