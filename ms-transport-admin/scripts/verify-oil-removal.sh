#!/bin/bash

# Oil包删除验证脚本
# 用于验证oil包是否已完全删除，以及transport包功能是否正常

echo "=========================================="
echo "Oil包删除验证脚本"
echo "=========================================="

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证计数器
PASS_COUNT=0
FAIL_COUNT=0

# 验证函数
verify_step() {
    local description="$1"
    local command="$2"
    local expected_result="$3"
    
    echo -n "验证: $description ... "
    
    if eval "$command"; then
        if [ "$expected_result" = "should_fail" ]; then
            echo -e "${RED}失败${NC} (期望失败但实际成功)"
            FAIL_COUNT=$((FAIL_COUNT + 1))
        else
            echo -e "${GREEN}通过${NC}"
            PASS_COUNT=$((PASS_COUNT + 1))
        fi
    else
        if [ "$expected_result" = "should_fail" ]; then
            echo -e "${GREEN}通过${NC} (期望失败)"
            PASS_COUNT=$((PASS_COUNT + 1))
        else
            echo -e "${RED}失败${NC}"
            FAIL_COUNT=$((FAIL_COUNT + 1))
        fi
    fi
}

echo "1. 检查oil包相关目录是否已删除..."
echo "----------------------------------------"

# 检查Domain目录
verify_step "Domain oil目录不存在" "[ ! -d '../ms-transport-system/src/main/java/com/ruoyi/system/domain/oil' ]" "should_pass"

# 检查Mapper目录
verify_step "Mapper oil目录不存在" "[ ! -d '../ms-transport-system/src/main/java/com/ruoyi/system/mapper/oil' ]" "should_pass"

# 检查Service目录
verify_step "Service oil目录不存在" "[ ! -d '../ms-transport-system/src/main/java/com/ruoyi/system/service/oil' ]" "should_pass"

# 检查Controller目录
verify_step "Controller oil目录不存在" "[ ! -d '../ms-transport-admin/src/main/java/com/ruoyi/web/controller/oil' ]" "should_pass"

# 检查Mapper XML目录
verify_step "Mapper XML oil目录不存在" "[ ! -d '../ms-transport-system/src/main/resources/mapper/oil' ]" "should_pass"

echo ""
echo "2. 检查代码中是否还有oil包引用..."
echo "----------------------------------------"

# 检查Java文件中的oil包引用
OIL_IMPORTS=$(find ../ms-transport-system/src/main/java -name "*.java" -exec grep -l "import.*\.oil\." {} \; 2>/dev/null | wc -l)
verify_step "Java文件中无oil包import引用" "[ $OIL_IMPORTS -eq 0 ]" "should_pass"

# 检查Controller中的oil包引用
OIL_CONTROLLER_REFS=$(find ../ms-transport-admin/src/main/java -name "*.java" -exec grep -l "import.*\.oil\." {} \; 2>/dev/null | wc -l)
verify_step "Controller中无oil包引用" "[ $OIL_CONTROLLER_REFS -eq 0 ]" "should_pass"

echo ""
echo "3. 检查transport包功能是否完整..."
echo "----------------------------------------"

# 检查Transport实体类
verify_step "Transport实体类存在" "[ -d '../ms-transport-system/src/main/java/com/ruoyi/system/domain/transport' ]" "should_pass"

# 检查Transport地址实体
verify_step "Transport地址实体存在" "[ -f '../ms-transport-system/src/main/java/com/ruoyi/system/domain/transport/TransportCountries.java' ]" "should_pass"

# 检查Transport Service
verify_step "Transport Service存在" "[ -d '../ms-transport-system/src/main/java/com/ruoyi/system/service/transport' ]" "should_pass"

# 检查Transport Controller
verify_step "Transport Controller存在" "[ -d '../ms-transport-admin/src/main/java/com/ruoyi/web/controller/transport' ]" "should_pass"

# 检查Transport地址Controller
verify_step "Transport地址Controller存在" "[ -f '../ms-transport-admin/src/main/java/com/ruoyi/web/controller/transport/TransportCountriesController.java' ]" "should_pass"

echo ""
echo "4. 检查配置文件..."
echo "----------------------------------------"

# 检查MyBatis配置
verify_step "MyBatis配置正常" "grep -q 'typeAliasesPackage.*transport' ../ms-transport-admin/src/main/resources/application.yml" "should_pass"

echo ""
echo "5. 项目结构验证..."
echo "----------------------------------------"

# 统计transport相关文件数量
TRANSPORT_ENTITIES=$(find ../ms-transport-system/src/main/java/com/ruoyi/system/domain/transport -name "*.java" 2>/dev/null | wc -l)
verify_step "Transport实体类数量充足" "[ $TRANSPORT_ENTITIES -gt 10 ]" "should_pass"

TRANSPORT_SERVICES=$(find ../ms-transport-system/src/main/java/com/ruoyi/system/service/transport -name "*.java" 2>/dev/null | wc -l)
verify_step "Transport服务类数量充足" "[ $TRANSPORT_SERVICES -gt 5 ]" "should_pass"

TRANSPORT_CONTROLLERS=$(find ../ms-transport-admin/src/main/java/com/ruoyi/web/controller/transport -name "*.java" 2>/dev/null | wc -l)
verify_step "Transport控制器数量充足" "[ $TRANSPORT_CONTROLLERS -gt 5 ]" "should_pass"

echo ""
echo "=========================================="
echo "验证结果汇总"
echo "=========================================="
echo -e "通过: ${GREEN}$PASS_COUNT${NC} 项"
echo -e "失败: ${RED}$FAIL_COUNT${NC} 项"
echo "总计: $((PASS_COUNT + FAIL_COUNT)) 项"

if [ $FAIL_COUNT -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ 所有验证项目都通过了！${NC}"
    echo -e "${GREEN}Oil包已成功删除，Transport包功能完整。${NC}"
    echo ""
    echo "建议下一步操作："
    echo "1. 编译项目: mvn clean compile"
    echo "2. 运行测试: mvn test"
    echo "3. 启动应用: mvn spring-boot:run"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 发现 $FAIL_COUNT 个问题需要处理${NC}"
    echo ""
    echo "请检查失败的验证项目并进行修复。"
    exit 1
fi
