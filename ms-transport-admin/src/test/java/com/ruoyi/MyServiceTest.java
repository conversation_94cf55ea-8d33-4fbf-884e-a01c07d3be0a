package com.ruoyi;

import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
public class MyServiceTest {

  /*  @Autowired
    private OilCategoryService oilCategoryService;

    @Autowired
    private OilInventoryService inventoryService;

    @Test
    public void testMyService() {
        OilCategory category = new OilCategory();
        category.setName("11");
        // 测试代码
        oilCategoryService.findAllByCondition(category);
    }

    @Test
    public void testMyService2() {
    *//*    OilInventory oilInventory = new OilInventory();
        oilInventory.setId(18l);
        oilInventory.setWarehouseId(1L);
        oilInventory.setWarehouseName("仓库1");
        oilInventory.setSupplierId(2l);
        oilInventory.setSupplierName("供应商2");
        oilInventory.setProductId(1L);
        oilInventory.setProductName("产品1");
        oilInventory.setCost(new BigDecimal(19.89));
        oilInventory.setQuantity(100);
        // 测试代码
        inventoryService.update(oilInventory);*//*
    }*/

/*    @Resource
    private OilShippingRateService shippingRateService;

    @Test
    public void testUpdate() {
        OilShippingRate oilShippingRate = new OilShippingRate();
        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", 1);
        params.put("pageSize", 10);
        oilShippingRate.setParams(params);
        oilShippingRate.setCompanyId(2L);
        List<OilShippingRate> list = shippingRateService.findAllByCondition(oilShippingRate);
        System.out.println(JSON.toJSONString(list));
    }*/

}
