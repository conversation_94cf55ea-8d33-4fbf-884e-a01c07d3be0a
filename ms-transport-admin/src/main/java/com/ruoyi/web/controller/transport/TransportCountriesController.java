package com.ruoyi.web.controller.transport;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.transport.TransportCities;
import com.ruoyi.system.domain.transport.TransportCountries;
import com.ruoyi.system.domain.transport.TransportDistricts;
import com.ruoyi.system.domain.transport.TransportProvinces;
import com.ruoyi.system.service.transport.TransportCountriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/countries")
@Api(tags = "运输管理地址管理")
public class TransportCountriesController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(TransportCountriesController.class);

    @Autowired
    private TransportCountriesService transportCountriesService;

    @ApiOperation(value = "查询国家列表", notes = "权限：'transport:countries:list'")
    @PreAuthorize("@ss.hasPermi('transport:countries:list')")
    @GetMapping("/list")
    public AjaxResult list() {
        try {
            return AjaxResult.success(transportCountriesService.selectCountriesList());
        } catch (ServiceException e) {
            log.error("查询国家列表业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询国家列表系统异常", e);
            return AjaxResult.error("查询国家列表异常，请联系管理员");
        }
    }

    /**
     * 查询省份列表
     */
    @ApiOperation(value = "根据国家编码查询省份列表", notes = "权限：'transport:countries:list'")
    @PreAuthorize("@ss.hasPermi('transport:countries:list')")
    @GetMapping("/provinces/{countryCode}")
    public AjaxResult provinces(@ApiParam("国家编码") @PathVariable("countryCode") String countryCode) {
        try {
            return AjaxResult.success(transportCountriesService.selectProvincesList(countryCode));
        } catch (ServiceException e) {
            log.error("查询省份列表业务异常, countryCode: {}", countryCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询省份列表系统异常, countryCode: {}", countryCode, e);
            return AjaxResult.error("查询省份列表异常，请联系管理员");
        }
    }

    /**
     * 查询城市列表
     */
    @ApiOperation(value = "根据省份编码查询城市列表", notes = "权限：'transport:countries:list'")
    @PreAuthorize("@ss.hasPermi('transport:countries:list')")
    @GetMapping("/cities/{provinceCode}")
    public AjaxResult cities(@ApiParam("省份编码") @PathVariable("provinceCode") String provinceCode) {
        try {
            return AjaxResult.success(transportCountriesService.selectCitiesList(provinceCode));
        } catch (ServiceException e) {
            log.error("查询城市列表业务异常, provinceCode: {}", provinceCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询城市列表系统异常, provinceCode: {}", provinceCode, e);
            return AjaxResult.error("查询城市列表异常，请联系管理员");
        }
    }

    /**
     * 查询区县列表
     */
    @ApiOperation(value = "根据城市编码查询区县列表", notes = "权限：'transport:countries:list'")
    @PreAuthorize("@ss.hasPermi('transport:countries:list')")
    @GetMapping("/districts/{cityCode}")
    public AjaxResult districts(@ApiParam("城市编码") @PathVariable("cityCode") String cityCode) {
        try {
            return AjaxResult.success(transportCountriesService.selectDistrictsList(cityCode));
        } catch (ServiceException e) {
            log.error("查询区县列表业务异常, cityCode: {}", cityCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询区县列表系统异常, cityCode: {}", cityCode, e);
            return AjaxResult.error("查询区县列表异常，请联系管理员");
        }
    }

    /**
     * 根据国家编码查询国家信息
     */
    @ApiOperation(value = "根据国家编码查询国家信息", notes = "权限：'transport:countries:query'")
    @PreAuthorize("@ss.hasPermi('transport:countries:query')")
    @GetMapping("/country/{code}")
    public AjaxResult getCountryByCode(@ApiParam("国家编码") @PathVariable("code") String code) {
        try {
            return AjaxResult.success(transportCountriesService.selectCountriesByCode(code));
        } catch (ServiceException e) {
            log.error("根据国家编码查询国家信息业务异常, code: {}", code, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据国家编码查询国家信息系统异常, code: {}", code, e);
            return AjaxResult.error("根据国家编码查询国家信息异常，请联系管理员");
        }
    }

    /**
     * 根据省份编码查询省份信息
     */
    @ApiOperation(value = "根据省份编码查询省份信息", notes = "权限：'transport:countries:query'")
    @PreAuthorize("@ss.hasPermi('transport:countries:query')")
    @GetMapping("/province/{code}")
    public AjaxResult getProvinceByCode(@ApiParam("省份编码") @PathVariable("code") String code) {
        try {
            return AjaxResult.success(transportCountriesService.selectProvincesByCode(code));
        } catch (ServiceException e) {
            log.error("根据省份编码查询省份信息业务异常, code: {}", code, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据省份编码查询省份信息系统异常, code: {}", code, e);
            return AjaxResult.error("根据省份编码查询省份信息异常，请联系管理员");
        }
    }

    /**
     * 根据城市编码查询城市信息
     */
    @ApiOperation(value = "根据城市编码查询城市信息", notes = "权限：'transport:countries:query'")
    @PreAuthorize("@ss.hasPermi('transport:countries:query')")
    @GetMapping("/city/{code}")
    public AjaxResult getCityByCode(@ApiParam("城市编码") @PathVariable("code") String code) {
        try {
            return AjaxResult.success(transportCountriesService.selectCitiesByCode(code));
        } catch (ServiceException e) {
            log.error("根据城市编码查询城市信息业务异常, code: {}", code, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据城市编码查询城市信息系统异常, code: {}", code, e);
            return AjaxResult.error("根据城市编码查询城市信息异常，请联系管理员");
        }
    }

    /**
     * 根据区县编码查询区县信息
     */
    @ApiOperation(value = "根据区县编码查询区县信息", notes = "权限：'transport:countries:query'")
    @PreAuthorize("@ss.hasPermi('transport:countries:query')")
    @GetMapping("/district/{code}")
    public AjaxResult getDistrictByCode(@ApiParam("区县编码") @PathVariable("code") String code) {
        try {
            return AjaxResult.success(transportCountriesService.selectDistrictsByCode(code));
        } catch (ServiceException e) {
            log.error("根据区县编码查询区县信息业务异常, code: {}", code, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据区县编码查询区县信息系统异常, code: {}", code, e);
            return AjaxResult.error("根据区县编码查询区县信息异常，请联系管理员");
        }
    }

    @ApiOperation(value = "查询地址级联数据", notes = "权限：'transport:address:for:select'")
    @PreAuthorize("@ss.hasPermi('transport:address:for:select')")
    @GetMapping("/listAddress")
    public AjaxResult listAddress() {
        try {
            return AjaxResult.success(transportCountriesService.listAddress());
        } catch (ServiceException e) {
            log.error("查询地址级联数据业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询地址级联数据系统异常", e);
            return AjaxResult.error("查询地址级联数据异常，请联系管理员");
        }
    }

}