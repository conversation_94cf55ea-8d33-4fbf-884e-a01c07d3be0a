package com.ruoyi.web.controller.transport;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.dto.TransportShippingRateDetails;
import com.ruoyi.system.domain.transport.TransportShippingRate;
import com.ruoyi.system.service.transport.ITransportExcelProcessService;
import com.ruoyi.system.service.transport.ITransportShippingRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * 运费定价管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(value = "运费定价管理")
@RestController
@RequestMapping("/shipping/rate")
public class TransportShippingRateController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(TransportShippingRateController.class);
    
    @Autowired
    private ITransportShippingRateService transportShippingRateService;
    
    @Autowired
    private ITransportExcelProcessService excelProcessService;

    @ApiOperation(value = "查询运费定价列表", notes = "权限：'transport:shipping:rate:list'")
    @PreAuthorize("@ss.hasPermi('transport:shipping:rate:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportShippingRate condition) {
        try {
            startPage();
            List<TransportShippingRate> list = transportShippingRateService.findAllByCondition(condition);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询运费定价列表异常, request: {}", condition, e);
            return new TableDataInfo();
        }
    }

    @ApiOperation(value = "查询运费定价详情", notes = "权限：'transport:shipping:rate:query'")
    @PreAuthorize("@ss.hasPermi('transport:shipping:rate:query')")
    @GetMapping("/{id}")
    public AjaxResult getTransportShippingRateById(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return error("输入数据无效或ID不合法");
            }
            return success(transportShippingRateService.findById(id));
        } catch (ServiceException e) {
            log.error("查询运费定价详情业务异常，id：{}", id, e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("查询运费定价详情系统异常，id：{}", id, e);
            return error("查询运费定价详情失败，请联系管理员");
        }
    }

    @ApiOperation(value = "新增运费定价", notes = "权限：'transport:shipping:rate:add'")
    @PreAuthorize("@ss.hasPermi('transport:shipping:rate:add')")
    @Log(title = "运费定价管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult createTransportShippingRate(@Validated @RequestBody TransportShippingRate transportShippingRate) {
        try {
            transportShippingRate.setCreateBy(getUsername());
            return toAjax(transportShippingRateService.insert(transportShippingRate));
        } catch (ServiceException e) {
            log.error("新增运费定价业务异常，request：{}", JSON.toJSONString(transportShippingRate), e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("新增运费定价系统异常，request：{}", JSON.toJSONString(transportShippingRate), e);
            return error("新增运费定价失败，请联系管理员");
        }
    }

    @ApiOperation(value = "修改运费定价", notes = "权限：'transport:shipping:rate:edit'")
    @PreAuthorize("@ss.hasPermi('transport:shipping:rate:edit')")
    @Log(title = "运费定价管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public AjaxResult updateTransportShippingRate(@PathVariable Long id, @RequestBody TransportShippingRate transportShippingRate) {
        try {
            if (id == null || id <= 0) {
                return error("输入数据无效或ID不合法");
            }
            transportShippingRate.setId(id);
            transportShippingRate.setUpdateBy(getUsername());
            return toAjax(transportShippingRateService.update(transportShippingRate));
        } catch (ServiceException e) {
            log.error("修改运费定价业务异常，request：{}", JSON.toJSONString(transportShippingRate), e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("修改运费定价系统异常，request：{}", JSON.toJSONString(transportShippingRate), e);
            return error("修改运费定价失败，请联系管理员");
        }
    }

    @ApiOperation(value = "删除运费定价", notes = "权限：'transport:shipping:rate:remove'")
    @PreAuthorize("@ss.hasPermi('transport:shipping:rate:remove')")
    @Log(title = "运费定价管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult deleteTransportShippingRate(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return error("ID不合法");
            }
            return toAjax(transportShippingRateService.delete(id));
        } catch (ServiceException e) {
            log.error("删除运费定价业务异常，id：{}", id, e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("删除运费定价系统异常，id：{}", id, e);
            return error("删除运费定价失败，请联系管理员");
        }
    }

    @ApiOperation(value = "导出运费定价", notes = "权限：'transport:shipping:rate:export'")
    @PreAuthorize("@ss.hasPermi('transport:shipping:rate:export')")
    @Log(title = "运费定价管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void exportToExcel(HttpServletResponse response, TransportShippingRate transportShippingRate) {
        try {
            List<TransportShippingRate> list = transportShippingRateService.listShippingRateData(transportShippingRate);
            excelProcessService.exportShippingRate(list, response);
        } catch (Exception e) {
            log.error("导出运费定价异常, request: {}", JSON.toJSONString(transportShippingRate), e);
        }
    }

    @ApiOperation(value = "导入运费定价", notes = "权限：'transport:shipping:rate:import'")
    @PreAuthorize("@ss.hasPermi('transport:shipping:rate:import')")
    @Log(title = "运费定价管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importToExcel")
    public AjaxResult importToExcel(@RequestParam("file") MultipartFile file) {
        try {
            List<TransportShippingRateDetails> details = excelProcessService.importExcelForShippingRate(file);
            if (CollectionUtils.isEmpty(details)) {
                return error("导入文件内容为空");
            }
            transportShippingRateService.batchUpdate(details);
            return success("导入成功");
        } catch (ServiceException e) {
            log.error("导入运费定价业务异常", e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("导入运费定价系统异常", e);
            return error("导入运费定价失败，请联系管理员");
        }
    }
}
