package com.ruoyi.web.controller.transport;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.dto.TransportDriverDropdownDto;
import com.ruoyi.system.domain.transport.TransportDriver;
import com.ruoyi.system.service.transport.ITransportDriverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 司机信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "司机信息管理")
@RestController
@RequestMapping("/driver")
public class TransportDriverController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TransportDriverController.class);

    @Autowired
    private ITransportDriverService transportDriverService;

    /**
     * 查询司机信息列表
     */
    @ApiOperation(value = "查询司机信息列表", notes = "权限：'transport:driver:list'")
    @PreAuthorize("@ss.hasPermi('transport:driver:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportDriver transportDriver) {
        try {
            startPage();
            List<TransportDriver> list = transportDriverService.selectTransportDriverList(transportDriver);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询司机信息列表异常, request: {}", transportDriver, e);
            return new TableDataInfo();
        }
    }

    /**
     * 导出司机信息列表
     */
    @ApiOperation(value = "导出司机信息列表", notes = "权限：'transport:driver:export'")
    @PreAuthorize("@ss.hasPermi('transport:driver:export')")
    @Log(title = "司机信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, TransportDriver transportDriver) {
        try {
            List<TransportDriver> list = transportDriverService.selectTransportDriverList(transportDriver);
            ExcelUtil<TransportDriver> util = new ExcelUtil<TransportDriver>(TransportDriver.class);
            util.exportExcel(response, list, "司机信息数据");
        } catch (Exception e) {
            log.error("导出司机信息列表异常, request: {}", transportDriver, e);
        }
    }

    /**
     * 获取司机信息详细信息
     */
    @ApiOperation(value = "获取司机信息详细信息", notes = "权限：'transport:driver:query'")
    @PreAuthorize("@ss.hasPermi('transport:driver:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("司机ID") @PathVariable("id") Long id) {
        try {
            return AjaxResult.success(transportDriverService.selectTransportDriverById(id));
        } catch (ServiceException e) {
            log.error("获取司机信息详细信息业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取司机信息详细信息系统异常, id: {}", id, e);
            return AjaxResult.error("获取司机信息详细信息失败，请联系管理员");
        }
    }

    /**
     * 新增司机信息
     */
    @ApiOperation(value = "新增司机信息", notes = "权限：'transport:driver:add'")
    @PreAuthorize("@ss.hasPermi('transport:driver:add')")
    @Log(title = "司机信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportDriver transportDriver) {
        try {
            return toAjax(transportDriverService.insertTransportDriver(transportDriver));
        } catch (ServiceException e) {
            log.error("新增司机信息业务异常, request: {}", transportDriver, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增司机信息系统异常, request: {}", transportDriver, e);
            return AjaxResult.error("新增司机信息失败，请联系管理员");
        }
    }

    /**
     * 修改司机信息
     */
    @ApiOperation(value = "修改司机信息", notes = "权限：'transport:driver:edit'")
    @PreAuthorize("@ss.hasPermi('transport:driver:edit')")
    @Log(title = "司机信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportDriver transportDriver) {
        try {
            return toAjax(transportDriverService.updateTransportDriver(transportDriver));
        } catch (ServiceException e) {
            log.error("修改司机信息业务异常, request: {}", transportDriver, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改司机信息系统异常, request: {}", transportDriver, e);
            return AjaxResult.error("修改司机信息失败，请联系管理员");
        }
    }

    /**
     * 删除司机信息
     */
    @ApiOperation(value = "删除司机信息", notes = "权限：'transport:driver:remove'")
    @PreAuthorize("@ss.hasPermi('transport:driver:remove')")
    @Log(title = "司机信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("司机ID数组") @PathVariable Long[] ids) {
        try {
            return toAjax(transportDriverService.deleteTransportDriverByIds(ids));
        } catch (ServiceException e) {
            log.error("删除司机信息业务异常, ids: {}", ids, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除司机信息系统异常, ids: {}", ids, e);
            return AjaxResult.error("删除司机信息失败，请联系管理员");
        }
    }

    /**
     * 根据手机号查询司机
     */
    @ApiOperation(value = "根据手机号查询司机", notes = "精确匹配查询单个司机信息")
    @GetMapping("/phone/{driverPhone}")
    public AjaxResult getDriverByPhone(@ApiParam("手机号") @PathVariable("driverPhone") String driverPhone) {
        try {
            return AjaxResult.success(transportDriverService.selectDriverByPhone(driverPhone));
        } catch (ServiceException e) {
            log.error("根据手机号查询司机业务异常, driverPhone: {}", driverPhone, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据手机号查询司机系统异常, driverPhone: {}", driverPhone, e);
            return AjaxResult.error("根据手机号查询司机失败，请联系管理员");
        }
    }

    /**
     * 检查手机号是否存在
     */
    @ApiOperation(value = "检查手机号是否存在", notes = "检查手机号是否已存在，用于新建或修改时的唯一性校验")
    @GetMapping("/checkPhone/{driverPhone}")
    public AjaxResult checkPhoneExists(@ApiParam("手机号") @PathVariable("driverPhone") String driverPhone,
                                     @ApiParam("司机ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            boolean exists = transportDriverService.checkPhoneExists(driverPhone, id);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查手机号是否存在业务异常, driverPhone: {}, id: {}", driverPhone, id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查手机号是否存在系统异常, driverPhone: {}, id: {}", driverPhone, id, e);
            return AjaxResult.error("检查手机号是否存在失败，请联系管理员");
        }
    }

    /**
     * 检查身份证号是否存在
     */
    @ApiOperation(value = "检查身份证号是否存在", notes = "检查身份证号是否已存在，用于新建或修改时的唯一性校验")
    @GetMapping("/checkIdCard/{idCard}")
    public AjaxResult checkIdCardExists(@ApiParam("身份证号") @PathVariable("idCard") String idCard,
                                      @ApiParam("司机ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            boolean exists = transportDriverService.checkIdCardExists(idCard, id);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查身份证号是否存在业务异常, idCard: {}, id: {}", idCard, id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查身份证号是否存在系统异常, idCard: {}, id: {}", idCard, id, e);
            return AjaxResult.error("检查身份证号是否存在失败，请联系管理员");
        }
    }

    /**
     * 查询可用司机列表
     */
    @ApiOperation(value = "查询可用司机列表", notes = "查询所有状态为可用的司机信息")
    @GetMapping("/available")
    public AjaxResult getAvailableDrivers() {
        try {
            return AjaxResult.success(transportDriverService.selectAvailableDrivers());
        } catch (ServiceException e) {
            log.error("查询可用司机列表业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询可用司机列表系统异常", e);
            return AjaxResult.error("查询可用司机列表失败，请联系管理员");
        }
    }

    /**
     * 更新司机状态
     */
    @ApiOperation(value = "更新司机状态", notes = "权限：'transport:driver:edit'")
    @PreAuthorize("@ss.hasPermi('transport:driver:edit')")
    @Log(title = "司机状态更新", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{status}")
    public AjaxResult updateStatus(@ApiParam("司机ID") @PathVariable("id") Long id, 
                                 @ApiParam("新状态") @PathVariable("status") Integer status) {
        try {
            return toAjax(transportDriverService.updateDriverStatus(id, status));
        } catch (ServiceException e) {
            log.error("更新司机状态业务异常, id: {}, status: {}", id, status, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新司机状态系统异常, id: {}, status: {}", id, status, e);
            return AjaxResult.error("更新司机状态失败，请联系管理员");
        }
    }

    /**
     * 统计各状态司机数量
     */
    @ApiOperation(value = "统计各状态司机数量", notes = "按状态分组统计司机数量")
    @GetMapping("/statistics/status")
    public AjaxResult getDriverStatusStatistics() {
        try {
            return AjaxResult.success(transportDriverService.selectDriverStatusStatistics());
        } catch (ServiceException e) {
            log.error("统计各状态司机数量业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("统计各状态司机数量系统异常", e);
            return AjaxResult.error("统计各状态司机数量失败，请联系管理员");
        }
    }

    /**
     * 根据驾照类型查询司机
     */
    @ApiOperation(value = "根据驾照类型查询司机", notes = "查询持有特定驾照类型的所有司机")
    @GetMapping("/licenseType/{licenseType}")
    public AjaxResult getDriversByLicenseType(@ApiParam("驾照类型") @PathVariable("licenseType") String licenseType) {
        try {
            return AjaxResult.success(transportDriverService.selectDriversByLicenseType(licenseType));
        } catch (ServiceException e) {
            log.error("根据驾照类型查询司机业务异常, licenseType: {}", licenseType, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据驾照类型查询司机系统异常, licenseType: {}", licenseType, e);
            return AjaxResult.error("根据驾照类型查询司机失败，请联系管理员");
        }
    }

    /**
     * 查询经验丰富的司机
     */
    @ApiOperation(value = "查询经验丰富的司机", notes = "查询驾龄超过指定年限的司机")
    @GetMapping("/experienced")
    public AjaxResult getExperiencedDrivers(@ApiParam("驾龄年数") @RequestParam(value = "years", defaultValue = "5") Integer years) {
        try {
            return AjaxResult.success(transportDriverService.selectExperiencedDrivers(years));
        } catch (ServiceException e) {
            log.error("查询经验丰富的司机业务异常, years: {}", years, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询经验丰富的司机系统异常, years: {}", years, e);
            return AjaxResult.error("查询经验丰富的司机失败，请联系管理员");
        }
    }

    /**
     * 查询司机下拉列表
     */
    @ApiOperation(value = "查询司机下拉列表", notes = "用于下拉选择框，支持关键字搜索")
    @GetMapping("/dropdownList")
    public AjaxResult dropdownList(@ApiParam("关键字") @RequestParam(value = "keyword", required = false) String keyword) {
        try {
            List<TransportDriverDropdownDto> list = transportDriverService.selectDriverDropdownList(keyword);
            return AjaxResult.success(list);
        } catch (ServiceException e) {
            log.error("查询司机下拉列表业务异常, keyword: {}", keyword, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询司机下拉列表系统异常, keyword: {}", keyword, e);
            return AjaxResult.error("查询司机下拉列表失败，请联系管理员");
        }
    }
}
