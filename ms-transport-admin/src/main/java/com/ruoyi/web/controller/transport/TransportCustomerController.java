package com.ruoyi.web.controller.transport;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.dto.TransportCustomerDropdownDto;
import com.ruoyi.system.domain.transport.TransportCustomer;
import com.ruoyi.system.service.transport.ITransportCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "客户信息管理")
@RestController
@RequestMapping("/customer")
public class TransportCustomerController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TransportCustomerController.class);

    @Autowired
    private ITransportCustomerService transportCustomerService;

    /**
     * 查询客户信息列表
     */
    @ApiOperation(value = "查询客户信息列表", notes = "权限：'transport:customer:list'")
    @PreAuthorize("@ss.hasPermi('transport:customer:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportCustomer transportCustomer) {
        try {
            startPage();
            List<TransportCustomer> list = transportCustomerService.selectTransportCustomerList(transportCustomer);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询客户信息列表异常, request: {}", transportCustomer, e);
            // For TableDataInfo, we can't directly return an error message in the same format.
            // Returning an empty list is a common practice, and the error is logged.
            return new TableDataInfo();
        }
    }

    /**
     * 导出客户信息列表
     */
    @ApiOperation(value = "导出客户信息列表", notes = "权限：'transport:customer:export'")
    @PreAuthorize("@ss.hasPermi('transport:customer:export')")
    @Log(title = "客户信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, TransportCustomer transportCustomer) {
        try {
            List<TransportCustomer> list = transportCustomerService.selectTransportCustomerList(transportCustomer);
            ExcelUtil<TransportCustomer> util = new ExcelUtil<TransportCustomer>(TransportCustomer.class);
            util.exportExcel(response, list, "客户信息数据");
        } catch (Exception e) {
            log.error("导出客户信息列表异常, request: {}", transportCustomer, e);
        }
    }

    /**
     * 获取客户信息详细信息
     */
    @ApiOperation(value = "获取客户信息详细信息", notes = "权限：'transport:customer:query'")
    @PreAuthorize("@ss.hasPermi('transport:customer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("客户ID") @PathVariable("id") Long id) {
        try {
            return AjaxResult.success(transportCustomerService.selectTransportCustomerById(id));
        } catch (ServiceException e) {
            log.error("获取客户信息详细信息业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取客户信息详细信息系统异常, id: {}", id, e);
            return AjaxResult.error("获取客户信息详细信息失败，请联系管理员");
        }
    }

    /**
     * 新增客户信息
     */
    @ApiOperation(value = "新增客户信息", notes = "权限：'transport:customer:add'")
    @PreAuthorize("@ss.hasPermi('transport:customer:add')")
    @Log(title = "客户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportCustomer transportCustomer) {
        try {
            return toAjax(transportCustomerService.insertTransportCustomer(transportCustomer));
        } catch (ServiceException e) {
            log.error("新增客户信息业务异常, request: {}", transportCustomer, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增客户信息系统异常, request: {}", transportCustomer, e);
            return AjaxResult.error("新增客户信息失败，请联系管理员");
        }
    }

    /**
     * 修改客户信息
     */
    @ApiOperation(value = "修改客户信息", notes = "权限：'transport:customer:edit'")
    @PreAuthorize("@ss.hasPermi('transport:customer:edit')")
    @Log(title = "客户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportCustomer transportCustomer) {
        try {
            return toAjax(transportCustomerService.updateTransportCustomer(transportCustomer));
        } catch (ServiceException e) {
            log.error("修改客户信息业务异常, request: {}", transportCustomer, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改客户信息系统异常, request: {}", transportCustomer, e);
            return AjaxResult.error("修改客户信息失败，请联系管理员");
        }
    }

    /**
     * 删除客户信息
     */
    @ApiOperation(value = "删除客户信息", notes = "权限：'transport:customer:remove'")
    @PreAuthorize("@ss.hasPermi('transport:customer:remove')")
    @Log(title = "客户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("客户ID数组") @PathVariable Long[] ids) {
        try {
            return toAjax(transportCustomerService.deleteTransportCustomerByIds(ids));
        } catch (ServiceException e) {
            log.error("删除客户信息业务异常, ids: {}", ids, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除客户信息系统异常, ids: {}", ids, e);
            return AjaxResult.error("删除客户信息失败，请联系管理员");
        }
    }

    /**
     * 检查客户编码是否存在
     */
    @ApiOperation("检查客户编码是否存在")
    @GetMapping("/checkCode/{customerCode}")
    public AjaxResult checkCustomerCodeExists(@ApiParam("客户编码") @PathVariable("customerCode") String customerCode,
                                            @ApiParam("客户ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            boolean exists = transportCustomerService.checkCustomerCodeExists(customerCode, id);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查客户编码是否存在业务异常, customerCode: {}, id: {}", customerCode, id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查客户编码是否存在系统异常, customerCode: {}, id: {}", customerCode, id, e);
            return AjaxResult.error("检查客户编码是否存在异常，请联系管理员");
        }
    }

    /**
     * 检查客户名称是否存在
     */
    @ApiOperation("检查客户名称是否存在")
    @GetMapping("/checkName/{customerName}")
    public AjaxResult checkCustomerNameExists(@ApiParam("客户名称") @PathVariable("customerName") String customerName,
                                            @ApiParam("客户ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            boolean exists = transportCustomerService.checkCustomerNameExists(customerName, id);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查客户名称是否存在业务异常, customerName: {}, id: {}", customerName, id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查客户名称是否存在系统异常, customerName: {}, id: {}", customerName, id, e);
            return AjaxResult.error("检查客户名称是否存在异常，请联系管理员");
        }
    }

    /**
     * 查询客户下拉列表
     */
    @ApiOperation("查询客户下拉列表")
    @GetMapping("/dropdownList")
    public AjaxResult dropdownList(@ApiParam("关键字") @RequestParam(value = "keyword", required = false) String keyword) {
        try {
            List<TransportCustomerDropdownDto> list = transportCustomerService.selectCustomerDropdownList(keyword);
            return AjaxResult.success(list);
        } catch (ServiceException e) {
            log.error("查询客户下拉列表业务异常, keyword: {}", keyword, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询客户下拉列表系统异常, keyword: {}", keyword, e);
            return AjaxResult.error("查询客户下拉列表失败，请联系管理员");
        }
    }
}
