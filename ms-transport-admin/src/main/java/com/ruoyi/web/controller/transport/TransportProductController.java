package com.ruoyi.web.controller.transport;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.TransportProduct;
import com.ruoyi.system.service.ITransportProductService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;

/**
 * 油品商品表Controller
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
@Api(tags = "油品商品表")
@RestController
@RequestMapping("/product")
public class TransportProductController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(TransportProductController.class);

    @Autowired
    private ITransportProductService transportProductService;

    /**
     * 查询油品商品表列表
     */
    @ApiOperation(value = "查询油品商品表列表", notes = "权限：'transport:product:list'")
    @PreAuthorize("@ss.hasPermi('transport:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportProduct transportProduct)
    {
        try {
            startPage();
            List<TransportProduct> list = transportProductService.selectTransportProductList(transportProduct);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询油品商品表列表异常, request: {}", transportProduct, e);
            return new TableDataInfo();
        }
    }

    /**
     * 导出油品商品表列表
     */
    @ApiOperation(value = "导出油品商品表列表", notes = "权限：'transport:product:export'")
    @PreAuthorize("@ss.hasPermi('transport:product:export')")
    @Log(title = "油品商品表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TransportProduct transportProduct)
    {
        try {
            List<TransportProduct> list = transportProductService.selectTransportProductList(transportProduct);
            ExcelUtil<TransportProduct> util = new ExcelUtil<TransportProduct>(TransportProduct.class);
            util.exportExcel(response, list, "油品商品表数据");
        } catch (Exception e) {
            log.error("导出油品商品表列表异常, request: {}", transportProduct, e);
        }
    }

    /**
     * 获取油品商品表详细信息
     */
    @ApiOperation(value = "获取油品商品表详细信息", notes = "权限：'transport:product:query'")
    @PreAuthorize("@ss.hasPermi('transport:product:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        try {
            return success(transportProductService.selectTransportProductById(id));
        } catch (ServiceException e) {
            log.error("获取油品商品表详细信息业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取油品商品表详细信息系统异常, id: {}", id, e);
            return AjaxResult.error("获取油品商品表详细信息失败，请联系管理员");
        }
    }

    /**
     * 新增油品商品表
     */
    @ApiOperation(value = "新增油品商品表", notes = "权限：'transport:product:add'")
    @PreAuthorize("@ss.hasPermi('transport:product:add')")
    @Log(title = "油品商品表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportProduct transportProduct)
    {
        try {
            return toAjax(transportProductService.insertTransportProduct(transportProduct));
        } catch (ServiceException e) {
            log.error("新增油品商品表业务异常, request: {}", transportProduct, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增油品商品表系统异常, request: {}", transportProduct, e);
            return AjaxResult.error("新增油品商品表失败，请联系管理员");
        }
    }

    /**
     * 修改油品商品表
     */
    @ApiOperation(value = "修改油品商品表", notes = "权限：'transport:product:edit'")
    @PreAuthorize("@ss.hasPermi('transport:product:edit')")
    @Log(title = "油品商品表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportProduct transportProduct)
    {
        try {
            return toAjax(transportProductService.updateTransportProduct(transportProduct));
        } catch (ServiceException e) {
            log.error("修改油品商品表业务异常, request: {}", transportProduct, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改油品商品表系统异常, request: {}", transportProduct, e);
            return AjaxResult.error("修改油品商品表失败，请联系管理员");
        }
    }

    /**
     * 删除油品商品表
     */
    @ApiOperation(value = "删除油品商品表", notes = "权限：'transport:product:remove'")
    @PreAuthorize("@ss.hasPermi('transport:product:remove')")
    @Log(title = "油品商品表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        try {
            return toAjax(transportProductService.deleteTransportProductByIds(ids));
        } catch (ServiceException e) {
            log.error("删除油品商品表业务异常, ids: {}", ids, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除油品商品表系统异常, ids: {}", ids, e);
            return AjaxResult.error("删除油品商品表失败，请联系管理员");
        }
    }
}
