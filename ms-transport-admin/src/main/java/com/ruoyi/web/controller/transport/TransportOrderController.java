package com.ruoyi.web.controller.transport;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.dto.RateDetailsResult;
import com.ruoyi.system.domain.transport.TransportOrder;
import com.ruoyi.system.service.transport.ITransportOrderService;
import com.ruoyi.system.service.transport.ITransportOrderStatusService;
import com.ruoyi.system.service.transport.ITransportShippingRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 运输单Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "运输单管理")
@RestController
@RequestMapping("/order")
public class TransportOrderController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TransportOrderController.class);

    @Autowired
    private ITransportOrderService transportOrderService;

    @Autowired
    private ITransportOrderStatusService transportOrderStatusService;

    @Autowired
    private ITransportShippingRateService transportShippingRateService;

    /**
     * 查询运输单列表
     */
    @ApiOperation(value = "查询运输单列表", notes = "权限：'transport:order:list'")
    @PreAuthorize("@ss.hasPermi('transport:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportOrder transportOrder) {
        try {
            startPage();
            List<TransportOrder> list = transportOrderService.selectTransportOrderList(transportOrder);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询运输单列表异常, request: {}", transportOrder, e);
            return new TableDataInfo();
        }
    }

    /**
     * 导出运输单列表
     */
    @ApiOperation(value = "导出运输单列表", notes = "权限：'transport:order:export'")
    @PreAuthorize("@ss.hasPermi('transport:order:export')")
    @Log(title = "运输单", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, TransportOrder transportOrder) {
        try {
            List<TransportOrder> list = transportOrderService.selectTransportOrderList(transportOrder);
            ExcelUtil<TransportOrder> util = new ExcelUtil<TransportOrder>(TransportOrder.class);
            util.exportExcel(response, list, "运输单数据");
        } catch (Exception e) {
            log.error("导出运输单列表异常, request: {}", transportOrder, e);
        }
    }

    /**
     * 获取运输单详细信息
     */
    @ApiOperation(value = "获取运输单详细信息", notes = "权限：'transport:order:query'")
    @PreAuthorize("@ss.hasPermi('transport:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("运输单ID") @PathVariable("id") Long id) {
        try {
            return AjaxResult.success(transportOrderService.selectTransportOrderById(id));
        } catch (ServiceException e) {
            log.error("获取运输单详细信息业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取运输单详细信息系统异常, id: {}", id, e);
            return AjaxResult.error("获取运输单详细信息失败，请联系管理员");
        }
    }

    /**
     * 新增运输单
     */
    @ApiOperation(value = "新增运输单", notes = "权限：'transport:order:add'")
    @PreAuthorize("@ss.hasPermi('transport:order:add')")
    @Log(title = "运输单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportOrder transportOrder) {
        try {
            return toAjax(transportOrderService.insertTransportOrder(transportOrder));
        } catch (ServiceException e) {
            log.error("新增运输单业务异常, request: {}", transportOrder, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增运输单系统异常, request: {}", transportOrder, e);
            return AjaxResult.error("新增运输单失败，请联系管理员");
        }
    }

    /**
     * 修改运输单
     */
    @ApiOperation(value = "修改运输单", notes = "权限：'transport:order:edit'")
    @PreAuthorize("@ss.hasPermi('transport:order:edit')")
    @Log(title = "运输单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportOrder transportOrder) {
        try {
            return toAjax(transportOrderService.updateTransportOrder(transportOrder));
        } catch (ServiceException e) {
            log.error("修改运输单业务异常, request: {}", transportOrder, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改运输单系统异常, request: {}", transportOrder, e);
            return AjaxResult.error("修改运输单失败，请联系管理员");
        }
    }

    /**
     * 删除运输单
     */
    @ApiOperation(value = "删除运输单", notes = "权限：'transport:order:remove'")
    @PreAuthorize("@ss.hasPermi('transport:order:remove')")
    @Log(title = "运输单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("运输单ID数组") @PathVariable Long[] ids) {
        try {
            return toAjax(transportOrderService.deleteTransportOrderByIds(ids));
        } catch (ServiceException e) {
            log.error("删除运输单业务异常, ids: {}", ids, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除运输单系统异常, ids: {}", ids, e);
            return AjaxResult.error("删除运输单失败，请联系管理员");
        }
    }

    /**
     * 获取运价并计算总价
     */
    @ApiOperation(value = "获取运价并计算总价", notes = "根据装货点、收货方和体积获取运价规则，计算总价")
    @GetMapping("/rate-details")
    public AjaxResult getRateDetails(@RequestParam("loadingPointId") Long loadingPointId,
                                     @RequestParam("consigneeId") Long consigneeId,
                                     @RequestParam("volume") BigDecimal volume) {
        try {
            RateDetailsResult rateDetails = transportShippingRateService.getRateDetails(loadingPointId, consigneeId, volume);
            return AjaxResult.success(rateDetails);
        } catch (ServiceException e) {
            log.error("获取运价规则明细并计算价格业务异常, loadingPointId: {}, consigneeId: {}, volume: {}", loadingPointId, consigneeId, volume, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取运价规则明细并计算价格系统异常, loadingPointId: {}, consigneeId: {}, volume: {}", loadingPointId, consigneeId, volume, e);
            return AjaxResult.error("获取运价失败，请联系管理员");
        }
    }

    /**
     * 生成运输单号
     */
    @ApiOperation(value = "生成运输单号", notes = "生成一个唯一的运输单号")
    @GetMapping("/generateOrderNo")
    public AjaxResult generateOrderNo() {
        try {
            return AjaxResult.success("data", transportOrderService.generateOrderNo());
        } catch (ServiceException e) {
            log.error("生成运输单号业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("生成运输单号系统异常", e);
            return AjaxResult.error("生成运输单号失败，请联系管理员");
        }
    }

    /**
     * 生成内部编号
     */
    @ApiOperation(value = "生成内部编号", notes = "生成一个唯一的内部编号")
    @GetMapping("/generateInternalCode")
    public AjaxResult generateInternalCode() {
        try {
            return AjaxResult.success("data", transportOrderService.generateInternalCode());
        } catch (ServiceException e) {
            log.error("生成内部编号业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("生成内部编号系统异常", e);
            return AjaxResult.error("生成内部编号失败，请联系管理员");
        }
    }

    /**
     * 更新运输单状态
     */
    @ApiOperation(value = "更新运输单状态", notes = "权限：'transport:order:edit'")
    @PreAuthorize("@ss.hasPermi('transport:order:edit')")
    @Log(title = "运输单状态更新", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{status}")
    public AjaxResult updateStatus(@ApiParam("运输单ID") @PathVariable("id") Long id, 
                                 @ApiParam("新状态") @PathVariable("status") Integer status) {
        try {
            return toAjax(transportOrderService.updateOrderStatus(id, status));
        } catch (ServiceException e) {
            log.error("更新运输单状态业务异常, id: {}, status: {}", id, status, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新运输单状态系统异常, id: {}, status: {}", id, status, e);
            return AjaxResult.error("更新运输单状态失败，请联系管理员");
        }
    }

    /**
     * 指派车辆和司机
     */
    @ApiOperation(value = "指派车辆和司机", notes = "权限：'transport:order:assign'")
    @PreAuthorize("@ss.hasPermi('transport:order:assign')")
    @Log(title = "运输单指派", businessType = BusinessType.UPDATE)
    @PutMapping("/assign/{id}")
    public AjaxResult assign(@ApiParam("运输单ID") @PathVariable("id") Long id,
                           @ApiParam("车辆ID") @RequestParam("vehicleId") Long vehicleId,
                           @ApiParam("司机ID") @RequestParam("driverId") Long driverId) {
        try {
            return toAjax(transportOrderService.assignVehicleAndDriver(id, vehicleId, driverId));
        } catch (ServiceException e) {
            log.error("指派车辆和司机业务异常, id: {}, vehicleId: {}, driverId: {}", id, vehicleId, driverId, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("指派车辆和司机系统异常, id: {}, vehicleId: {}, driverId: {}", id, vehicleId, driverId, e);
            return AjaxResult.error("指派车辆和司机失败，请联系管理员");
        }
    }

    /**
     * 根据车辆ID查询运输单
     */
    @ApiOperation(value = "根据车辆ID查询运输单", notes = "查询特定车辆的所有运输单")
    @GetMapping("/vehicle/{vehicleId}")
    public AjaxResult getOrdersByVehicleId(@ApiParam("车辆ID") @PathVariable("vehicleId") Long vehicleId) {
        try {
            return AjaxResult.success(transportOrderService.selectOrdersByVehicleId(vehicleId));
        } catch (ServiceException e) {
            log.error("根据车辆ID查询运输单业务异常, vehicleId: {}", vehicleId, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据车辆ID查询运输单系统异常, vehicleId: {}", vehicleId, e);
            return AjaxResult.error("根据车辆ID查询运输单失败，请联系管理员");
        }
    }

    /**
     * 根据司机ID查询运输单
     */
    @ApiOperation(value = "根据司机ID查询运输单", notes = "查询特定司机的所有运输单")
    @GetMapping("/driver/{driverId}")
    public AjaxResult getOrdersByDriverId(@ApiParam("司机ID") @PathVariable("driverId") Long driverId) {
        try {
            return AjaxResult.success(transportOrderService.selectOrdersByDriverId(driverId));
        } catch (ServiceException e) {
            log.error("根据司机ID查询运输单业务异常, driverId: {}", driverId, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据司机ID查询运输单系统异常, driverId: {}", driverId, e);
            return AjaxResult.error("根据司机ID查询运输单失败，请联系管理员");
        }
    }

    /**
     * 根据客户ID查询运输单
     */
    @ApiOperation(value = "根据客户ID查询运输单", notes = "查询特定客户的所有运输单")
    @GetMapping("/customer/{customerId}")
    public AjaxResult getOrdersByCustomerId(@ApiParam("客户ID") @PathVariable("customerId") Long customerId) {
        try {
            return AjaxResult.success(transportOrderService.selectOrdersByCustomerId(customerId));
        } catch (ServiceException e) {
            log.error("根据客户ID查询运输单业务异常, customerId: {}", customerId, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据客户ID查询运输单系统异常, customerId: {}", customerId, e);
            return AjaxResult.error("根据客户ID查询运输单失败，请联系管理员");
        }
    }

    /**
     * 统计各状态运输单数量
     */
    @ApiOperation(value = "统计各状态运输单数量", notes = "按状态分组统计运输单数量")
    @GetMapping("/statistics/status")
    public AjaxResult getOrderStatusStatistics() {
        try {
            return AjaxResult.success(transportOrderService.selectOrderStatusStatistics());
        } catch (ServiceException e) {
            log.error("统计各状态运输单数量业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("统计各状态运输单数量系统异常", e);
            return AjaxResult.error("统计各状态运输单数量失败，请联系管理员");
        }
    }

    /**
     * 查询待指派的运输单
     */
    @ApiOperation(value = "查询待指派的运输单", notes = "查询所有状态为待指派的运输单")
    @GetMapping("/pending")
    public AjaxResult getPendingOrders() {
        try {
            return AjaxResult.success(transportOrderService.selectPendingOrders());
        } catch (ServiceException e) {
            log.error("查询待指派的运输单业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询待指派的运输单系统异常", e);
            return AjaxResult.error("查询待指派的运输单失败，请联系管理员");
        }
    }

    /**
     * 查询运输中的运输单
     */
    @ApiOperation(value = "查询运输中的运输单", notes = "查询所有状态为运输中的运输单")
    @GetMapping("/transporting")
    public AjaxResult getTransportingOrders() {
        try {
            return AjaxResult.success(transportOrderService.selectTransportingOrders());
        } catch (ServiceException e) {
            log.error("查询运输中的运输单业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询运输中的运输单系统异常", e);
            return AjaxResult.error("查询运输中的运输单失败，请联系管理员");
        }
    }

    /**
     * 根据运输单号查询运输单
     */
    @ApiOperation(value = "根据运输单号查询运输单", notes = "精确匹配查询单个运输单信息")
    @GetMapping("/orderNo/{orderNo}")
    public AjaxResult getOrderByOrderNo(@ApiParam("运输单号") @PathVariable("orderNo") String orderNo) {
        try {
            return AjaxResult.success(transportOrderService.selectTransportOrderByOrderNo(orderNo));
        } catch (ServiceException e) {
            log.error("根据运输单号查询运输单业务异常, orderNo: {}", orderNo, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据运输单号查询运输单系统异常, orderNo: {}", orderNo, e);
            return AjaxResult.error("根据运输单号查询运输单失败，请联系管理员");
        }
    }

    /**
     * 检查运输单号是否存在
     */
    @ApiOperation(value = "检查运输单号是否存在", notes = "检查运输单号是否已存在，用于新建时的唯一性校验")
    @GetMapping("/checkOrderNo/{orderNo}")
    public AjaxResult checkOrderNoExists(@ApiParam("运输单号") @PathVariable("orderNo") String orderNo) {
        try {
            boolean exists = transportOrderService.checkOrderNoExists(orderNo);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查运输单号是否存在业务异常, orderNo: {}", orderNo, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查运输单号是否存在系统异常, orderNo: {}", orderNo, e);
            return AjaxResult.error("检查运输单号是否存在失败，请联系管理员");
        }
    }

    /**
     * 检查内部编号是否存在
     */
    @ApiOperation(value = "检查内部编号是否存在", notes = "检查内部编号是否已存在，用于新建时的唯一性校验")
    @GetMapping("/checkInternalCode/{internalCode}")
    public AjaxResult checkInternalCodeExists(@ApiParam("内部编号") @PathVariable("internalCode") String internalCode) {
        try {
            boolean exists = transportOrderService.checkInternalCodeExists(internalCode);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查内部编号是否存在业务异常, internalCode: {}", internalCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查内部编号是否存在系统异常, internalCode: {}", internalCode, e);
            return AjaxResult.error("检查内部编号是否存在失败，请联系管理员");
        }
    }

    /**
     * 开始装货
     */
    @ApiOperation(value = "开始装货", notes = "权限：'transport:order:edit'")
    @PreAuthorize("@ss.hasPermi('transport:order:edit')")
    @Log(title = "开始装货", businessType = BusinessType.UPDATE)
    @PutMapping("/startLoading/{id}")
    public AjaxResult startLoading(@ApiParam("运输单ID") @PathVariable("id") Long id) {
        try {
            return toAjax(transportOrderStatusService.startLoading(id));
        } catch (ServiceException e) {
            log.error("开始装货业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("开始装货系统异常, id: {}", id, e);
            return AjaxResult.error("开始装货失败，请联系管理员");
        }
    }

    /**
     * 完成装货
     */
    @ApiOperation(value = "完成装货", notes = "权限：'transport:order:edit'")
    @PreAuthorize("@ss.hasPermi('transport:order:edit')")
    @Log(title = "完成装货", businessType = BusinessType.UPDATE)
    @PutMapping("/completeLoading/{id}")
    public AjaxResult completeLoading(@ApiParam("运输单ID") @PathVariable("id") Long id) {
        try {
            return toAjax(transportOrderStatusService.completeLoading(id));
        } catch (ServiceException e) {
            log.error("完成装货业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("完成装货系统异常, id: {}", id, e);
            return AjaxResult.error("完成装货失败，请联系管理员");
        }
    }

    /**
     * 开始运输
     */
    @ApiOperation(value = "开始运输", notes = "权限：'transport:order:edit'")
    @PreAuthorize("@ss.hasPermi('transport:order:edit')")
    @Log(title = "开始运输", businessType = BusinessType.UPDATE)
    @PutMapping("/startTransporting/{id}")
    public AjaxResult startTransporting(@ApiParam("运输单ID") @PathVariable("id") Long id) {
        try {
            return toAjax(transportOrderStatusService.startTransporting(id));
        } catch (ServiceException e) {
            log.error("开始运输业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("开始运输系统异常, id: {}", id, e);
            return AjaxResult.error("开始运输失败，请联系管理员");
        }
    }

    /**
     * 完成配送
     */
    @ApiOperation(value = "完成配送", notes = "权限：'transport:order:edit'，完成配送后将自动生成对账单信息")
    @PreAuthorize("@ss.hasPermi('transport:order:edit')")
    @Log(title = "完成配送", businessType = BusinessType.UPDATE)
    @PutMapping("/completeDelivery/{id}")
    public AjaxResult completeDelivery(@ApiParam("运输单ID") @PathVariable("id") Long id) {
        try {
            int result = transportOrderStatusService.completeDelivery(id);
            if (result > 0) {
                return AjaxResult.success("配送完成，已自动生成对账单信息");
            } else {
                return AjaxResult.error("配送完成操作失败");
            }
        } catch (ServiceException e) {
            log.error("完成配送业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("完成配送系统异常, id: {}", id, e);
            return AjaxResult.error("完成配送失败，请联系管理员");
        }
    }

    /**
     * 完成对账
     */
    @ApiOperation(value = "完成对账", notes = "权限：'transport:order:edit'")
    @PreAuthorize("@ss.hasPermi('transport:order:edit')")
    @Log(title = "完成对账", businessType = BusinessType.UPDATE)
    @PutMapping("/completeBilling/{id}")
    public AjaxResult completeBilling(@ApiParam("运输单ID") @PathVariable("id") Long id) {
        try {
            return toAjax(transportOrderStatusService.completeBilling(id));
        } catch (ServiceException e) {
            log.error("完成对账业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("完成对账系统异常, id: {}", id, e);
            return AjaxResult.error("完成对账失败，请联系管理员");
        }
    }

    /**
     * 获取下一个可能的状态列表
     */
    @ApiOperation(value = "获取下一个可能的状态列表", notes = "根据当前状态，获取所有可能的下一个状态")
    @GetMapping("/nextStatuses/{currentStatus}")
    public AjaxResult getNextPossibleStatuses(@ApiParam("当前状态") @PathVariable("currentStatus") Integer currentStatus) {
        try {
            return AjaxResult.success(transportOrderStatusService.getNextPossibleStatuses(currentStatus));
        } catch (ServiceException e) {
            log.error("获取下一个可能的状态列表业务异常, currentStatus: {}", currentStatus, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取下一个可能的状态列表系统异常, currentStatus: {}", currentStatus, e);
            return AjaxResult.error("获取下一个可能的状态列表失败，请联系管理员");
        }
    }
}
