package com.ruoyi.web.controller.transport;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.transport.TransportVehicle;
import com.ruoyi.system.service.transport.ITransportVehicleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 车辆信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "车辆信息管理")
@RestController
@RequestMapping("/vehicle")
public class TransportVehicleController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TransportVehicleController.class);

    @Autowired
    private ITransportVehicleService transportVehicleService;

    /**
     * 查询车辆信息列表
     */
    @ApiOperation(value = "查询车辆信息列表", notes = "权限：'transport:vehicle:list'")
    @PreAuthorize("@ss.hasPermi('transport:vehicle:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportVehicle transportVehicle) {
        try {
            startPage();
            List<TransportVehicle> list = transportVehicleService.selectTransportVehicleList(transportVehicle);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询车辆信息列表异常, request: {}", transportVehicle, e);
            return new TableDataInfo();
        }
    }

    /**
     * 导出车辆信息列表
     */
    @ApiOperation(value = "导出车辆信息列表", notes = "权限：'transport:vehicle:export'")
    @PreAuthorize("@ss.hasPermi('transport:vehicle:export')")
    @Log(title = "车辆信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, TransportVehicle transportVehicle) {
        try {
            List<TransportVehicle> list = transportVehicleService.selectTransportVehicleList(transportVehicle);
            ExcelUtil<TransportVehicle> util = new ExcelUtil<TransportVehicle>(TransportVehicle.class);
            util.exportExcel(response, list, "车辆信息数据");
        } catch (Exception e) {
            log.error("导出车辆信息列表异常, request: {}", transportVehicle, e);
        }
    }

    /**
     * 获取车辆信息详细信息
     */
    @ApiOperation(value = "获取车辆信息详细信息", notes = "权限：'transport:vehicle:query'")
    @PreAuthorize("@ss.hasPermi('transport:vehicle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("车辆ID") @PathVariable("id") Long id) {
        try {
            return AjaxResult.success(transportVehicleService.selectTransportVehicleById(id));
        } catch (ServiceException e) {
            log.error("获取车辆信息详细信息业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取车辆信息详细信息系统异常, id: {}", id, e);
            return AjaxResult.error("获取车辆信息详细信息失败，请联系管理员");
        }
    }

    /**
     * 新增车辆信息
     */
    @ApiOperation(value = "新增车辆信息", notes = "权限：'transport:vehicle:add'")
    @PreAuthorize("@ss.hasPermi('transport:vehicle:add')")
    @Log(title = "车辆信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportVehicle transportVehicle) {
        try {
            return toAjax(transportVehicleService.insertTransportVehicle(transportVehicle));
        } catch (ServiceException e) {
            log.error("新增车辆信息业务异常, request: {}", transportVehicle, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增车辆信息系统异常, request: {}", transportVehicle, e);
            return AjaxResult.error("新增车辆信息失败，请联系管理员");
        }
    }

    /**
     * 修改车辆信息
     */
    @ApiOperation(value = "修改车辆信息", notes = "权限：'transport:vehicle:edit'")
    @PreAuthorize("@ss.hasPermi('transport:vehicle:edit')")
    @Log(title = "车辆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportVehicle transportVehicle) {
        try {
            return toAjax(transportVehicleService.updateTransportVehicle(transportVehicle));
        } catch (ServiceException e) {
            log.error("修改车辆信息业务异常, request: {}", transportVehicle, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改车辆信息系统异常, request: {}", transportVehicle, e);
            return AjaxResult.error("修改车辆信息失败，请联系管理员");
        }
    }

    /**
     * 删除车辆信息
     */
    @ApiOperation(value = "删除车辆信息", notes = "权限：'transport:vehicle:remove'")
    @PreAuthorize("@ss.hasPermi('transport:vehicle:remove')")
    @Log(title = "车辆信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("车辆ID数组") @PathVariable Long[] ids) {
        try {
            return toAjax(transportVehicleService.deleteTransportVehicleByIds(ids));
        } catch (ServiceException e) {
            log.error("删除车辆信息业务异常, ids: {}", ids, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除车辆信息系统异常, ids: {}", ids, e);
            return AjaxResult.error("删除车辆信息失败，请联系管理员");
        }
    }

    /**
     * 根据车牌号查询车辆
     */
    @ApiOperation(value = "根据车牌号查询车辆", notes = "精确匹配查询单个车辆信息")
    @GetMapping("/licensePlate/{licensePlate}")
    public AjaxResult getVehicleByLicensePlate(@ApiParam("车牌号") @PathVariable("licensePlate") String licensePlate) {
        try {
            return AjaxResult.success(transportVehicleService.selectVehicleByLicensePlate(licensePlate));
        } catch (ServiceException e) {
            log.error("根据车牌号查询车辆业务异常, licensePlate: {}", licensePlate, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据车牌号查询车辆系统异常, licensePlate: {}", licensePlate, e);
            return AjaxResult.error("根据车牌号查询车辆失败，请联系管理员");
        }
    }

    /**
     * 检查车牌号是否存在
     */
    @ApiOperation(value = "检查车牌号是否存在", notes = "检查车牌号是否已存在，用于新建或修改时的唯一性校验")
    @GetMapping("/checkLicensePlate/{licensePlate}")
    public AjaxResult checkLicensePlateExists(@ApiParam("车牌号") @PathVariable("licensePlate") String licensePlate,
                                            @ApiParam("车辆ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            boolean exists = transportVehicleService.checkLicensePlateExists(licensePlate, id);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查车牌号是否存在业务异常, licensePlate: {}, id: {}", licensePlate, id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查车牌号是否存在系统异常, licensePlate: {}, id: {}", licensePlate, id, e);
            return AjaxResult.error("检查车牌号是否存在失败，请联系管理员");
        }
    }

    /**
     * 查询可用车辆列表
     */
    @ApiOperation(value = "查询可用车辆列表", notes = "查询所有状态为可用的车辆信息")
    @GetMapping("/available")
    public AjaxResult getAvailableVehicles() {
        try {
            return AjaxResult.success(transportVehicleService.selectAvailableVehicles());
        } catch (ServiceException e) {
            log.error("查询可用车辆列表业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询可用车辆列表系统异常", e);
            return AjaxResult.error("查询可用车辆列表失败，请联系管理员");
        }
    }

    /**
     * 更新车辆状态
     */
    @ApiOperation(value = "更新车辆状态", notes = "权限：'transport:vehicle:edit'")
    @PreAuthorize("@ss.hasPermi('transport:vehicle:edit')")
    @Log(title = "车辆状态更新", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{status}")
    public AjaxResult updateStatus(@ApiParam("车辆ID") @PathVariable("id") Long id, 
                                 @ApiParam("新状态") @PathVariable("status") Integer status) {
        try {
            return toAjax(transportVehicleService.updateVehicleStatus(id, status));
        } catch (ServiceException e) {
            log.error("更新车辆状态业务异常, id: {}, status: {}", id, status, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新车辆状态系统异常, id: {}, status: {}", id, status, e);
            return AjaxResult.error("更新车辆状态失败，请联系管理员");
        }
    }

    /**
     * 根据区域代码查询车辆
     */
    @ApiOperation(value = "根据区域代码查询车辆", notes = "查询特定区域代码下的所有车辆")
    @GetMapping("/region/{regionCode}")
    public AjaxResult getVehiclesByRegion(@ApiParam("区域代码") @PathVariable("regionCode") String regionCode) {
        try {
            return AjaxResult.success(transportVehicleService.selectVehiclesByRegion(regionCode));
        } catch (ServiceException e) {
            log.error("根据区域代码查询车辆业务异常, regionCode: {}", regionCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据区域代码查询车辆系统异常, regionCode: {}", regionCode, e);
            return AjaxResult.error("根据区域代码查询车辆失败，请联系管理员");
        }
    }

    /**
     * 统计各状态车辆数量
     */
    @ApiOperation(value = "统计各状态车辆数量", notes = "按状态分组统计车辆数量")
    @GetMapping("/statistics/status")
    public AjaxResult getVehicleStatusStatistics() {
        try {
            return AjaxResult.success(transportVehicleService.selectVehicleStatusStatistics());
        } catch (ServiceException e) {
            log.error("统计各状态车辆数量业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("统计各状态车辆数量系统异常", e);
            return AjaxResult.error("统计各状态车辆数量失败，请联系管理员");
        }
    }

    /**
     * 查询即将到期的车辆(年检、保险)
     */
    @ApiOperation(value = "查询即将到期的车辆", notes = "查询年检或保险即将到期的车辆")
    @GetMapping("/expiring")
    public AjaxResult getExpiringVehicles(@ApiParam("提前天数") @RequestParam(value = "days", defaultValue = "30") Integer days) {
        try {
            return AjaxResult.success(transportVehicleService.selectExpiringVehicles(days));
        } catch (ServiceException e) {
            log.error("查询即将到期的车辆业务异常, days: {}", days, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询即将到期的车辆系统异常, days: {}", days, e);
            return AjaxResult.error("查询即将到期的车辆失败，请联系管理员");
        }
    }
}
