package com.ruoyi.web.controller.transport;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.transport.TransportConsignee;
import com.ruoyi.system.service.transport.ITransportConsigneeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收货方信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "收货方信息管理")
@RestController
@RequestMapping("/consignee")
public class TransportConsigneeController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TransportConsigneeController.class);

    @Autowired
    private ITransportConsigneeService transportConsigneeService;

    /**
     * 查询收货方信息列表
     */
    @ApiOperation(value = "查询收货方信息列表", notes = "权限：'transport:consignee:list'")
    @PreAuthorize("@ss.hasPermi('transport:consignee:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportConsignee transportConsignee) {
        try {
            startPage();
            List<TransportConsignee> list = transportConsigneeService.selectTransportConsigneeList(transportConsignee);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询收货方信息列表异常, request: {}", transportConsignee, e);
            return new TableDataInfo();
        }
    }

    /**
     * 导出收货方信息列表
     */
    @ApiOperation(value = "导出收货方信息列表", notes = "权限：'transport:consignee:export'")
    @PreAuthorize("@ss.hasPermi('transport:consignee:export')")
    @Log(title = "收货方信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, TransportConsignee transportConsignee) {
        try {
            List<TransportConsignee> list = transportConsigneeService.selectTransportConsigneeList(transportConsignee);
            ExcelUtil<TransportConsignee> util = new ExcelUtil<TransportConsignee>(TransportConsignee.class);
            util.exportExcel(response, list, "收货方信息数据");
        } catch (Exception e) {
            log.error("导出收货方信息列表异常, request: {}", transportConsignee, e);
        }
    }

    /**
     * 获取收货方信息详细信息
     */
    @ApiOperation(value = "获取收货方信息详细信息", notes = "权限：'transport:consignee:query'")
    @PreAuthorize("@ss.hasPermi('transport:consignee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("收货方ID") @PathVariable("id") Long id) {
        try {
            return AjaxResult.success(transportConsigneeService.selectTransportConsigneeById(id));
        } catch (ServiceException e) {
            log.error("获取收货方信息详细信息业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取收货方信息详细信息系统异常, id: {}", id, e);
            return AjaxResult.error("获取收货方信息详细信息失败，请联系管理员");
        }
    }

    /**
     * 新增收货方信息
     */
    @ApiOperation(value = "新增收货方信息", notes = "权限：'transport:consignee:add'")
    @PreAuthorize("@ss.hasPermi('transport:consignee:add')")
    @Log(title = "收货方信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportConsignee transportConsignee) {
        try {
            return toAjax(transportConsigneeService.insertTransportConsignee(transportConsignee));
        } catch (ServiceException e) {
            log.error("新增收货方信息业务异常, request: {}", transportConsignee, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增收货方信息系统异常, request: {}", transportConsignee, e);
            return AjaxResult.error("新增收货方信息失败，请联系管理员");
        }
    }

    /**
     * 修改收货方信息
     */
    @ApiOperation(value = "修改收货方信息", notes = "权限：'transport:consignee:edit'")
    @PreAuthorize("@ss.hasPermi('transport:consignee:edit')")
    @Log(title = "收货方信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportConsignee transportConsignee) {
        try {
            return toAjax(transportConsigneeService.updateTransportConsignee(transportConsignee));
        } catch (ServiceException e) {
            log.error("修改收货方信息业务异常, request: {}", transportConsignee, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改收货方信息系统异常, request: {}", transportConsignee, e);
            return AjaxResult.error("修改收货方信息失败，请联系管理员");
        }
    }

    /**
     * 删除收货方信息
     */
    @ApiOperation(value = "删除收货方信息", notes = "权限：'transport:consignee:remove'")
    @PreAuthorize("@ss.hasPermi('transport:consignee:remove')")
    @Log(title = "收货方信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("收货方ID数组") @PathVariable Long[] ids) {
        try {
            return toAjax(transportConsigneeService.deleteTransportConsigneeByIds(ids));
        } catch (ServiceException e) {
            log.error("删除收货方信息业务异常, ids: {}", ids, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除收货方信息系统异常, ids: {}", ids, e);
            return AjaxResult.error("删除收货方信息失败，请联系管理员");
        }
    }

    /**
     * 根据客户ID查询收货方列表
     */
    @ApiOperation(value = "根据客户ID查询收货方列表", notes = "根据客户ID查询其所有关联的收货方信息")
    @GetMapping("/customer/{customerId}")
    public AjaxResult getConsigneesByCustomerId(@ApiParam("客户ID") @PathVariable("customerId") Long customerId) {
        try {
            return AjaxResult.success(transportConsigneeService.selectConsigneesByCustomerId(customerId));
        } catch (ServiceException e) {
            log.error("根据客户ID查询收货方列表业务异常, customerId: {}", customerId, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据客户ID查询收货方列表系统异常, customerId: {}", customerId, e);
            return AjaxResult.error("根据客户ID查询收货方列表失败，请联系管理员");
        }
    }

    /**
     * 根据收货方编码查询收货方
     */
    @ApiOperation(value = "根据收货方编码查询收货方", notes = "精确匹配查询单个收货方信息")
    @GetMapping("/code/{consigneeCode}")
    public AjaxResult getConsigneeByCode(@ApiParam("收货方编码") @PathVariable("consigneeCode") String consigneeCode) {
        try {
            return AjaxResult.success(transportConsigneeService.selectConsigneeByCode(consigneeCode));
        } catch (ServiceException e) {
            log.error("根据收货方编码查询收货方业务异常, consigneeCode: {}", consigneeCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据收货方编码查询收货方系统异常, consigneeCode: {}", consigneeCode, e);
            return AjaxResult.error("根据收货方编码查询收货方失败，请联系管理员");
        }
    }

    /**
     * 检查收货方编码是否存在
     */
    @ApiOperation(value = "检查收货方编码是否存在", notes = "检查收货方编码是否已存在，用于新建或修改时的唯一性校验")
    @GetMapping("/checkCode/{consigneeCode}")
    public AjaxResult checkConsigneeCodeExists(@ApiParam("收货方编码") @PathVariable("consigneeCode") String consigneeCode,
                                             @ApiParam("收货方ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            boolean exists = transportConsigneeService.checkConsigneeCodeExists(consigneeCode, id);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查收货方编码是否存在业务异常, consigneeCode: {}, id: {}", consigneeCode, id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查收货方编码是否存在系统异常, consigneeCode: {}, id: {}", consigneeCode, id, e);
            return AjaxResult.error("检查收货方编码是否存在失败，请联系管理员");
        }
    }

    /**
     * 查询启用的收货方列表
     */
    @ApiOperation(value = "查询启用的收货方列表", notes = "查询所有状态为启用的收货方信息")
    @GetMapping("/active")
    public AjaxResult getActiveConsignees() {
        try {
            return AjaxResult.success(transportConsigneeService.selectActiveConsignees());
        } catch (ServiceException e) {
            log.error("查询启用的收货方列表业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询启用的收货方列表系统异常", e);
            return AjaxResult.error("查询启用的收货方列表失败，请联系管理员");
        }
    }

    /**
     * 更新收货方状态
     */
    @ApiOperation(value = "更新收货方状态", notes = "权限：'transport:consignee:edit'")
    @PreAuthorize("@ss.hasPermi('transport:consignee:edit')")
    @Log(title = "收货方状态更新", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{isActive}")
    public AjaxResult updateStatus(@ApiParam("收货方ID") @PathVariable("id") Long id, 
                                 @ApiParam("是否启用") @PathVariable("isActive") Integer isActive) {
        try {
            return toAjax(transportConsigneeService.updateConsigneeStatus(id, isActive));
        } catch (ServiceException e) {
            log.error("更新收货方状态业务异常, id: {}, isActive: {}", id, isActive, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新收货方状态系统异常, id: {}, isActive: {}", id, isActive, e);
            return AjaxResult.error("更新收货方状态失败，请联系管理员");
        }
    }

    /**
     * 根据收货方类型查询收货方
     */
    @ApiOperation(value = "根据收货方类型查询收货方", notes = "根据收货方类型查询收货方信息")
    @GetMapping("/type/{consigneeType}")
    public AjaxResult getConsigneesByType(@ApiParam("收货方类型") @PathVariable("consigneeType") Integer consigneeType) {
        try {
            return AjaxResult.success(transportConsigneeService.selectConsigneesByType(consigneeType));
        } catch (ServiceException e) {
            log.error("根据收货方类型查询收货方业务异常, consigneeType: {}", consigneeType, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据收货方类型查询收货方系统异常, consigneeType: {}", consigneeType, e);
            return AjaxResult.error("根据收货方类型查询收货方失败，请联系管理员");
        }
    }

    /**
     * 根据地区查询收货方
     */
    @ApiOperation(value = "根据地区查询收货方", notes = "根据国家、省份、城市等地区信息查询收货方")
    @GetMapping("/region")
    public AjaxResult getConsigneesByRegion(@ApiParam("国家编码") @RequestParam(value = "countryCode", required = false) String countryCode,
                                          @ApiParam("省份编码") @RequestParam(value = "provinceCode", required = false) String provinceCode,
                                          @ApiParam("城市编码") @RequestParam(value = "cityCode", required = false) String cityCode) {
        try {
            return AjaxResult.success(transportConsigneeService.selectConsigneesByRegion(countryCode, provinceCode, cityCode));
        } catch (ServiceException e) {
            log.error("根据地区查询收货方业务异常, countryCode: {}, provinceCode: {}, cityCode: {}", countryCode, provinceCode, cityCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据地区查询收货方系统异常, countryCode: {}, provinceCode: {}, cityCode: {}", countryCode, provinceCode, cityCode, e);
            return AjaxResult.error("根据地区查询收货方失败，请联系管理员");
        }
    }
}
