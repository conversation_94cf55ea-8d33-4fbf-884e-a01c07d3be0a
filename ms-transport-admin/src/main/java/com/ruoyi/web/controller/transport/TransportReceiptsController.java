package com.ruoyi.web.controller.transport;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.dto.QueryTransportCustomerReceiptsDTO;
import com.ruoyi.system.domain.dto.TransportReceiptsDetails;
import com.ruoyi.system.domain.transport.TransportReceipts;
import com.ruoyi.system.domain.vo.TransportCustomerReceiptsSummaryVO;
import com.ruoyi.system.service.transport.ITransportExcelProcessService;
import com.ruoyi.system.service.transport.ITransportReceiptsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * 运输对账Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(value = "运输对账管理")
@RestController
@RequestMapping("/transport/receipts")
public class TransportReceiptsController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(TransportReceiptsController.class);
    
    @Autowired
    private ITransportReceiptsService transportReceiptsService;
    
    @Autowired
    private ITransportExcelProcessService excelProcessService;

    /**
     * 查询运输对账列表
     */
    @ApiOperation(value = "查询运输对账列表", notes = "权限：'transport:receipts:list'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportReceipts condition) {
        startPage();
        List<TransportReceipts> list;
        try {
            list = transportReceiptsService.findAllByCondition(condition);
        } catch (Exception e) {
            log.error("查询运输对账列表异常，condition：{}，e:", JSON.toJSONString(condition), e);
            return getDataTable(Collections.emptyList());
        }
        return getDataTable(list);
    }

    /**
     * 查询客户期初余额
     */
    @ApiOperation(value = "查询客户期初余额", notes = "权限：'transport:receipts:initial.amount'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:initial.amount')")
    @GetMapping("/getInitialAmount")
    public AjaxResult getInitialAmount(TransportReceipts condition) {
        try {
            return success(transportReceiptsService.getInitialAmount(condition));
        } catch (ServiceException e) {
            log.error("查询客户期初余额异常，condition：{}，e:", condition, e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("查询客户期初余额异常，condition：{}，e:", condition, e);
            return error("获取数据失败");
        }
    }

    /**
     * 获取运输对账详细信息
     */
    @ApiOperation(value = "获取运输对账详细信息", notes = "权限：'transport:receipts:query'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:query')")
    @GetMapping("/{id}")
    public AjaxResult getTransportReceiptsById(@PathVariable Long id) {
        if (id == null || id <= 0) {
            return error("输入数据无效或ID不合法");
        }
        try {
            return success(transportReceiptsService.findById(id));
        } catch (ServiceException e) {
            log.error("查询运输对账详情异常，id：{}，e:", id, e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("查询运输对账详情异常，id：{}，e:", id, e);
            return error("获取数据失败");
        }
    }

    /**
     * 新增运输对账
     */
    @ApiOperation(value = "新增运输对账", notes = "权限：'transport:receipts:add'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:add')")
    @PostMapping
    public AjaxResult createTransportReceipts(@Validated @RequestBody TransportReceipts transportReceipts) {
        transportReceipts.setCreateBy(getUsername());
        try {
            return toAjax(transportReceiptsService.insert(transportReceipts));
        } catch (ServiceException e) {
            log.error("新增运输对账异常，transportReceipts：{}，e:", JSON.toJSONString(transportReceipts), e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("新增运输对账异常，transportReceipts：{}，e:", JSON.toJSONString(transportReceipts), e);
            return error("创建失败");
        }
    }

    /**
     * 修改运输对账
     */
    @ApiOperation(value = "修改运输对账", notes = "权限：'transport:receipts:edit'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:edit')")
    @PutMapping
    public AjaxResult updateTransportReceipts(@Validated @RequestBody TransportReceipts transportReceipts) {
        transportReceipts.setUpdateBy(getUsername());
        try {
            return toAjax(transportReceiptsService.updateTransportReceipts(transportReceipts));
        } catch (ServiceException e) {
            log.error("修改运输对账异常，transportReceipts：{}，e:", JSON.toJSONString(transportReceipts), e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("修改运输对账异常，transportReceipts：{}，e:", JSON.toJSONString(transportReceipts), e);
            return error("修改失败");
        }
    }

    /**
     * 删除运输对账
     */
    @ApiOperation(value = "删除运输对账", notes = "权限：'transport:receipts:delete'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:delete')")
    @DeleteMapping("/{id}")
    public AjaxResult deleteTransportReceipts(@PathVariable Long id) {
        if (id == null || id <= 0) {
            return error("ID不合法");
        }
        try {
            return toAjax(transportReceiptsService.delete(id));
        } catch (ServiceException e) {
            log.error("删除运输对账异常，id：{}，e:", id, e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("删除运输对账异常，id：{}，e:", id, e);
            return error("删除失败");
        }
    }

    /**
     * 批量删除运输对账
     */
    @ApiOperation(value = "批量删除运输对账", notes = "权限：'transport:receipts:delete'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:delete')")
    @DeleteMapping("/{ids}")
    public AjaxResult deleteTransportReceiptsByIds(@PathVariable Long[] ids) {
        if (ids == null || ids.length == 0) {
            return error("ID不能为空");
        }
        try {
            return toAjax(transportReceiptsService.deleteTransportReceiptsByIds(ids));
        } catch (ServiceException e) {
            log.error("批量删除运输对账异常，ids：{}，e:", JSON.toJSONString(ids), e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("批量删除运输对账异常，ids：{}，e:", JSON.toJSONString(ids), e);
            return error("删除失败");
        }
    }

    /**
     * 导出运输对账
     */
    @ApiOperation(value = "导出运输对账", notes = "权限：'transport:receipts:export'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:export')")
    @GetMapping("/export")
    public void exportToExcel(HttpServletResponse response, TransportReceipts receipts) {
        if (null == receipts) {
            throw new ServiceException("导出失败");
        }
        try {
            excelProcessService.exportTransportReceipts(
                transportReceiptsService.listReceiptsReceiptsData(receipts), 
                receipts.getCustomerCode(), 
                response
            );
        } catch (ServiceException e) {
            log.error("导出客户对账单异常，e:", e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("导出客户对账单异常，e:", e);
            throw new ServiceException("导出失败");
        }
    }

    /**
     * 导入运输对账
     */
    @ApiOperation(value = "导入运输对账", notes = "权限：'transport:receipts:import'")
    @PreAuthorize("@ss.hasPermi('transport:receipts:import')")
    @PostMapping("/importToExcel")
    public AjaxResult importToExcel(@RequestParam("file") MultipartFile file) {
        long startTime = System.currentTimeMillis();
        try {
            List<TransportReceiptsDetails> details = excelProcessService.importExcelForTransportReceipts(file);
            if (CollectionUtils.isEmpty(details)) {
                log.error("导入表信息为空！");
                return error("导入表信息为空");
            }
            transportReceiptsService.batchSave(details, getUsername());
            log.info("导入运输对账耗时：{}ms", System.currentTimeMillis() - startTime);
            return success("导入成功");
        } catch (ServiceException e) {
            log.error("导入运输对账异常，e:", e);
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("导入运输对账异常，e:", e);
            return error("导入异常");
        }
    }

    @ApiOperation(value = "查询委托客户账期", notes = "权限：'transport:customer:payable:summary'")
    @PreAuthorize("@ss.hasPermi('transport:customer:payable:summary')")
    @GetMapping("/getTransportCustomerPayableSummary")
    public TableDataInfo getTransportCustomerPayableSummary(QueryTransportCustomerReceiptsDTO condition) {
        Pair<Integer, List<TransportCustomerReceiptsSummaryVO>> pair;
        try {
            startPage();
            pair = transportReceiptsService.getTransportCustomerPayableSummary(condition);
        } catch (Exception e) {
            log.error("查询委托客户账期列表异常，condition：{}，e:", JSON.toJSONString(condition), e);
            return getDataTable(Collections.emptyList());
        }
        if (CollectionUtils.isEmpty(pair.getRight()) || pair.getLeft() == null) {
            return getDataTable(Collections.emptyList());
        }
        return getDataTable(pair.getRight(), Long.valueOf(pair.getLeft()));
    }

    @ApiOperation(value = "导出委托客户账期汇总数据", notes = "权限：'transport:customer:payable:summary:export'")
    @PreAuthorize("@ss.hasPermi('transport:customer:payable:summary:export')")
    @GetMapping("/exportTransportCustomerPayableSummary")
    public void exportTransportCustomerPayableSummaryToExcel(HttpServletResponse response, QueryTransportCustomerReceiptsDTO condition) {
        try {
            // 获取账期数据
            List<TransportCustomerReceiptsSummaryVO> dataList = transportReceiptsService.getAllCustomerPayableSummary(condition);
            if (CollectionUtils.isEmpty(dataList)) {
                throw new ServiceException("没有可导出的数据");
            }

            // 调用 Excel 处理服务导出
            excelProcessService.exportCustomerPayableSummary(dataList, response);
        } catch (ServiceException e) {
            log.error("导出委托客户账期失败，condition:{}, e:", JSON.toJSONString(condition), e);
            throw e;
        } catch (Exception e) {
            log.error("导出委托客户账期异常，condition:{}, e:", JSON.toJSONString(condition), e);
            throw new ServiceException("导出失败");
        }
    }
}
