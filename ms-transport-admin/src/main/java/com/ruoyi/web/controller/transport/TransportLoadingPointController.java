package com.ruoyi.web.controller.transport;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.transport.TransportLoadingPoint;
import com.ruoyi.system.service.transport.ITransportLoadingPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 装货点信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "装货点信息管理")
@RestController
@RequestMapping("/loadingPoint")
public class TransportLoadingPointController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TransportLoadingPointController.class);

    @Autowired
    private ITransportLoadingPointService transportLoadingPointService;

    /**
     * 查询装货点信息列表
     */
    @ApiOperation(value = "查询装货点信息列表", notes = "权限：'transport:loadingPoint:list'")
    @PreAuthorize("@ss.hasPermi('transport:loadingPoint:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportLoadingPoint transportLoadingPoint) {
        try {
            startPage();
            List<TransportLoadingPoint> list = transportLoadingPointService.selectTransportLoadingPointList(transportLoadingPoint);
            return getDataTable(list);
        } catch (Exception e) {
            log.error("查询装货点信息列表异常, request: {}", transportLoadingPoint, e);
            return new TableDataInfo();
        }
    }

    /**
     * 查询装货点信息列表，用于下拉框
     */
    @ApiOperation(value = "查询装货点信息列表，用于下拉框")
    @GetMapping("/options")
    public AjaxResult selectOptions(TransportLoadingPoint transportLoadingPoint) {
        try {
            List<TransportLoadingPoint> list = transportLoadingPointService.selectLoadingPointOptions(transportLoadingPoint);
            return AjaxResult.success(list);
        } catch (ServiceException e) {
            log.error("查询装货点信息列表业务异常, request: {}", transportLoadingPoint, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询装货点信息列表系统异常, request: {}", transportLoadingPoint, e);
            return AjaxResult.error("查询装货点信息列表失败，请联系管理员");
        }
    }

    /**
     * 导出装货点信息列表
     */
    @ApiOperation(value = "导出装货点信息列表", notes = "权限：'transport:loadingPoint:export'")
    @PreAuthorize("@ss.hasPermi('transport:loadingPoint:export')")
    @Log(title = "装货点信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(HttpServletResponse response, TransportLoadingPoint transportLoadingPoint) {
        try {
            List<TransportLoadingPoint> list = transportLoadingPointService.selectTransportLoadingPointList(transportLoadingPoint);
            ExcelUtil<TransportLoadingPoint> util = new ExcelUtil<TransportLoadingPoint>(TransportLoadingPoint.class);
            util.exportExcel(response, list, "装货点信息数据");
        } catch (Exception e) {
            log.error("导出装货点信息列表异常, request: {}", transportLoadingPoint, e);
        }
    }

    /**
     * 获取装货点信息详细信息
     */
    @ApiOperation(value = "获取装货点信息详细信息", notes = "权限：'transport:loadingPoint:query'")
    @PreAuthorize("@ss.hasPermi('transport:loadingPoint:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam("装货点ID") @PathVariable("id") Long id) {
        try {
            return AjaxResult.success(transportLoadingPointService.selectTransportLoadingPointById(id));
        } catch (ServiceException e) {
            log.error("获取装货点信息详细信息业务异常, id: {}", id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取装货点信息详细信息系统异常, id: {}", id, e);
            return AjaxResult.error("获取装货点信息详细信息失败，请联系管理员");
        }
    }

    /**
     * 新增装货点信息
     */
    @ApiOperation(value = "新增装货点信息", notes = "权限：'transport:loadingPoint:add'")
    @PreAuthorize("@ss.hasPermi('transport:loadingPoint:add')")
    @Log(title = "装货点信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportLoadingPoint transportLoadingPoint) {
        try {
            return toAjax(transportLoadingPointService.insertTransportLoadingPoint(transportLoadingPoint));
        } catch (ServiceException e) {
            log.error("新增装货点信息业务异常, request: {}", transportLoadingPoint, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增装货点信息系统异常, request: {}", transportLoadingPoint, e);
            return AjaxResult.error("新增装货点信息失败，请联系管理员");
        }
    }

    /**
     * 修改装货点信息
     */
    @ApiOperation(value = "修改装货点信息", notes = "权限：'transport:loadingPoint:edit'")
    @PreAuthorize("@ss.hasPermi('transport:loadingPoint:edit')")
    @Log(title = "装货点信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportLoadingPoint transportLoadingPoint) {
        try {
            return toAjax(transportLoadingPointService.updateTransportLoadingPoint(transportLoadingPoint));
        } catch (ServiceException e) {
            log.error("修改装货点信息业务异常, request: {}", transportLoadingPoint, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改装货点信息系统异常, request: {}", transportLoadingPoint, e);
            return AjaxResult.error("修改装货点信息失败，请联系管理员");
        }
    }

    /**
     * 删除装货点信息
     */
    @ApiOperation(value = "删除装货点信息", notes = "权限：'transport:loadingPoint:remove'")
    @PreAuthorize("@ss.hasPermi('transport:loadingPoint:remove')")
    @Log(title = "装货点信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("装货点ID数组") @PathVariable Long[] ids) {
        try {
            return toAjax(transportLoadingPointService.deleteTransportLoadingPointByIds(ids));
        } catch (ServiceException e) {
            log.error("删除装货点信息业务异常, ids: {}", ids, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除装货点信息系统异常, ids: {}", ids, e);
            return AjaxResult.error("删除装货点信息失败，请联系管理员");
        }
    }

    /**
     * 根据装货点编码查询装货点
     */
    @ApiOperation("根据装货点编码查询装货点")
    @GetMapping("/code/{pointCode}")
    public AjaxResult getLoadingPointByCode(@ApiParam("装货点编码") @PathVariable("pointCode") String pointCode) {
        try {
            return AjaxResult.success(transportLoadingPointService.selectLoadingPointByCode(pointCode));
        } catch (ServiceException e) {
            log.error("根据装货点编码查询装货点业务异常, pointCode: {}", pointCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据装货点编码查询装货点系统异常, pointCode: {}", pointCode, e);
            return AjaxResult.error("根据装货点编码查询装货点失败，请联系管理员");
        }
    }

    /**
     * 检查装货点编码是否存在
     */
    @ApiOperation("检查装货点编码是否存在")
    @GetMapping("/checkCode/{pointCode}")
    public AjaxResult checkPointCodeExists(@ApiParam("装货点编码") @PathVariable("pointCode") String pointCode,
                                         @ApiParam("装货点ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            boolean exists = transportLoadingPointService.checkPointCodeExists(pointCode, id);
            return AjaxResult.success("exists", exists);
        } catch (ServiceException e) {
            log.error("检查装货点编码是否存在业务异常, pointCode: {}, id: {}", pointCode, id, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("检查装货点编码是否存在系统异常, pointCode: {}, id: {}", pointCode, id, e);
            return AjaxResult.error("检查装货点编码是否存在失败，请联系管理员");
        }
    }

    /**
     * 查询启用的装货点列表
     */
    @ApiOperation("查询启用的装货点列表")
    @GetMapping("/active")
    public AjaxResult getActiveLoadingPoints() {
        try {
            return AjaxResult.success(transportLoadingPointService.selectActiveLoadingPoints());
        } catch (ServiceException e) {
            log.error("查询启用的装货点列表业务异常", e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询启用的装货点列表系统异常", e);
            return AjaxResult.error("查询启用的装货点列表失败，请联系管理员");
        }
    }

    /**
     * 更新装货点状态
     */
    @ApiOperation(value = "更新装货点状态", notes = "权限：'transport:loadingPoint:edit'")
    @PreAuthorize("@ss.hasPermi('transport:loadingPoint:edit')")
    @Log(title = "装货点状态更新", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{isActive}")
    public AjaxResult updateStatus(@ApiParam("装货点ID") @PathVariable("id") Long id, 
                                 @ApiParam("是否启用") @PathVariable("isActive") Integer isActive) {
        try {
            return toAjax(transportLoadingPointService.updateLoadingPointStatus(id, isActive));
        } catch (ServiceException e) {
            log.error("更新装货点状态业务异常, id: {}, isActive: {}", id, isActive, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新装货点状态系统异常, id: {}, isActive: {}", id, isActive, e);
            return AjaxResult.error("更新装货点状态失败，请联系管理员");
        }
    }

    /**
     * 根据装货点类型查询装货点
     */
    @ApiOperation("根据装货点类型查询装货点")
    @GetMapping("/type/{pointType}")
    public AjaxResult getLoadingPointsByType(@ApiParam("装货点类型") @PathVariable("pointType") Integer pointType) {
        try {
            return AjaxResult.success(transportLoadingPointService.selectLoadingPointsByType(pointType));
        } catch (ServiceException e) {
            log.error("根据装货点类型查询装货点业务异常, pointType: {}", pointType, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据装货点类型查询装货点系统异常, pointType: {}", pointType, e);
            return AjaxResult.error("根据装货点类型查询装货点失败，请联系管理员");
        }
    }

    /**
     * 根据地区查询装货点
     */
    @ApiOperation("根据地区查询装货点")
    @GetMapping("/region")
    public AjaxResult getLoadingPointsByRegion(@ApiParam("国家编码") @RequestParam(value = "countryCode", required = false) String countryCode,
                                             @ApiParam("省份编码") @RequestParam(value = "provinceCode", required = false) String provinceCode,
                                             @ApiParam("城市编码") @RequestParam(value = "cityCode", required = false) String cityCode) {
        try {
            return AjaxResult.success(transportLoadingPointService.selectLoadingPointsByRegion(countryCode, provinceCode, cityCode));
        } catch (ServiceException e) {
            log.error("根据地区查询装货点业务异常, countryCode: {}, provinceCode: {}, cityCode: {}", countryCode, provinceCode, cityCode, e);
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据地区查询装货点系统异常, countryCode: {}, provinceCode: {}, cityCode: {}", countryCode, provinceCode, cityCode, e);
            return AjaxResult.error("根据地区查询装货点失败，请联系管理员");
        }
    }
}
