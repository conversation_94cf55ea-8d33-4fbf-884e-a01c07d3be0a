package com.ruoyi.web.controller.common;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(tags = "系统健康检查")
@RestController
@RequestMapping("/common/health")
public class HealthController extends BaseController {

    /**
     * 健康检查
     */
    @ApiOperation("健康检查")
    @GetMapping("/check")
    public AjaxResult healthCheck() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("application", "ms-transport");
        data.put("version", "1.0.0");
        data.put("message", "应用运行正常");
        
        return AjaxResult.success("健康检查通过", data);
    }

    /**
     * Swagger测试接口
     */
    @ApiOperation("Swagger测试接口")
    @GetMapping("/swagger-test")
    public AjaxResult swaggerTest() {
        Map<String, Object> data = new HashMap<>();
        data.put("swagger", "working");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "Swagger配置正常");
        
        return AjaxResult.success("Swagger测试成功", data);
    }

    /**
     * 获取应用信息
     */
    @ApiOperation("获取应用信息")
    @GetMapping("/info")
    public AjaxResult getAppInfo() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "运输管理系统");
        data.put("version", "1.0.0");
        data.put("description", "基于Spring Boot的运输管理系统");
        data.put("author", "ruoyi");
        data.put("swagger-ui", "/transport/dev-api/swagger-ui/index.html");
        data.put("api-docs", "/transport/dev-api/v2/api-docs");
        
        return AjaxResult.success("获取应用信息成功", data);
    }
}
