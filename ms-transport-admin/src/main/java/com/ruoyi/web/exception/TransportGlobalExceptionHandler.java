package com.ruoyi.web.exception;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.exception.transport.TransportBusinessException;
import com.ruoyi.system.exception.transport.TransportException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.Set;

/**
 * 运输管理系统统一异常处理器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestControllerAdvice(basePackages = "com.ruoyi.web.controller.transport")
public class TransportGlobalExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(TransportGlobalExceptionHandler.class);
    
    /**
     * 处理运输业务异常
     */
    @ExceptionHandler(TransportBusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public AjaxResult handleTransportBusinessException(TransportBusinessException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生运输业务异常.", requestURI, e);
        
        return AjaxResult.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理运输系统异常
     */
    @ExceptionHandler(TransportException.class)
    @ResponseStatus(HttpStatus.OK)
    public AjaxResult handleTransportException(TransportException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生运输系统异常.", requestURI, e);
        
        String code = e.getCode() != null ? e.getCode() : "TRANSPORT_ERROR";
        return AjaxResult.error(code, e.getMessage());
    }
    
    /**
     * 权限校验异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public AjaxResult handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限校验失败'{}'", requestURI, e.getMessage());
        return AjaxResult.error(HttpStatus.FORBIDDEN.value(), "没有权限，请联系管理员授权");
    }
    
    /**
     * 请求参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public AjaxResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',参数校验异常'{}'", requestURI, e.getMessage());
        
        StringBuilder message = new StringBuilder();
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        }
        
        return AjaxResult.error("PARAM_VALIDATION_ERROR", message.toString());
    }
    
    /**
     * 请求参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public AjaxResult handleBindException(BindException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',参数绑定异常'{}'", requestURI, e.getMessage());
        
        StringBuilder message = new StringBuilder();
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        }
        
        return AjaxResult.error("PARAM_BIND_ERROR", message.toString());
    }
    
    /**
     * 参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public AjaxResult handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',参数校验异常'{}'", requestURI, e.getMessage());
        
        StringBuilder message = new StringBuilder();
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            message.append(violation.getPropertyPath()).append(": ").append(violation.getMessage()).append("; ");
        }
        
        return AjaxResult.error("CONSTRAINT_VIOLATION_ERROR", message.toString());
    }
    
    /**
     * 数据库约束异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public AjaxResult handleDuplicateKeyException(DuplicateKeyException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',数据库约束异常'{}'", requestURI, e.getMessage());
        
        String message = "数据已存在，请检查唯一性约束";
        if (e.getMessage().contains("license_plate")) {
            message = "车牌号已存在";
        } else if (e.getMessage().contains("driver_phone")) {
            message = "手机号已存在";
        } else if (e.getMessage().contains("id_card")) {
            message = "身份证号已存在";
        } else if (e.getMessage().contains("customer_code")) {
            message = "客户编码已存在";
        } else if (e.getMessage().contains("order_no")) {
            message = "运输单号已存在";
        }
        
        return AjaxResult.error("DUPLICATE_KEY_ERROR", message);
    }
    
    /**
     * 数据完整性异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public AjaxResult handleDataIntegrityViolationException(DataIntegrityViolationException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',数据完整性异常'{}'", requestURI, e.getMessage());
        
        String message = "数据完整性约束违反";
        if (e.getMessage().contains("foreign key constraint")) {
            message = "存在关联数据，无法删除";
        } else if (e.getMessage().contains("not null")) {
            message = "必填字段不能为空";
        }
        
        return AjaxResult.error("DATA_INTEGRITY_ERROR", message);
    }
    
    /**
     * SQL异常
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public AjaxResult handleSQLException(SQLException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',数据库异常'{}'", requestURI, e.getMessage());
        
        return AjaxResult.error("SQL_ERROR", "数据库操作异常，请联系管理员");
    }
    
    /**
     * 空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public AjaxResult handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',空指针异常.", requestURI, e);
        
        return AjaxResult.error("NULL_POINTER_ERROR", "系统内部错误，请联系管理员");
    }
    
    /**
     * 非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public AjaxResult handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',非法参数异常'{}'", requestURI, e.getMessage());
        
        return AjaxResult.error("ILLEGAL_ARGUMENT_ERROR", e.getMessage());
    }
    
    /**
     * 运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',运行时异常.", requestURI, e);
        
        return AjaxResult.error("RUNTIME_ERROR", "系统运行异常，请联系管理员");
    }
    
    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public AjaxResult handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);
        
        return AjaxResult.error("SYSTEM_ERROR", "系统异常，请联系管理员");
    }
}
