# 运输管理系统API接口快速参考

## 基础信息
- **基础URL**: `http://localhost:8080/transport`
- **认证方式**: Bearer Token
- **响应格式**: JSON

## 1. 运输单管理 (/order)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:order:list | 查询运输单列表 |
| GET | `/{id}` | transport:order:query | 获取运输单详情 |
| POST | `/` | transport:order:add | 新增运输单 |
| PUT | `/` | transport:order:edit | 修改运输单 |
| DELETE | `/{ids}` | transport:order:remove | 删除运输单 |
| POST | `/export` | transport:order:export | 导出运输单 |
| GET | `/generateOrderNo` | - | 生成运输单号 |
| GET | `/generateInternalCode` | - | 生成内部编号 |
| PUT | `/status/{id}/{status}` | transport:order:edit | 更新运输单状态 |
| PUT | `/assign/{id}` | transport:order:assign | 指派车辆和司机 |
| PUT | `/startLoading/{id}` | transport:order:edit | 开始装货 |
| PUT | `/completeLoading/{id}` | transport:order:edit | 完成装货 |
| PUT | `/startTransporting/{id}` | transport:order:edit | 开始运输 |
| PUT | `/completeDelivery/{id}` | transport:order:edit | 完成配送 |
| PUT | `/completeBilling/{id}` | transport:order:edit | 完成对账 |
| GET | `/vehicle/{vehicleId}` | - | 根据车辆ID查询运输单 |
| GET | `/driver/{driverId}` | - | 根据司机ID查询运输单 |
| GET | `/customer/{customerId}` | - | 根据客户ID查询运输单 |
| GET | `/statistics/status` | - | 统计各状态运输单数量 |
| GET | `/pending` | - | 查询待指派运输单 |
| GET | `/transporting` | - | 查询运输中运输单 |
| GET | `/orderNo/{orderNo}` | - | 根据运输单号查询 |
| GET | `/nextStatuses/{currentStatus}` | - | 获取下一个可能的状态 |

## 2. 车辆管理 (/vehicle)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:vehicle:list | 查询车辆列表 |
| GET | `/{id}` | transport:vehicle:query | 获取车辆详情 |
| POST | `/` | transport:vehicle:add | 新增车辆 |
| PUT | `/` | transport:vehicle:edit | 修改车辆 |
| DELETE | `/{ids}` | transport:vehicle:remove | 删除车辆 |
| POST | `/export` | transport:vehicle:export | 导出车辆列表 |
| GET | `/available` | - | 查询可用车辆 |
| PUT | `/status/{id}/{status}` | transport:vehicle:edit | 更新车辆状态 |
| GET | `/licensePlate/{licensePlate}` | - | 根据车牌号查询车辆 |
| GET | `/checkLicensePlate/{licensePlate}` | - | 检查车牌号是否存在 |
| GET | `/region/{regionCode}` | - | 根据区域查询车辆 |
| GET | `/statistics/status` | - | 统计各状态车辆数量 |
| GET | `/expiring` | - | 查询即将到期车辆 |

## 3. 司机管理 (/driver)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:driver:list | 查询司机列表 |
| GET | `/{id}` | transport:driver:query | 获取司机详情 |
| POST | `/` | transport:driver:add | 新增司机 |
| PUT | `/` | transport:driver:edit | 修改司机 |
| DELETE | `/{ids}` | transport:driver:remove | 删除司机 |
| POST | `/export` | transport:driver:export | 导出司机列表 |
| GET | `/available` | - | 查询可用司机 |
| PUT | `/status/{id}/{status}` | transport:driver:edit | 更新司机状态 |
| GET | `/phone/{driverPhone}` | - | 根据手机号查询司机 |
| GET | `/checkPhone/{driverPhone}` | - | 检查手机号是否存在 |
| GET | `/checkIdCard/{idCard}` | - | 检查身份证号是否存在 |
| GET | `/statistics/status` | - | 统计各状态司机数量 |
| GET | `/licenseType/{licenseType}` | - | 根据驾照类型查询司机 |
| GET | `/experienced` | - | 查询经验丰富司机 |

## 4. 客户管理 (/customer)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:customer:list | 查询客户列表 |
| GET | `/{id}` | transport:customer:query | 获取客户详情 |
| POST | `/` | transport:customer:add | 新增客户 |
| PUT | `/` | transport:customer:edit | 修改客户 |
| DELETE | `/{ids}` | transport:customer:remove | 删除客户 |
| POST | `/export` | transport:customer:export | 导出客户列表 |
| GET | `/code/{customerCode}` | - | 根据客户编码查询 |
| GET | `/checkCode/{customerCode}` | - | 检查客户编码是否存在 |
| GET | `/checkName/{customerName}` | - | 检查客户名称是否存在 |
| GET | `/active` | - | 查询正常合作客户 |
| PUT | `/status/{id}/{status}` | transport:customer:edit | 更新客户状态 |
| GET | `/statistics/status` | - | 统计各状态客户数量 |
| GET | `/creditRating/{creditRating}` | - | 根据信用等级查询客户 |
| GET | `/paymentMethod/{paymentMethod}` | - | 根据付款方式查询客户 |
| GET | `/vip` | - | 查询VIP客户 |

## 5. 收货方管理 (/consignee)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:consignee:list | 查询收货方列表 |
| GET | `/{id}` | transport:consignee:query | 获取收货方详情 |
| POST | `/` | transport:consignee:add | 新增收货方 |
| PUT | `/` | transport:consignee:edit | 修改收货方 |
| DELETE | `/{ids}` | transport:consignee:remove | 删除收货方 |
| POST | `/export` | transport:consignee:export | 导出收货方列表 |
| GET | `/customer/{customerId}` | - | 根据客户ID查询收货方 |
| GET | `/code/{consigneeCode}` | - | 根据收货方编码查询 |
| GET | `/checkCode/{consigneeCode}` | - | 检查收货方编码是否存在 |
| GET | `/active` | - | 查询启用的收货方 |
| PUT | `/status/{id}/{isActive}` | transport:consignee:edit | 更新收货方状态 |
| GET | `/type/{consigneeType}` | - | 根据收货方类型查询 |
| GET | `/region` | - | 根据地区查询收货方 |

## 6. 装货点管理 (/loadingPoint)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:loadingPoint:list | 查询装货点列表 |
| GET | `/{id}` | transport:loadingPoint:query | 获取装货点详情 |
| POST | `/` | transport:loadingPoint:add | 新增装货点 |
| PUT | `/` | transport:loadingPoint:edit | 修改装货点 |
| DELETE | `/{ids}` | transport:loadingPoint:remove | 删除装货点 |
| POST | `/export` | transport:loadingPoint:export | 导出装货点列表 |
| GET | `/code/{pointCode}` | - | 根据装货点编码查询 |
| GET | `/checkCode/{pointCode}` | - | 检查装货点编码是否存在 |
| GET | `/active` | - | 查询启用的装货点 |
| PUT | `/status/{id}/{isActive}` | transport:loadingPoint:edit | 更新装货点状态 |
| GET | `/type/{pointType}` | - | 根据装货点类型查询 |
| GET | `/region` | - | 根据地区查询装货点 |

## 7. 运费规则管理 (/pricingRule)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:pricingRule:list | 查询运费规则列表 |
| GET | `/{id}` | transport:pricingRule:query | 获取运费规则详情 |
| POST | `/` | transport:pricingRule:add | 新增运费规则 |
| PUT | `/` | transport:pricingRule:edit | 修改运费规则 |
| DELETE | `/{ids}` | transport:pricingRule:remove | 删除运费规则 |
| POST | `/export` | transport:pricingRule:export | 导出运费规则列表 |
| GET | `/active` | - | 查询有效的运费规则 |
| POST | `/calculate` | - | 计算运输费用 |
| GET | `/findRule` | - | 查找适用的计费规则 |

## 8. 地址管理 (/countries)

| 方法 | 路径 | 权限 | 描述 |
|------|------|------|------|
| GET | `/list` | transport:countries:list | 查询国家列表 |
| GET | `/provinces/{countryCode}` | transport:countries:list | 查询省份列表 |
| GET | `/cities/{provinceCode}` | transport:countries:list | 查询城市列表 |
| GET | `/districts/{cityCode}` | transport:countries:list | 查询区县列表 |
| GET | `/country/{code}` | transport:countries:query | 根据国家编码查询国家信息 |
| GET | `/province/{code}` | transport:countries:query | 根据省份编码查询省份信息 |
| GET | `/city/{code}` | transport:countries:query | 根据城市编码查询城市信息 |
| GET | `/district/{code}` | transport:countries:query | 根据区县编码查询区县信息 |

## 状态码说明

### 运输单状态
- 1: 待指派
- 2: 已指派  
- 3: 前往装货
- 4: 装货中
- 5: 运输中
- 6: 已送达
- 7: 已对账

### 车辆状态
- 1: 空闲
- 2: 运输中
- 3: 维修中
- 4: 报废

### 司机状态
- 1: 在职
- 2: 休假
- 3: 离职

### 客户状态
- 1: 正常合作
- 2: 暂停合作
- 3: 终止合作

### 计费方式
- 1: 按距离
- 2: 按重量
- 3: 按体积
- 4: 固定价格

## 常用查询参数

### 分页参数
- `pageNum`: 页码（从1开始）
- `pageSize`: 每页记录数

### 时间范围
- `params.beginTime`: 开始时间 (yyyy-MM-dd)
- `params.endTime`: 结束时间 (yyyy-MM-dd)

### 模糊查询
大部分名称、编码字段支持模糊查询，直接传入关键字即可。

## 权限前缀说明
- `transport:order:*` - 运输单相关权限
- `transport:vehicle:*` - 车辆相关权限  
- `transport:driver:*` - 司机相关权限
- `transport:customer:*` - 客户相关权限
- `transport:consignee:*` - 收货方相关权限
- `transport:loadingPoint:*` - 装货点相关权限
- `transport:pricingRule:*` - 运费规则相关权限

权限操作类型：
- `list` - 查询列表
- `query` - 查询详情
- `add` - 新增
- `edit` - 修改
- `remove` - 删除
- `export` - 导出
- `assign` - 指派（运输单专用）
