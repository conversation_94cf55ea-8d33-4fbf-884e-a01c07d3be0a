# 运输管理系统统一异常处理指南

## 概述

本指南介绍运输管理系统的统一异常处理机制，包括自定义异常类、错误码定义、全局异常处理器的使用方法和最佳实践。

## 1. 异常处理架构

### 1.1 异常类层次结构
```
TransportException (基础异常)
├── TransportBusinessException (业务异常)
│   ├── OrderException (运输单异常)
│   ├── VehicleException (车辆异常)
│   ├── DriverException (司机异常)
│   ├── CustomerException (客户异常)
│   ├── StatusException (状态异常)
│   ├── PricingException (运费计算异常)
│   └── ValidationException (数据验证异常)
└── TransportSystemException (系统异常)
```

### 1.2 核心组件
- **TransportException** - 基础异常类
- **TransportBusinessException** - 业务异常类
- **TransportErrorCode** - 错误码枚举
- **TransportExceptionUtils** - 异常工具类
- **TransportGlobalExceptionHandler** - 全局异常处理器

## 2. 错误码规范

### 2.1 错误码分类
| 范围 | 分类 | 说明 |
|------|------|------|
| 1000-1099 | 通用错误 | 系统级别的通用错误 |
| 1100-1199 | 运输单错误 | 运输单相关的业务错误 |
| 1200-1299 | 车辆错误 | 车辆管理相关错误 |
| 1300-1399 | 司机错误 | 司机管理相关错误 |
| 1400-1499 | 客户错误 | 客户管理相关错误 |
| 1500-1599 | 收货方错误 | 收货方管理相关错误 |
| 1600-1699 | 装货点错误 | 装货点管理相关错误 |
| 1700-1799 | 运费计算错误 | 运费计算相关错误 |
| 1800-1899 | 状态流转错误 | 状态管理相关错误 |
| 1900-1999 | 权限错误 | 权限和认证相关错误 |

### 2.2 常用错误码
```java
// 运输单相关
ORDER_NOT_FOUND("1100", "运输单不存在")
ORDER_STATUS_ERROR("1101", "运输单状态不允许该操作")
ORDER_NO_EXISTS("1102", "运输单号已存在")

// 车辆相关
VEHICLE_NOT_FOUND("1200", "车辆不存在")
VEHICLE_NOT_AVAILABLE("1201", "车辆不可用")
LICENSE_PLATE_EXISTS("1202", "车牌号已存在")

// 司机相关
DRIVER_NOT_FOUND("1300", "司机不存在")
DRIVER_PHONE_EXISTS("1302", "手机号已存在")
ID_CARD_EXISTS("1303", "身份证号已存在")
```

## 3. 使用方法

### 3.1 在Service层抛出异常

#### 方法一：使用错误码枚举
```java
@Service
public class TransportOrderServiceImpl implements ITransportOrderService {
    
    @Override
    public TransportOrder selectTransportOrderById(Long id) {
        TransportOrder order = transportOrderMapper.selectTransportOrderById(id);
        if (order == null) {
            throw TransportErrorCode.ORDER_NOT_FOUND.createException();
        }
        return order;
    }
    
    @Override
    public int assignVehicleAndDriver(Long orderId, Long vehicleId, Long driverId) {
        // 检查运输单状态
        TransportOrder order = selectTransportOrderById(orderId);
        if (order.getOrderStatus() != 1) {
            throw TransportErrorCode.ORDER_STATUS_ERROR.createException("只有待指派状态的运输单才能指派");
        }
        
        // 检查车辆可用性
        TransportVehicle vehicle = vehicleService.selectById(vehicleId);
        if (vehicle == null) {
            throw TransportErrorCode.VEHICLE_NOT_FOUND.createException();
        }
        if (vehicle.getVehicleStatus() != 1) {
            throw TransportErrorCode.VEHICLE_NOT_AVAILABLE.createException();
        }
        
        // 执行指派逻辑...
        return 1;
    }
}
```

#### 方法二：使用异常工具类
```java
@Service
public class TransportOrderServiceImpl implements ITransportOrderService {
    
    @Override
    public int assignVehicleAndDriver(Long orderId, Long vehicleId, Long driverId) {
        // 检查运输单是否存在
        TransportOrder order = transportOrderMapper.selectTransportOrderById(orderId);
        TransportExceptionUtils.checkOrderExists(order);
        
        // 检查运输单状态
        TransportExceptionUtils.assertTrue(order.getOrderStatus() == 1, 
            "只有待指派状态的运输单才能指派车辆和司机");
        
        // 检查车辆可用性
        TransportVehicle vehicle = vehicleMapper.selectById(vehicleId);
        TransportExceptionUtils.checkVehicleExists(vehicle);
        TransportExceptionUtils.checkVehicleAvailable(vehicle.getVehicleStatus());
        
        // 检查司机可用性
        TransportDriver driver = driverMapper.selectById(driverId);
        TransportExceptionUtils.checkDriverExists(driver);
        TransportExceptionUtils.checkDriverAvailable(driver.getDriverStatus());
        
        // 执行指派逻辑...
        return 1;
    }
}
```

#### 方法三：使用具体业务异常类
```java
@Service
public class TransportPricingServiceImpl implements ITransportPricingService {
    
    @Override
    public BigDecimal calculateShippingCost(Long customerId, String productType, BigDecimal distance) {
        TransportPricingRule rule = findApplicableRule(customerId, productType);
        if (rule == null) {
            throw new TransportBusinessException.PricingException("未找到适用的计费规则");
        }
        
        if (distance == null || distance.compareTo(BigDecimal.ZERO) <= 0) {
            throw new TransportBusinessException.ValidationException("运输距离必须大于0");
        }
        
        // 计算运费逻辑...
        return BigDecimal.ZERO;
    }
}
```

### 3.2 在Controller层处理

#### 推荐方式：依赖全局异常处理器
```java
@RestController
@RequestMapping("/transport/order")
public class TransportOrderController {
    
    @Autowired
    private ITransportOrderService transportOrderService;
    
    /**
     * 新增运输单 - 不需要try-catch，异常由全局处理器处理
     */
    @PostMapping
    public AjaxResult add(@RequestBody TransportOrder transportOrder) {
        return toAjax(transportOrderService.insertTransportOrder(transportOrder));
    }
    
    /**
     * 指派车辆和司机 - 不需要try-catch
     */
    @PutMapping("/assign/{id}")
    public AjaxResult assign(@PathVariable Long id, 
                           @RequestParam Long vehicleId, 
                           @RequestParam Long driverId) {
        return toAjax(transportOrderService.assignVehicleAndDriver(id, vehicleId, driverId));
    }
}
```

#### 特殊情况：需要特殊处理的异常
```java
@RestController
@RequestMapping("/transport/order")
public class TransportOrderController {
    
    /**
     * 批量导入 - 需要特殊处理部分成功的情况
     */
    @PostMapping("/import")
    public AjaxResult importOrders(@RequestParam MultipartFile file) {
        try {
            ImportResult result = transportOrderService.importOrders(file);
            return AjaxResult.success(result);
        } catch (TransportBusinessException.ValidationException e) {
            // 数据验证异常，返回详细的验证错误信息
            return AjaxResult.error("IMPORT_VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            // 其他异常交给全局处理器
            throw e;
        }
    }
}
```

## 4. 全局异常处理器

### 4.1 异常处理优先级
1. **TransportBusinessException** - 业务异常，返回具体错误码和消息
2. **TransportException** - 系统异常，返回通用错误信息
3. **AccessDeniedException** - 权限异常，返回403状态码
4. **MethodArgumentNotValidException** - 参数校验异常，返回详细验证信息
5. **DuplicateKeyException** - 数据库约束异常，返回友好提示
6. **Exception** - 其他异常，返回通用系统错误

### 4.2 响应格式
```json
{
  "code": "1100",           // 错误码
  "msg": "运输单不存在",     // 错误消息
  "data": null              // 数据为空
}
```

## 5. 最佳实践

### 5.1 异常抛出原则
1. **及早抛出** - 在发现问题的第一时间抛出异常
2. **具体明确** - 使用具体的错误码和清晰的错误消息
3. **业务相关** - 抛出与业务逻辑相关的异常类型
4. **避免吞噬** - 不要捕获异常后不处理或只打印日志

### 5.2 错误消息规范
```java
// ✅ 好的做法 - 消息清晰具体
throw TransportErrorCode.ORDER_STATUS_ERROR.createException("只有待指派状态的运输单才能指派车辆和司机");

// ❌ 不好的做法 - 消息模糊
throw new RuntimeException("操作失败");

// ✅ 好的做法 - 包含关键信息
throw TransportErrorCode.VEHICLE_NOT_AVAILABLE.createException(
    String.format("车辆[%s]不可用，当前状态：%s", vehicle.getLicensePlate(), vehicle.getVehicleStatusText()));

// ❌ 不好的做法 - 缺少关键信息
throw new RuntimeException("车辆不可用");
```

### 5.3 异常处理层次
```java
// Service层 - 抛出业务异常
@Service
public class TransportOrderServiceImpl {
    public void updateOrderStatus(Long orderId, Integer targetStatus) {
        TransportOrder order = getOrderById(orderId);
        if (!isValidStatusTransition(order.getOrderStatus(), targetStatus)) {
            throw TransportErrorCode.STATUS_TRANSITION_ERROR.createException(
                String.format("不能从状态[%d]转换到状态[%d]", order.getOrderStatus(), targetStatus));
        }
        // 更新逻辑...
    }
}

// Controller层 - 不处理异常，交给全局处理器
@RestController
public class TransportOrderController {
    @PutMapping("/status/{id}/{status}")
    public AjaxResult updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        transportOrderService.updateOrderStatus(id, status);
        return AjaxResult.success();
    }
}

// 全局异常处理器 - 统一处理和响应
@RestControllerAdvice
public class TransportGlobalExceptionHandler {
    @ExceptionHandler(TransportBusinessException.class)
    public AjaxResult handleBusinessException(TransportBusinessException e) {
        return AjaxResult.error(e.getCode(), e.getMessage());
    }
}
```

### 5.4 日志记录
```java
@RestControllerAdvice
public class TransportGlobalExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(TransportGlobalExceptionHandler.class);
    
    @ExceptionHandler(TransportBusinessException.class)
    public AjaxResult handleBusinessException(TransportBusinessException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        // 业务异常记录为WARN级别
        log.warn("请求地址'{}',发生业务异常: {}", requestURI, e.getMessage());
        return AjaxResult.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        // 系统异常记录为ERROR级别，包含堆栈信息
        log.error("请求地址'{}',发生系统异常", requestURI, e);
        return AjaxResult.error("SYSTEM_ERROR", "系统异常，请联系管理员");
    }
}
```

## 6. 测试建议

### 6.1 单元测试
```java
@Test
public void testAssignVehicleAndDriver_OrderNotFound() {
    // 测试运输单不存在的情况
    Long orderId = 999L;
    Long vehicleId = 1L;
    Long driverId = 1L;
    
    TransportBusinessException exception = assertThrows(
        TransportBusinessException.class,
        () -> transportOrderService.assignVehicleAndDriver(orderId, vehicleId, driverId)
    );
    
    assertEquals("ORDER_ERROR", exception.getCode());
    assertTrue(exception.getMessage().contains("运输单不存在"));
}

@Test
public void testAssignVehicleAndDriver_VehicleNotAvailable() {
    // 测试车辆不可用的情况
    // ... 测试代码
}
```

### 6.2 集成测试
```java
@Test
public void testAssignVehicleAndDriver_Integration() {
    // 准备测试数据
    TransportOrder order = createTestOrder();
    TransportVehicle vehicle = createTestVehicle();
    TransportDriver driver = createTestDriver();
    
    // 设置车辆为维修状态
    vehicle.setVehicleStatus(3);
    vehicleService.updateVehicle(vehicle);
    
    // 调用接口，期望返回错误
    ResponseEntity<AjaxResult> response = restTemplate.exchange(
        "/transport/order/assign/" + order.getId() + "?vehicleId=" + vehicle.getId() + "&driverId=" + driver.getId(),
        HttpMethod.PUT,
        null,
        AjaxResult.class
    );
    
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals("VEHICLE_ERROR", response.getBody().get("code"));
}
```

## 7. 总结

通过使用统一异常处理机制，我们实现了：

1. **代码简化** - Controller层不需要大量try-catch块
2. **错误统一** - 所有错误都有统一的格式和处理方式
3. **维护性好** - 错误码和消息集中管理，易于维护
4. **用户友好** - 提供清晰的错误信息，便于前端处理
5. **日志完整** - 自动记录异常日志，便于问题排查

这种异常处理机制大大提高了代码质量和开发效率，是企业级应用的最佳实践。
