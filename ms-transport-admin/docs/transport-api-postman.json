{"info": {"name": "运输管理系统API", "description": "运输管理系统所有API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/transport", "type": "string"}, {"key": "token", "value": "your-jwt-token-here", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "运输单管理", "item": [{"name": "查询运输单列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/order/list?pageNum=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["order", "list"], "query": [{"key": "pageNum", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "orderNo", "value": "", "disabled": true}, {"key": "customerName", "value": "", "disabled": true}, {"key": "orderStatus", "value": "", "disabled": true}]}}}, {"name": "获取运输单详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/order/1", "host": ["{{baseUrl}}"], "path": ["order", "1"]}}}, {"name": "新增运输单", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerName\": \"XX石油贸易公司\",\n  \"consigneeName\": \"XX加油站\",\n  \"consigneeAddress\": \"北京市朝阳区XX路XX号\",\n  \"consigneeContact\": \"张三\",\n  \"consigneePhone\": \"13800138001\",\n  \"loadingPointName\": \"上海港石油码头\",\n  \"loadingAddress\": \"上海市浦东新区港口大道1号\",\n  \"productName\": \"92号汽油\",\n  \"productQuantity\": 10.50,\n  \"totalVolume\": 12000.00,\n  \"transportDistance\": 1200.00,\n  \"shippingCost\": 3000.00,\n  \"deliveryRequirements\": \"工作时间配送\"\n}"}, "url": {"raw": "{{baseUrl}}/order", "host": ["{{baseUrl}}"], "path": ["order"]}}}, {"name": "指派车辆和司机", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/order/assign/1?vehicleId=1&driverId=1", "host": ["{{baseUrl}}"], "path": ["order", "assign", "1"], "query": [{"key": "vehicleId", "value": "1"}, {"key": "driverId", "value": "1"}]}}}, {"name": "开始装货", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/order/startLoading/1", "host": ["{{baseUrl}}"], "path": ["order", "startLoading", "1"]}}}, {"name": "生成运输单号", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/order/generateOrderNo", "host": ["{{baseUrl}}"], "path": ["order", "generateOrderNo"]}}}]}, {"name": "车辆管理", "item": [{"name": "查询车辆列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/vehicle/list?pageNum=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["vehicle", "list"], "query": [{"key": "pageNum", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "licensePlate", "value": "", "disabled": true}, {"key": "vehicleStatus", "value": "", "disabled": true}]}}}, {"name": "新增车辆", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"licensePlate\": \"京A12345\",\n  \"vehicleType\": \"油罐车\",\n  \"loadCapacity\": 20.00,\n  \"fuelTankCapacity\": 300.00,\n  \"vehicleStatus\": 1,\n  \"purchaseDate\": \"2023-01-01\",\n  \"regionCode\": \"110000\"\n}"}, "url": {"raw": "{{baseUrl}}/vehicle", "host": ["{{baseUrl}}"], "path": ["vehicle"]}}}, {"name": "查询可用车辆", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/vehicle/available", "host": ["{{baseUrl}}"], "path": ["vehicle", "available"]}}}]}, {"name": "司机管理", "item": [{"name": "查询司机列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/driver/list?pageNum=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["driver", "list"], "query": [{"key": "pageNum", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "<PERSON><PERSON><PERSON>", "value": "", "disabled": true}, {"key": "driverStatus", "value": "", "disabled": true}]}}}, {"name": "新增司机", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"driverName\": \"张三\",\n  \"driverPhone\": \"13800138001\",\n  \"idCard\": \"110101199001011234\",\n  \"licenseType\": \"A2\",\n  \"licenseNumber\": \"110101199001011234\",\n  \"drivingYears\": 5,\n  \"driverStatus\": 1,\n  \"hireDate\": \"2023-01-01\"\n}"}, "url": {"raw": "{{baseUrl}}/driver", "host": ["{{baseUrl}}"], "path": ["driver"]}}}, {"name": "查询可用司机", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/driver/available", "host": ["{{baseUrl}}"], "path": ["driver", "available"]}}}]}, {"name": "客户管理", "item": [{"name": "查询客户列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/customer/list?pageNum=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["customer", "list"], "query": [{"key": "pageNum", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "customerName", "value": "", "disabled": true}, {"key": "customerStatus", "value": "", "disabled": true}]}}}, {"name": "新增客户", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerName\": \"XX石油贸易公司\",\n  \"customerCode\": \"CUST001\",\n  \"companyType\": \"贸易公司\",\n  \"contactPerson\": \"张经理\",\n  \"contactPhone\": \"13800138001\",\n  \"customerStatus\": 1,\n  \"creditRating\": \"AAA\",\n  \"paymentMethod\": 1\n}"}, "url": {"raw": "{{baseUrl}}/customer", "host": ["{{baseUrl}}"], "path": ["customer"]}}}]}, {"name": "运费计算", "item": [{"name": "计算运输费用", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/pricingRule/calculate?distance=1200&weight=10.5&volume=12000", "host": ["{{baseUrl}}"], "path": ["pricingRule", "calculate"], "query": [{"key": "customerId", "value": "", "disabled": true}, {"key": "productType", "value": "", "disabled": true}, {"key": "distance", "value": "1200"}, {"key": "weight", "value": "10.5"}, {"key": "volume", "value": "12000"}]}}}, {"name": "查找适用规则", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/pricingRule/findRule?customerId=1&productType=汽油", "host": ["{{baseUrl}}"], "path": ["pricingRule", "findRule"], "query": [{"key": "customerId", "value": "1"}, {"key": "routeId", "value": "", "disabled": true}, {"key": "productType", "value": "汽油"}]}}}]}]}