# 运输管理系统异常处理使用示例

## 概述

本文档提供运输管理系统异常处理的具体使用示例，展示如何正确使用异常工具类和错误码。

## 1. TransportExceptionUtils 使用方法

### 1.1 assertNotNull 方法

```java
// 方法一：使用默认错误消息
TransportExceptionUtils.assertNotNull(order, TransportErrorCode.ORDER_NOT_FOUND);

// 方法二：使用自定义错误消息
TransportExceptionUtils.assertNotNull(order, TransportErrorCode.ORDER_NOT_FOUND, "指定的运输单不存在");

// 方法三：使用字符串消息（会抛出通用业务异常）
TransportExceptionUtils.assertNotNull(order, "运输单不存在");
```

### 1.2 assertTrue 方法

```java
// 方法一：使用默认错误消息
TransportExceptionUtils.assertTrue(order.getOrderStatus() == 1, TransportErrorCode.ORDER_STATUS_ERROR);

// 方法二：使用自定义错误消息
TransportExceptionUtils.assertTrue(order.getOrderStatus() == 1, 
    TransportErrorCode.ORDER_STATUS_ERROR, "只有待指派状态的运输单才能指派车辆和司机");

// 方法三：使用字符串消息
TransportExceptionUtils.assertTrue(order.getOrderStatus() == 1, "运输单状态不允许该操作");
```

### 1.3 专用检查方法

```java
// 检查运输单是否存在
TransportExceptionUtils.checkOrderExists(order);

// 检查车辆是否存在
TransportExceptionUtils.checkVehicleExists(vehicle);

// 检查司机是否存在
TransportExceptionUtils.checkDriverExists(driver);

// 检查客户是否存在
TransportExceptionUtils.checkCustomerExists(customer);

// 检查车辆是否可用
TransportExceptionUtils.checkVehicleAvailable(vehicle.getVehicleStatus());

// 检查司机是否可用
TransportExceptionUtils.checkDriverAvailable(driver.getDriverStatus());
```

## 2. 直接抛出异常的方法

### 2.1 使用错误码枚举

```java
// 抛出默认消息的异常
throw TransportErrorCode.ORDER_NOT_FOUND.createException();

// 抛出自定义消息的异常
throw TransportErrorCode.ORDER_STATUS_ERROR.createException("只有待指派状态的运输单才能指派车辆和司机");

// 抛出带参数的消息
throw TransportErrorCode.VEHICLE_NOT_AVAILABLE.createException(
    String.format("车辆[%s]不可用，当前状态：%s", vehicle.getLicensePlate(), vehicle.getVehicleStatusText()));
```

### 2.2 使用业务异常类

```java
// 运输单异常
throw new TransportBusinessException.OrderException("运输单状态不允许该操作");

// 车辆异常
throw new TransportBusinessException.VehicleException("车辆正在维修中");

// 司机异常
throw new TransportBusinessException.DriverException("司机正在休假");

// 客户异常
throw new TransportBusinessException.CustomerException("客户已暂停合作");

// 状态异常
throw new TransportBusinessException.StatusException("状态流转不合法");

// 运费计算异常
throw new TransportBusinessException.PricingException("未找到适用的计费规则");

// 数据验证异常
throw new TransportBusinessException.ValidationException("运输数量必须大于0");
```

## 3. 完整的Service方法示例

### 3.1 运输单指派方法

```java
@Override
@Transactional
public int assignVehicleAndDriver(Long orderId, Long vehicleId, Long driverId) {
    // 1. 检查运输单是否存在
    TransportOrder order = transportOrderMapper.selectTransportOrderById(orderId);
    TransportExceptionUtils.checkOrderExists(order);
    
    // 2. 检查运输单状态
    TransportExceptionUtils.assertTrue(order.getOrderStatus() == 1, 
        TransportErrorCode.ORDER_STATUS_ERROR, "只有待指派状态的运输单才能指派车辆和司机");
    
    // 3. 检查车辆可用性
    TransportVehicle vehicle = transportVehicleMapper.selectTransportVehicleById(vehicleId);
    TransportExceptionUtils.checkVehicleExists(vehicle);
    TransportExceptionUtils.checkVehicleAvailable(vehicle.getVehicleStatus());
    
    // 4. 检查司机可用性
    TransportDriver driver = transportDriverMapper.selectTransportDriverById(driverId);
    TransportExceptionUtils.checkDriverExists(driver);
    TransportExceptionUtils.checkDriverAvailable(driver.getDriverStatus());
    
    // 5. 执行指派操作
    int result = transportOrderMapper.assignVehicleAndDriver(orderId, vehicleId, driverId, 
                                                           vehicle.getLicensePlate(), SecurityUtils.getUsername());
    
    // 6. 更新车辆和司机状态
    if (result > 0) {
        transportVehicleMapper.updateVehicleStatus(vehicleId, 2, SecurityUtils.getUsername());
        transportDriverMapper.updateDriverStatus(driverId, 1, SecurityUtils.getUsername());
    }
    
    return result;
}
```

### 3.2 车辆新增方法

```java
@Override
@Transactional
public int insertTransportVehicle(TransportVehicle transportVehicle) {
    // 1. 数据验证
    TransportExceptionUtils.assertNotEmpty(transportVehicle.getLicensePlate(), "车牌号不能为空");
    TransportExceptionUtils.assertNotEmpty(transportVehicle.getVehicleType(), "车辆类型不能为空");
    TransportExceptionUtils.assertTrue(transportVehicle.getLoadCapacity() != null && 
        transportVehicle.getLoadCapacity().compareTo(BigDecimal.ZERO) > 0, "载重吨位必须大于0");
    
    // 2. 检查车牌号是否已存在
    if (checkLicensePlateExists(transportVehicle.getLicensePlate(), null)) {
        throw TransportErrorCode.LICENSE_PLATE_EXISTS.createException();
    }
    
    // 3. 设置默认值
    transportVehicle.setCreateBy(SecurityUtils.getUsername());
    transportVehicle.setCreateTime(DateUtils.getNowDate());
    transportVehicle.setIsDeleted(0);
    
    return transportVehicleMapper.insertTransportVehicle(transportVehicle);
}
```

### 3.3 运费计算方法

```java
@Override
public BigDecimal calculateShippingCost(Long customerId, Long routeId, String productType, 
                                      BigDecimal distance, BigDecimal weight, BigDecimal volume) {
    
    // 1. 参数验证
    TransportExceptionUtils.assertTrue(distance != null && distance.compareTo(BigDecimal.ZERO) > 0, 
        TransportErrorCode.PARAM_ERROR, "运输距离必须大于0");
    TransportExceptionUtils.assertTrue(weight != null && weight.compareTo(BigDecimal.ZERO) > 0, 
        TransportErrorCode.PARAM_ERROR, "运输重量必须大于0");
    
    // 2. 查找适用的计费规则
    TransportPricingRule rule = findApplicableRule(customerId, routeId, productType);
    if (rule == null) {
        throw TransportErrorCode.PRICING_RULE_NOT_FOUND.createException("未找到适用的计费规则");
    }
    
    // 3. 计算运费
    BigDecimal cost = BigDecimal.ZERO;
    try {
        switch (rule.getPricingType()) {
            case 1: // 按距离计费
                cost = calculateByDistance(rule, distance);
                break;
            case 2: // 按重量计费
                cost = calculateByWeight(rule, weight);
                break;
            case 3: // 按体积计费
                cost = calculateByVolume(rule, volume);
                break;
            case 4: // 固定价格
                cost = rule.getBasePrice();
                break;
            default:
                throw TransportErrorCode.PRICING_CALCULATION_ERROR.createException("不支持的计费方式");
        }
    } catch (Exception e) {
        throw TransportErrorCode.PRICING_CALCULATION_ERROR.createException("运费计算失败：" + e.getMessage());
    }
    
    // 4. 应用价格限制
    if (rule.getMinPrice() != null && cost.compareTo(rule.getMinPrice()) < 0) {
        cost = rule.getMinPrice();
    }
    if (rule.getMaxPrice() != null && cost.compareTo(rule.getMaxPrice()) > 0) {
        cost = rule.getMaxPrice();
    }
    
    return cost.setScale(2, RoundingMode.HALF_UP);
}
```

## 4. 状态流转检查示例

### 4.1 运输单状态流转

```java
@Override
@Transactional
public int updateOrderStatus(Long orderId, Integer targetStatus) {
    // 1. 检查运输单是否存在
    TransportOrder order = transportOrderMapper.selectTransportOrderById(orderId);
    TransportExceptionUtils.checkOrderExists(order);
    
    // 2. 检查状态流转是否合法
    if (!isValidStatusTransition(order.getOrderStatus(), targetStatus)) {
        throw TransportErrorCode.STATUS_TRANSITION_ERROR.createException(
            String.format("不能从状态[%s]转换到状态[%s]", 
                getStatusDescription(order.getOrderStatus()), 
                getStatusDescription(targetStatus)));
    }
    
    // 3. 执行状态更新
    int result = transportOrderMapper.updateOrderStatus(orderId, targetStatus, SecurityUtils.getUsername());
    
    // 4. 更新相关时间字段和车辆状态
    updateTimeFields(orderId, targetStatus);
    updateVehicleStatus(order, targetStatus);
    
    return result;
}

private boolean isValidStatusTransition(Integer currentStatus, Integer targetStatus) {
    TransportExceptionUtils.assertNotNull(currentStatus, "当前状态不能为空");
    TransportExceptionUtils.assertNotNull(targetStatus, "目标状态不能为空");
    
    // 定义合法的状态流转规则
    switch (currentStatus) {
        case 1: // 待指派
            return targetStatus == 2; // 只能到已指派
        case 2: // 已指派
            return targetStatus == 3; // 只能到前往装货
        case 3: // 前往装货
            return targetStatus == 4; // 只能到装货中
        case 4: // 装货中
            return targetStatus == 5; // 只能到运输中
        case 5: // 运输中
            return targetStatus == 6; // 只能到已送达
        case 6: // 已送达
            return targetStatus == 7; // 只能到已对账
        case 7: // 已对账
            return false; // 终态，不能再流转
        default:
            return false;
    }
}
```

## 5. 数据验证示例

### 5.1 复杂数据验证

```java
private void validateTransportOrder(TransportOrder order) {
    // 基础字段验证
    TransportExceptionUtils.assertNotEmpty(order.getCustomerName(), "客户名称不能为空");
    TransportExceptionUtils.assertNotEmpty(order.getConsigneeName(), "收货方名称不能为空");
    TransportExceptionUtils.assertNotEmpty(order.getConsigneeAddress(), "收货地址不能为空");
    TransportExceptionUtils.assertNotEmpty(order.getLoadingPointName(), "装货点名称不能为空");
    TransportExceptionUtils.assertNotEmpty(order.getProductName(), "油品名称不能为空");
    
    // 数值字段验证
    TransportExceptionUtils.assertTrue(order.getProductQuantity() != null && 
        order.getProductQuantity().compareTo(BigDecimal.ZERO) > 0, 
        TransportErrorCode.PARAM_ERROR, "运输数量必须大于0");
        
    TransportExceptionUtils.assertTrue(order.getShippingCost() != null && 
        order.getShippingCost().compareTo(BigDecimal.ZERO) > 0, 
        TransportErrorCode.PARAM_ERROR, "运输费用必须大于0");
    
    // 业务规则验证
    if (order.getPlannedDeliveryTime() != null && order.getPlannedLoadingTime() != null) {
        TransportExceptionUtils.assertTrue(
            order.getPlannedDeliveryTime().after(order.getPlannedLoadingTime()),
            TransportErrorCode.PARAM_ERROR, "计划送达时间必须晚于计划装货时间");
    }
}
```

## 6. 错误处理最佳实践

### 6.1 选择合适的异常处理方式

```java
// ✅ 推荐：使用工具类进行简单检查
TransportExceptionUtils.checkOrderExists(order);

// ✅ 推荐：使用工具类进行条件检查（带自定义消息）
TransportExceptionUtils.assertTrue(order.getOrderStatus() == 1, 
    TransportErrorCode.ORDER_STATUS_ERROR, "只有待指派状态的运输单才能指派");

// ✅ 推荐：复杂逻辑直接抛出异常
if (isComplexBusinessRule(order)) {
    throw TransportErrorCode.BUSINESS_RULE_VIOLATION.createException("违反业务规则：" + getRuleDescription());
}

// ❌ 不推荐：使用通用RuntimeException
throw new RuntimeException("操作失败");

// ❌ 不推荐：在Controller层处理业务异常
try {
    service.doSomething();
} catch (Exception e) {
    return AjaxResult.error(e.getMessage());
}
```

### 6.2 异常消息的编写规范

```java
// ✅ 好的异常消息 - 具体明确
throw TransportErrorCode.ORDER_STATUS_ERROR.createException("只有待指派状态的运输单才能指派车辆和司机");

// ✅ 好的异常消息 - 包含关键信息
throw TransportErrorCode.VEHICLE_NOT_AVAILABLE.createException(
    String.format("车辆[%s]不可用，当前状态：%s", vehicle.getLicensePlate(), vehicle.getVehicleStatusText()));

// ❌ 不好的异常消息 - 模糊不清
throw new RuntimeException("操作失败");

// ❌ 不好的异常消息 - 缺少关键信息
throw new RuntimeException("车辆不可用");
```

这些示例展示了如何正确使用运输管理系统的异常处理机制，确保代码的健壮性和可维护性。
