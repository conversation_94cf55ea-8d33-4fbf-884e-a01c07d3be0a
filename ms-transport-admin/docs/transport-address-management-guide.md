# 运输管理系统地址管理使用指南

## 概述

运输管理系统的地址管理模块提供了完整的四级地址体系（国家-省份-城市-区县），用于支持运输业务中的地址选择和管理功能。

## 1. 地址数据结构

### 1.1 四级地址体系
```
国家 (Countries)
├── 省份 (Provinces)
│   ├── 城市 (Cities)
│   │   ├── 区县 (Districts)
```

### 1.2 数据表结构
- **transport_countries** - 国家表
- **transport_provinces** - 省份表  
- **transport_cities** - 城市表
- **transport_districts** - 区县表

## 2. API接口使用

### 2.1 获取完整地址树
```javascript
// 获取包含所有层级的完整地址树
const getAddressTree = async () => {
  const response = await axios.get('/transport/countries/list');
  return response.data;
};

// 响应数据结构
{
  "code": 200,
  "data": [
    {
      "code": "CN",
      "name": "中国",
      "provinces": [
        {
          "code": "110000",
          "name": "北京市",
          "cities": [
            {
              "code": "110100", 
              "name": "北京市",
              "districts": [
                {
                  "code": "110101",
                  "name": "东城区"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 2.2 按层级获取地址数据
```javascript
// 获取省份列表
const getProvinces = async (countryCode) => {
  const response = await axios.get(`/transport/countries/provinces/${countryCode}`);
  return response.data;
};

// 获取城市列表
const getCities = async (provinceCode) => {
  const response = await axios.get(`/transport/countries/cities/${provinceCode}`);
  return response.data;
};

// 获取区县列表
const getDistricts = async (cityCode) => {
  const response = await axios.get(`/transport/countries/districts/${cityCode}`);
  return response.data;
};
```

### 2.3 根据编码查询地址信息
```javascript
// 查询省份信息
const getProvinceInfo = async (provinceCode) => {
  const response = await axios.get(`/transport/countries/province/${provinceCode}`);
  return response.data;
};

// 查询城市信息
const getCityInfo = async (cityCode) => {
  const response = await axios.get(`/transport/countries/city/${cityCode}`);
  return response.data;
};
```

## 3. 前端组件使用示例

### 3.1 Vue.js 地址选择器组件
```vue
<template>
  <div class="address-selector">
    <el-select v-model="selectedProvince" @change="onProvinceChange" placeholder="请选择省份">
      <el-option
        v-for="province in provinces"
        :key="province.code"
        :label="province.name"
        :value="province.code">
      </el-option>
    </el-select>
    
    <el-select v-model="selectedCity" @change="onCityChange" placeholder="请选择城市">
      <el-option
        v-for="city in cities"
        :key="city.code"
        :label="city.name"
        :value="city.code">
      </el-option>
    </el-select>
    
    <el-select v-model="selectedDistrict" @change="onDistrictChange" placeholder="请选择区县">
      <el-option
        v-for="district in districts"
        :key="district.code"
        :label="district.name"
        :value="district.code">
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getProvinces, getCities, getDistricts } from '@/api/transport/address';

export default {
  name: 'AddressSelector',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      provinces: [],
      cities: [],
      districts: [],
      selectedProvince: '',
      selectedCity: '',
      selectedDistrict: ''
    };
  },
  mounted() {
    this.loadProvinces();
  },
  methods: {
    async loadProvinces() {
      try {
        const response = await getProvinces('CN');
        this.provinces = response.data;
      } catch (error) {
        this.$message.error('加载省份数据失败');
      }
    },
    
    async onProvinceChange(provinceCode) {
      this.selectedCity = '';
      this.selectedDistrict = '';
      this.cities = [];
      this.districts = [];
      
      if (provinceCode) {
        try {
          const response = await getCities(provinceCode);
          this.cities = response.data;
        } catch (error) {
          this.$message.error('加载城市数据失败');
        }
      }
      
      this.emitChange();
    },
    
    async onCityChange(cityCode) {
      this.selectedDistrict = '';
      this.districts = [];
      
      if (cityCode) {
        try {
          const response = await getDistricts(cityCode);
          this.districts = response.data;
        } catch (error) {
          this.$message.error('加载区县数据失败');
        }
      }
      
      this.emitChange();
    },
    
    onDistrictChange() {
      this.emitChange();
    },
    
    emitChange() {
      const selectedAddress = {
        provinceCode: this.selectedProvince,
        provinceName: this.getNameByCode(this.provinces, this.selectedProvince),
        cityCode: this.selectedCity,
        cityName: this.getNameByCode(this.cities, this.selectedCity),
        districtCode: this.selectedDistrict,
        districtName: this.getNameByCode(this.districts, this.selectedDistrict)
      };
      
      this.$emit('input', selectedAddress);
      this.$emit('change', selectedAddress);
    },
    
    getNameByCode(list, code) {
      const item = list.find(item => item.code === code);
      return item ? item.name : '';
    }
  }
};
</script>
```

### 3.2 React 地址选择器组件
```jsx
import React, { useState, useEffect } from 'react';
import { Select, message } from 'antd';
import { getProvinces, getCities, getDistricts } from '@/api/transport/address';

const { Option } = Select;

const AddressSelector = ({ value, onChange }) => {
  const [provinces, setProvinces] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [selectedProvince, setSelectedProvince] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedDistrict, setSelectedDistrict] = useState('');

  useEffect(() => {
    loadProvinces();
  }, []);

  const loadProvinces = async () => {
    try {
      const response = await getProvinces('CN');
      setProvinces(response.data);
    } catch (error) {
      message.error('加载省份数据失败');
    }
  };

  const onProvinceChange = async (provinceCode) => {
    setSelectedProvince(provinceCode);
    setSelectedCity('');
    setSelectedDistrict('');
    setCities([]);
    setDistricts([]);

    if (provinceCode) {
      try {
        const response = await getCities(provinceCode);
        setCities(response.data);
      } catch (error) {
        message.error('加载城市数据失败');
      }
    }

    emitChange(provinceCode, '', '');
  };

  const onCityChange = async (cityCode) => {
    setSelectedCity(cityCode);
    setSelectedDistrict('');
    setDistricts([]);

    if (cityCode) {
      try {
        const response = await getDistricts(cityCode);
        setDistricts(response.data);
      } catch (error) {
        message.error('加载区县数据失败');
      }
    }

    emitChange(selectedProvince, cityCode, '');
  };

  const onDistrictChange = (districtCode) => {
    setSelectedDistrict(districtCode);
    emitChange(selectedProvince, selectedCity, districtCode);
  };

  const emitChange = (provinceCode, cityCode, districtCode) => {
    const selectedAddress = {
      provinceCode,
      provinceName: getNameByCode(provinces, provinceCode),
      cityCode,
      cityName: getNameByCode(cities, cityCode),
      districtCode,
      districtName: getNameByCode(districts, districtCode)
    };

    onChange && onChange(selectedAddress);
  };

  const getNameByCode = (list, code) => {
    const item = list.find(item => item.code === code);
    return item ? item.name : '';
  };

  return (
    <div className="address-selector">
      <Select
        value={selectedProvince}
        onChange={onProvinceChange}
        placeholder="请选择省份"
        style={{ width: 120, marginRight: 8 }}
      >
        {provinces.map(province => (
          <Option key={province.code} value={province.code}>
            {province.name}
          </Option>
        ))}
      </Select>

      <Select
        value={selectedCity}
        onChange={onCityChange}
        placeholder="请选择城市"
        style={{ width: 120, marginRight: 8 }}
      >
        {cities.map(city => (
          <Option key={city.code} value={city.code}>
            {city.name}
          </Option>
        ))}
      </Select>

      <Select
        value={selectedDistrict}
        onChange={onDistrictChange}
        placeholder="请选择区县"
        style={{ width: 120 }}
      >
        {districts.map(district => (
          <Option key={district.code} value={district.code}>
            {district.name}
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default AddressSelector;
```

## 4. 数据管理

### 4.1 地址编码规则
- **国家编码**: 2位字母，如 CN
- **省份编码**: 6位数字，如 110000（北京市）
- **城市编码**: 6位数字，如 110100（北京市）
- **区县编码**: 6位数字，如 110101（东城区）

### 4.2 数据初始化
系统已预置中国的省份数据和部分城市、区县数据。如需完整数据，可以：

1. **导入完整地址数据**
```sql
-- 可以从国家统计局等官方渠道获取完整的地址编码数据
-- 按照表结构导入到对应的表中
```

2. **通过管理界面维护**
- 系统提供了地址管理的查询接口
- 可以根据需要扩展增删改功能

## 5. 最佳实践

### 5.1 性能优化
1. **缓存策略**: 地址数据相对稳定，建议在前端进行缓存
2. **懒加载**: 按需加载下级地址数据，避免一次性加载全部数据
3. **索引优化**: 数据库已建立必要的索引，支持快速查询

### 5.2 用户体验
1. **默认选择**: 可以根据用户IP或历史记录设置默认地址
2. **搜索功能**: 支持地址名称的模糊搜索
3. **快捷选择**: 提供常用地址的快捷选择功能

### 5.3 数据一致性
1. **编码唯一性**: 确保地址编码的唯一性
2. **层级关系**: 维护正确的上下级关系
3. **数据同步**: 如有地址变更，及时同步更新

## 6. 扩展功能

### 6.1 支持多国家
当前系统支持扩展到多个国家的地址管理，只需：
1. 在 transport_countries 表中添加其他国家数据
2. 按照对应国家的行政区划添加省份、城市、区县数据

### 6.2 地址验证
可以扩展地址验证功能：
```javascript
// 验证地址编码的有效性
const validateAddress = async (addressCodes) => {
  // 调用后端验证接口
  const response = await axios.post('/transport/countries/validate', addressCodes);
  return response.data;
};
```

### 6.3 地址搜索
可以扩展地址搜索功能：
```javascript
// 根据关键字搜索地址
const searchAddress = async (keyword) => {
  const response = await axios.get(`/transport/countries/search?keyword=${keyword}`);
  return response.data;
};
```

这个地址管理系统为运输管理提供了完整的地址支持，可以满足各种业务场景的需求。
