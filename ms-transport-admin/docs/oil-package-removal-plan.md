# Oil包删除计划

## 概述
本文档记录了删除oil包的详细计划和步骤，确保安全删除所有相关文件。

## 删除文件清单

### 1. Domain实体类 (ms-transport-system/src/main/java/com/ruoyi/system/domain/oil/)
需要删除整个oil目录及其所有子文件

### 2. Mapper接口 (ms-transport-system/src/main/java/com/ruoyi/system/mapper/oil/)
需要删除整个oil目录及其所有子文件

### 3. Service接口和实现类 (ms-transport-system/src/main/java/com/ruoyi/system/service/oil/)
需要删除整个oil目录及其所有子文件，包括：
- 接口文件
- impl实现类目录
- builder工具类目录

### 4. Controller控制器 (ms-transport-admin/src/main/java/com/ruoyi/web/controller/oil/)
需要删除整个oil目录及其所有子文件

### 5. Mapper XML文件 (ms-transport-system/src/main/resources/mapper/oil/)
需要删除整个oil目录及其所有子文件

### 6. 其他可能的相关文件
- 配置文件中的oil相关配置
- 测试文件中的oil相关测试
- 文档中的oil相关说明

## 删除前检查

### 1. 确认transport包功能完整
- ✅ transport地址管理功能已完成
- ✅ transport业务功能已完成
- ✅ 所有transport相关的Controller、Service、Mapper都已创建

### 2. 确认没有其他模块依赖oil包
- 检查是否有其他包引用oil包的类
- 检查配置文件是否有oil相关配置
- 检查是否有跨包调用

### 3. 数据库表处理
- oil相关的数据库表可以保留（如果有重要数据）
- 或者根据需要决定是否删除oil相关表

## 删除步骤

### 步骤1：删除Java源码文件
1. 删除 ms-transport-system/src/main/java/com/ruoyi/system/domain/oil/
2. 删除 ms-transport-system/src/main/java/com/ruoyi/system/mapper/oil/
3. 删除 ms-transport-system/src/main/java/com/ruoyi/system/service/oil/
4. 删除 ms-transport-admin/src/main/java/com/ruoyi/web/controller/oil/

### 步骤2：删除资源文件
1. 删除 ms-transport-system/src/main/resources/mapper/oil/

### 步骤3：清理配置文件
1. 检查并清理application.yml中的oil相关配置
2. 检查并清理其他配置文件中的oil引用

### 步骤4：清理测试文件
1. 删除test目录下的oil相关测试文件

### 步骤5：验证删除结果
1. 编译项目，确保没有编译错误
2. 启动项目，确保没有运行时错误
3. 测试transport功能，确保正常工作

## 风险评估

### 低风险
- Domain实体类：transport包已有对应实体
- Mapper和Service：transport包已有对应实现
- Controller：transport包已有对应接口

### 中等风险
- 可能存在的跨包引用
- 配置文件中的引用

### 高风险
- 数据库表的处理需要谨慎
- 如果有生产数据，需要备份

## 回滚计划

如果删除后出现问题，可以通过以下方式回滚：
1. 从版本控制系统恢复删除的文件
2. 重新编译和部署
3. 恢复数据库表（如果删除了的话）

## 删除后的好处

1. **代码简化**：移除不需要的代码，减少维护成本
2. **结构清晰**：只保留transport相关的业务逻辑
3. **性能提升**：减少不必要的类加载和内存占用
4. **部署简化**：减少部署包大小

## 注意事项

1. **备份重要数据**：删除前确保重要数据已备份
2. **分步执行**：建议分步骤执行，每步后验证系统正常
3. **团队通知**：通知团队成员oil包已删除，避免误用
4. **文档更新**：更新相关文档，移除oil包的说明

## 执行时间建议

建议在以下时间执行删除操作：
- 开发环境：随时可以执行
- 测试环境：在测试周期开始前执行
- 生产环境：在维护窗口期执行
