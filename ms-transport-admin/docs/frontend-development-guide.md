# 运输管理系统前端开发指南

## 概述

本指南为前端开发人员提供运输管理系统的开发规范、最佳实践和常见问题解决方案。

## 1. 项目结构建议

```
src/
├── views/transport/                 # 运输管理页面
│   ├── order/                      # 运输单管理
│   │   ├── index.vue              # 运输单列表页
│   │   ├── detail.vue             # 运输单详情页
│   │   ├── form.vue               # 运输单表单页
│   │   └── components/            # 运输单相关组件
│   │       ├── OrderStatusFlow.vue # 状态流转组件
│   │       ├── AssignDialog.vue    # 指派对话框
│   │       └── OrderTimeline.vue   # 时间轴组件
│   ├── vehicle/                    # 车辆管理
│   ├── driver/                     # 司机管理
│   ├── customer/                   # 客户管理
│   ├── consignee/                  # 收货方管理
│   ├── loadingPoint/               # 装货点管理
│   └── pricingRule/                # 运费规则管理
├── api/transport/                   # API接口
│   ├── order.js                   # 运输单API
│   ├── vehicle.js                 # 车辆API
│   ├── driver.js                  # 司机API
│   └── ...
├── components/transport/            # 通用组件
│   ├── StatusTag.vue              # 状态标签组件
│   ├── RegionSelector.vue         # 地区选择器
│   └── PricingCalculator.vue      # 运费计算器
└── utils/transport/                # 工具函数
    ├── constants.js               # 常量定义
    ├── validators.js              # 验证函数
    └── formatters.js              # 格式化函数
```

## 2. API接口封装

### 2.1 基础API封装 (api/transport/base.js)

```javascript
import request from '@/utils/request'

// 基础API类
export class BaseAPI {
  constructor(module) {
    this.module = module
    this.baseURL = `/transport/${module}`
  }

  // 查询列表
  getList(params) {
    return request({
      url: `${this.baseURL}/list`,
      method: 'get',
      params
    })
  }

  // 获取详情
  getDetail(id) {
    return request({
      url: `${this.baseURL}/${id}`,
      method: 'get'
    })
  }

  // 新增
  create(data) {
    return request({
      url: this.baseURL,
      method: 'post',
      data
    })
  }

  // 修改
  update(data) {
    return request({
      url: this.baseURL,
      method: 'put',
      data
    })
  }

  // 删除
  delete(ids) {
    return request({
      url: `${this.baseURL}/${ids}`,
      method: 'delete'
    })
  }

  // 导出
  export(params) {
    return request({
      url: `${this.baseURL}/export`,
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  }
}
```

### 2.2 运输单API (api/transport/order.js)

```javascript
import { BaseAPI } from './base'
import request from '@/utils/request'

class OrderAPI extends BaseAPI {
  constructor() {
    super('order')
  }

  // 生成运输单号
  generateOrderNo() {
    return request({
      url: `${this.baseURL}/generateOrderNo`,
      method: 'get'
    })
  }

  // 指派车辆和司机
  assign(orderId, vehicleId, driverId) {
    return request({
      url: `${this.baseURL}/assign/${orderId}`,
      method: 'put',
      params: { vehicleId, driverId }
    })
  }

  // 状态流转操作
  startLoading(orderId) {
    return request({
      url: `${this.baseURL}/startLoading/${orderId}`,
      method: 'put'
    })
  }

  completeLoading(orderId) {
    return request({
      url: `${this.baseURL}/completeLoading/${orderId}`,
      method: 'put'
    })
  }

  // 获取下一个可能的状态
  getNextStatuses(currentStatus) {
    return request({
      url: `${this.baseURL}/nextStatuses/${currentStatus}`,
      method: 'get'
    })
  }

  // 统计各状态数量
  getStatusStatistics() {
    return request({
      url: `${this.baseURL}/statistics/status`,
      method: 'get'
    })
  }
}

export default new OrderAPI()
```

## 3. 常量定义 (utils/transport/constants.js)

```javascript
// 运输单状态
export const ORDER_STATUS = {
  PENDING_ASSIGN: { value: 1, label: '待指派', color: 'warning' },
  ASSIGNED: { value: 2, label: '已指派', color: 'info' },
  TO_LOADING: { value: 3, label: '前往装货', color: 'primary' },
  LOADING: { value: 4, label: '装货中', color: 'primary' },
  TRANSPORTING: { value: 5, label: '运输中', color: 'success' },
  DELIVERED: { value: 6, label: '已送达', color: 'success' },
  BILLED: { value: 7, label: '已对账', color: 'default' }
}

// 车辆状态
export const VEHICLE_STATUS = {
  IDLE: { value: 1, label: '空闲', color: 'success' },
  TRANSPORTING: { value: 2, label: '运输中', color: 'warning' },
  MAINTENANCE: { value: 3, label: '维修中', color: 'danger' },
  SCRAPPED: { value: 4, label: '报废', color: 'info' }
}

// 司机状态
export const DRIVER_STATUS = {
  ACTIVE: { value: 1, label: '在职', color: 'success' },
  VACATION: { value: 2, label: '休假', color: 'warning' },
  RESIGNED: { value: 3, label: '离职', color: 'info' }
}

// 客户状态
export const CUSTOMER_STATUS = {
  ACTIVE: { value: 1, label: '正常合作', color: 'success' },
  SUSPENDED: { value: 2, label: '暂停合作', color: 'warning' },
  TERMINATED: { value: 3, label: '终止合作', color: 'danger' }
}

// 计费方式
export const PRICING_TYPE = {
  BY_DISTANCE: { value: 1, label: '按距离' },
  BY_WEIGHT: { value: 2, label: '按重量' },
  BY_VOLUME: { value: 3, label: '按体积' },
  FIXED_PRICE: { value: 4, label: '固定价格' }
}

// 获取状态标签
export function getStatusLabel(statusMap, value) {
  const status = Object.values(statusMap).find(item => item.value === value)
  return status ? status.label : '未知'
}

// 获取状态颜色
export function getStatusColor(statusMap, value) {
  const status = Object.values(statusMap).find(item => item.value === value)
  return status ? status.color : 'default'
}
```

## 4. 通用组件

### 4.1 状态标签组件 (components/transport/StatusTag.vue)

```vue
<template>
  <el-tag :type="statusColor" :size="size">
    {{ statusLabel }}
  </el-tag>
</template>

<script>
import { ORDER_STATUS, VEHICLE_STATUS, DRIVER_STATUS, CUSTOMER_STATUS } from '@/utils/transport/constants'

export default {
  name: 'StatusTag',
  props: {
    type: {
      type: String,
      required: true,
      validator: value => ['order', 'vehicle', 'driver', 'customer'].includes(value)
    },
    value: {
      type: Number,
      required: true
    },
    size: {
      type: String,
      default: 'small'
    }
  },
  computed: {
    statusMap() {
      const maps = {
        order: ORDER_STATUS,
        vehicle: VEHICLE_STATUS,
        driver: DRIVER_STATUS,
        customer: CUSTOMER_STATUS
      }
      return maps[this.type]
    },
    statusInfo() {
      return Object.values(this.statusMap).find(item => item.value === this.value)
    },
    statusLabel() {
      return this.statusInfo ? this.statusInfo.label : '未知'
    },
    statusColor() {
      return this.statusInfo ? this.statusInfo.color : 'info'
    }
  }
}
</script>
```

### 4.2 运输单状态流转组件 (components/transport/OrderStatusFlow.vue)

```vue
<template>
  <div class="order-status-flow">
    <el-steps :active="currentStepIndex" finish-status="success">
      <el-step
        v-for="(step, index) in steps"
        :key="step.value"
        :title="step.label"
        :description="getStepTime(step.value)"
      />
    </el-steps>
    
    <div class="action-buttons" v-if="showActions">
      <el-button
        v-for="action in availableActions"
        :key="action.status"
        :type="action.type"
        :loading="loading"
        @click="handleStatusChange(action.status)"
      >
        {{ action.label }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { ORDER_STATUS } from '@/utils/transport/constants'
import orderAPI from '@/api/transport/order'

export default {
  name: 'OrderStatusFlow',
  props: {
    order: {
      type: Object,
      required: true
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      steps: [
        { value: 1, label: '待指派' },
        { value: 2, label: '已指派' },
        { value: 3, label: '前往装货' },
        { value: 4, label: '装货中' },
        { value: 5, label: '运输中' },
        { value: 6, label: '已送达' },
        { value: 7, label: '已对账' }
      ]
    }
  },
  computed: {
    currentStepIndex() {
      return this.steps.findIndex(step => step.value === this.order.orderStatus)
    },
    availableActions() {
      const actions = []
      const status = this.order.orderStatus
      
      if (status === 3) {
        actions.push({ status: 4, label: '开始装货', type: 'primary' })
      } else if (status === 4) {
        actions.push({ status: 5, label: '完成装货', type: 'success' })
      } else if (status === 5) {
        actions.push({ status: 6, label: '完成配送', type: 'success' })
      } else if (status === 6) {
        actions.push({ status: 7, label: '完成对账', type: 'warning' })
      }
      
      return actions
    }
  },
  methods: {
    getStepTime(status) {
      // 根据状态返回对应的时间
      const timeMap = {
        2: this.order.assignTime,
        4: this.order.actualLoadingTime,
        5: this.order.departureTime,
        6: this.order.actualDeliveryTime,
        7: this.order.billingTime
      }
      return timeMap[status] ? this.$moment(timeMap[status]).format('MM-DD HH:mm') : ''
    },
    
    async handleStatusChange(targetStatus) {
      this.loading = true
      try {
        const actionMap = {
          4: () => orderAPI.startLoading(this.order.id),
          5: () => orderAPI.completeLoading(this.order.id),
          6: () => orderAPI.completeDelivery(this.order.id),
          7: () => orderAPI.completeBilling(this.order.id)
        }
        
        await actionMap[targetStatus]()
        this.$message.success('操作成功')
        this.$emit('status-changed', targetStatus)
      } catch (error) {
        this.$message.error(error.message || '操作失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.order-status-flow {
  padding: 20px;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
```

## 5. 表单验证规则 (utils/transport/validators.js)

```javascript
// 车牌号验证
export function validateLicensePlate(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入车牌号'))
  } else if (!/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/.test(value)) {
    callback(new Error('车牌号格式不正确'))
  } else {
    callback()
  }
}

// 手机号验证
export function validatePhone(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入手机号'))
  } else if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('手机号格式不正确'))
  } else {
    callback()
  }
}

// 身份证号验证
export function validateIdCard(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入身份证号'))
  } else if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
    callback(new Error('身份证号格式不正确'))
  } else {
    callback()
  }
}

// 金额验证
export function validateAmount(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入金额'))
  } else if (!/^\d+(\.\d{1,2})?$/.test(value)) {
    callback(new Error('金额格式不正确'))
  } else if (parseFloat(value) <= 0) {
    callback(new Error('金额必须大于0'))
  } else {
    callback()
  }
}

// 表单验证规则
export const formRules = {
  // 运输单验证规则
  transportOrder: {
    customerName: [
      { required: true, message: '请输入客户名称', trigger: 'blur' }
    ],
    consigneeName: [
      { required: true, message: '请输入收货方名称', trigger: 'blur' }
    ],
    consigneeAddress: [
      { required: true, message: '请输入收货地址', trigger: 'blur' }
    ],
    loadingPointName: [
      { required: true, message: '请输入装货点名称', trigger: 'blur' }
    ],
    productName: [
      { required: true, message: '请输入油品名称', trigger: 'blur' }
    ],
    productQuantity: [
      { required: true, message: '请输入运输数量', trigger: 'blur' },
      { validator: validateAmount, trigger: 'blur' }
    ],
    shippingCost: [
      { required: true, message: '请输入运输费用', trigger: 'blur' },
      { validator: validateAmount, trigger: 'blur' }
    ]
  },
  
  // 车辆验证规则
  vehicle: {
    licensePlate: [
      { required: true, message: '请输入车牌号', trigger: 'blur' },
      { validator: validateLicensePlate, trigger: 'blur' }
    ],
    vehicleType: [
      { required: true, message: '请选择车辆类型', trigger: 'change' }
    ],
    loadCapacity: [
      { required: true, message: '请输入载重吨位', trigger: 'blur' },
      { validator: validateAmount, trigger: 'blur' }
    ]
  },
  
  // 司机验证规则
  driver: {
    driverName: [
      { required: true, message: '请输入司机姓名', trigger: 'blur' }
    ],
    driverPhone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { validator: validatePhone, trigger: 'blur' }
    ],
    idCard: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      { validator: validateIdCard, trigger: 'blur' }
    ],
    licenseType: [
      { required: true, message: '请选择驾照类型', trigger: 'change' }
    ]
  }
}
```

## 6. 开发最佳实践

### 6.1 错误处理
```javascript
// 统一错误处理
export function handleApiError(error, defaultMessage = '操作失败') {
  const message = error.response?.data?.msg || error.message || defaultMessage
  this.$message.error(message)
  console.error('API Error:', error)
}

// 在组件中使用
async loadData() {
  try {
    const response = await orderAPI.getList(this.queryParams)
    this.tableData = response.rows
    this.total = response.total
  } catch (error) {
    this.handleApiError(error, '加载数据失败')
  }
}
```

### 6.2 权限控制
```javascript
// 权限检查指令
Vue.directive('permission', {
  inserted(el, binding) {
    const { value } = binding
    const permissions = store.getters.permissions
    
    if (value && !permissions.includes(value)) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
})

// 在模板中使用
<el-button v-permission="'transport:order:add'" @click="handleAdd">新增</el-button>
```

### 6.3 状态管理
```javascript
// Vuex store for transport module
const transportStore = {
  namespaced: true,
  state: {
    orderList: [],
    vehicleList: [],
    driverList: [],
    currentOrder: null
  },
  mutations: {
    SET_ORDER_LIST(state, list) {
      state.orderList = list
    },
    SET_CURRENT_ORDER(state, order) {
      state.currentOrder = order
    }
  },
  actions: {
    async fetchOrderList({ commit }, params) {
      const response = await orderAPI.getList(params)
      commit('SET_ORDER_LIST', response.rows)
      return response
    }
  }
}
```

## 7. 性能优化建议

1. **列表虚拟滚动** - 大数据量列表使用虚拟滚动
2. **图片懒加载** - 车辆图片等使用懒加载
3. **防抖搜索** - 搜索输入使用防抖
4. **缓存策略** - 基础数据使用缓存
5. **分页加载** - 合理设置分页大小

## 8. 测试建议

1. **单元测试** - 工具函数和组件的单元测试
2. **集成测试** - API接口的集成测试
3. **E2E测试** - 关键业务流程的端到端测试
4. **性能测试** - 大数据量下的性能测试

## 9. 部署注意事项

1. **环境配置** - 不同环境的API地址配置
2. **权限配置** - 确保权限配置正确
3. **缓存策略** - 生产环境的缓存策略
4. **错误监控** - 生产环境的错误监控

这个开发指南为前端开发人员提供了完整的开发规范和最佳实践，可以帮助快速上手运输管理系统的前端开发工作。
