# Swagger访问问题解决指南

## 问题描述
ms-transport项目发布后，Swagger文档无法正常访问。

## 解决方案

### 1. 正确的Swagger访问地址

根据当前配置，Swagger的访问地址应该是：

#### 主要访问地址
```
http://localhost:6877/transport/dev-api/swagger-ui/index.html
```

#### 备用访问地址
```
http://localhost:6877/transport/swagger-ui/index.html
http://localhost:6877/transport/dev-api/doc.html
http://localhost:6877/transport/doc.html
```

### 2. 配置说明

#### application.yml配置
```yaml
server:
  port: 6877
  servlet:
    context-path: /transport

swagger:
  enabled: true
  pathMapping: /dev-api
```

#### 地址组成说明
- **服务器地址**: localhost:6877
- **应用上下文**: /transport
- **API路径映射**: /dev-api
- **Swagger UI路径**: /swagger-ui/index.html

### 3. 已修复的配置问题

#### 3.1 SwaggerConfig.java
- ✅ 修复了API扫描路径
- ✅ 更新了文档标题和描述

#### 3.2 SecurityConfig.java
- ✅ 添加了Swagger相关路径的匿名访问权限
- ✅ 包含了所有必要的Swagger资源路径

#### 3.3 ResourcesConfig.java
- ✅ 添加了Swagger静态资源映射
- ✅ 配置了正确的缓存策略

### 4. 验证步骤

#### 步骤1：检查应用启动
```bash
# 查看应用是否正常启动
curl http://localhost:6877/transport/actuator/health
```

#### 步骤2：检查Swagger配置
```bash
# 检查Swagger配置是否生效
curl http://localhost:6877/transport/dev-api/v2/api-docs
```

#### 步骤3：访问Swagger UI
在浏览器中访问：
```
http://localhost:6877/transport/dev-api/swagger-ui/index.html
```

### 5. 常见问题排查

#### 问题1：404 Not Found
**可能原因**：
- 应用未正常启动
- 端口被占用
- 上下文路径配置错误

**解决方法**：
```bash
# 检查端口占用
netstat -an | grep 6877

# 检查应用日志
tail -f logs/sys-info.log
```

#### 问题2：403 Forbidden
**可能原因**：
- Spring Security配置问题
- Swagger路径未加入白名单

**解决方法**：
检查SecurityConfig.java中的配置：
```java
.antMatchers("/swagger-ui/**", "/swagger-resources/**", "/webjars/**", "/*/api-docs", "/v2/api-docs", "/doc.html").permitAll()
```

#### 问题3：静态资源加载失败
**可能原因**：
- 静态资源映射配置问题
- 缓存问题

**解决方法**：
1. 清除浏览器缓存
2. 检查ResourcesConfig.java配置
3. 重启应用

### 6. 开发环境配置

#### 6.1 IDEA配置
如果使用IDEA开发，确保：
1. 项目正确导入
2. Maven依赖已下载
3. 应用配置文件路径正确

#### 6.2 Maven配置
确保pom.xml中包含Swagger依赖：
```xml
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-boot-starter</artifactId>
</dependency>
```

### 7. 生产环境注意事项

#### 7.1 安全配置
生产环境建议：
```yaml
swagger:
  enabled: false  # 生产环境关闭Swagger
```

#### 7.2 访问控制
如果生产环境需要Swagger，建议：
1. 添加访问认证
2. 限制访问IP
3. 使用HTTPS

### 8. 测试API接口

#### 8.1 Transport相关接口
Swagger中应该能看到以下接口分组：
- **运输单管理** - TransportOrderController
- **车辆管理** - TransportVehicleController  
- **司机管理** - TransportDriverController
- **客户管理** - TransportCustomerController
- **收货方管理** - TransportConsigneeController
- **装货点管理** - TransportLoadingPointController
- **运费规则管理** - TransportPricingRuleController
- **地址管理** - TransportCountriesController

#### 8.2 接口测试
在Swagger UI中可以：
1. 查看接口文档
2. 在线测试接口
3. 查看请求/响应示例
4. 下载API文档

### 9. 故障排除命令

#### 9.1 检查应用状态
```bash
# 检查Java进程
jps -l | grep transport

# 检查端口监听
ss -tlnp | grep 6877

# 检查应用日志
tail -f logs/sys-*.log
```

#### 9.2 重启应用
```bash
# 停止应用
pkill -f transport

# 启动应用
nohup java -jar ms-transport-admin.jar > logs/app.log 2>&1 &
```

### 10. 联系支持

如果以上方法都无法解决问题，请提供：
1. 应用启动日志
2. 浏览器控制台错误信息
3. 网络请求详情
4. 服务器环境信息

## 总结

通过以上配置修复，Swagger应该能够正常访问。主要访问地址是：
```
http://localhost:6877/transport/dev-api/swagger-ui/index.html
```

如果仍然无法访问，请按照故障排除步骤逐一检查。
