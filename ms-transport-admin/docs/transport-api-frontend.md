# 运输管理系统前端对接API文档

## 概述

本文档提供运输管理系统所有Controller的前端对接API接口说明，包括请求参数、响应格式、权限要求等详细信息。

## 通用说明

### 基础URL
```
http://localhost:8080/transport
```

### 通用响应格式
```json
{
  "code": 200,           // 状态码：200-成功，500-失败
  "msg": "操作成功",      // 响应消息
  "data": {},            // 响应数据
  "total": 0             // 分页查询时的总记录数（可选）
}
```

### 分页参数
```json
{
  "pageNum": 1,          // 页码，从1开始
  "pageSize": 10         // 每页记录数
}
```

### 权限说明
- 所有接口都需要登录认证
- 具体权限要求见各接口说明
- 权限格式：`transport:模块:操作`

---

## 1. 运输单管理 (TransportOrderController)

### 1.1 查询运输单列表
**接口地址：** `GET /transport/order/list`  
**权限要求：** `transport:order:list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "orderNo": "YS20240115001",        // 运输单号（模糊查询）
  "internalCode": "NB20240115001",   // 内部编号（模糊查询）
  "customerId": 1,                   // 客户ID
  "customerName": "XX石油公司",       // 客户名称（模糊查询）
  "consigneeName": "XX加油站",        // 收货方名称（模糊查询）
  "orderStatus": 1,                  // 订单状态
  "vehicleId": 1,                    // 车辆ID
  "driverId": 1,                     // 司机ID
  "licensePlate": "京A12345",         // 车牌号（模糊查询）
  "params": {
    "beginTime": "2024-01-01",       // 开始时间
    "endTime": "2024-01-31"          // 结束时间
  }
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "orderNo": "YS20240115001",
      "internalCode": "NB20240115001",
      "customerName": "XX石油贸易公司",
      "consigneeName": "XX加油站",
      "orderStatus": 1,
      "orderStatusText": "待指派",
      "shippingCost": 3000.00,
      "createTime": "2024-01-15 10:30:00"
    }
  ],
  "total": 100
}
```

### 1.2 获取运输单详情
**接口地址：** `GET /transport/order/{id}`  
**权限要求：** `transport:order:query`

**路径参数：**
- `id`: 运输单ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "orderNo": "YS20240115001",
    "internalCode": "NB20240115001",
    "customerId": 1,
    "customerName": "XX石油贸易公司",
    "consigneeId": 1,
    "consigneeName": "XX加油站",
    "consigneeAddress": "北京市朝阳区XX路XX号",
    "loadingPointName": "上海港石油码头",
    "loadingAddress": "上海市浦东新区港口大道1号",
    "productName": "92号汽油",
    "productQuantity": 10.50,
    "totalVolume": 12000.00,
    "transportDistance": 1200.00,
    "orderStatus": 1,
    "orderStatusText": "待指派",
    "vehicleId": null,
    "driverId": null,
    "licensePlate": null,
    "shippingCost": 3000.00,
    "otherExpenses": 0.00,
    "deliveryRequirements": "工作时间配送",
    "specialInstructions": "注意安全",
    "createTime": "2024-01-15 10:30:00"
  }
}
```

### 1.3 新增运输单
**接口地址：** `POST /transport/order`  
**权限要求：** `transport:order:add`

**请求体：**
```json
{
  "customerName": "XX石油贸易公司",
  "consigneeName": "XX加油站",
  "consigneeAddress": "北京市朝阳区XX路XX号",
  "consigneeContact": "张三",
  "consigneePhone": "***********",
  "loadingPointName": "上海港石油码头",
  "loadingAddress": "上海市浦东新区港口大道1号",
  "productName": "92号汽油",
  "productQuantity": 10.50,
  "totalVolume": 12000.00,
  "transportDistance": 1200.00,
  "shippingCost": 3000.00,
  "plannedLoadingTime": "2024-01-16 08:00:00",
  "plannedDeliveryTime": "2024-01-16 18:00:00",
  "deliveryRequirements": "工作时间配送",
  "specialInstructions": "注意安全"
}
```

### 1.4 修改运输单
**接口地址：** `PUT /transport/order`  
**权限要求：** `transport:order:edit`

**请求体：** 同新增接口，需包含id字段

### 1.5 删除运输单
**接口地址：** `DELETE /transport/order/{ids}`  
**权限要求：** `transport:order:remove`

**路径参数：**
- `ids`: 运输单ID数组，多个ID用逗号分隔，如：1,2,3

### 1.6 导出运输单
**接口地址：** `POST /transport/order/export`  
**权限要求：** `transport:order:export`

**请求体：** 同查询列表接口参数

### 1.7 生成运输单号
**接口地址：** `GET /transport/order/generateOrderNo`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "YS20240115001"
}
```

### 1.8 生成内部编号
**接口地址：** `GET /transport/order/generateInternalCode`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "NB20240115001"
}
```

### 1.9 更新运输单状态
**接口地址：** `PUT /transport/order/status/{id}/{status}`  
**权限要求：** `transport:order:edit`

**路径参数：**
- `id`: 运输单ID
- `status`: 新状态（1-7）

### 1.10 指派车辆和司机
**接口地址：** `PUT /transport/order/assign/{id}`  
**权限要求：** `transport:order:assign`

**路径参数：**
- `id`: 运输单ID

**请求参数：**
```json
{
  "vehicleId": 1,    // 车辆ID
  "driverId": 1      // 司机ID
}
```

### 1.11 状态流转操作接口

#### 开始装货
**接口地址：** `PUT /transport/order/startLoading/{id}`  
**权限要求：** `transport:order:edit`

#### 完成装货
**接口地址：** `PUT /transport/order/completeLoading/{id}`  
**权限要求：** `transport:order:edit`

#### 开始运输
**接口地址：** `PUT /transport/order/startTransporting/{id}`  
**权限要求：** `transport:order:edit`

#### 完成配送
**接口地址：** `PUT /transport/order/completeDelivery/{id}`  
**权限要求：** `transport:order:edit`

#### 完成对账
**接口地址：** `PUT /transport/order/completeBilling/{id}`  
**权限要求：** `transport:order:edit`

### 1.12 查询接口

#### 根据车辆ID查询运输单
**接口地址：** `GET /transport/order/vehicle/{vehicleId}`

#### 根据司机ID查询运输单
**接口地址：** `GET /transport/order/driver/{driverId}`

#### 根据客户ID查询运输单
**接口地址：** `GET /transport/order/customer/{customerId}`

#### 统计各状态运输单数量
**接口地址：** `GET /transport/order/statistics/status`

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {"orderStatus": 1, "count": 10},
    {"orderStatus": 2, "count": 5},
    {"orderStatus": 3, "count": 3}
  ]
}
```

#### 查询待指派运输单
**接口地址：** `GET /transport/order/pending`

#### 查询运输中运输单
**接口地址：** `GET /transport/order/transporting`

#### 根据运输单号查询
**接口地址：** `GET /transport/order/orderNo/{orderNo}`

#### 获取下一个可能的状态列表
**接口地址：** `GET /transport/order/nextStatuses/{currentStatus}`

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {"code": 2, "desc": "已指派"}
  ]
}
```

---

## 2. 车辆管理 (TransportVehicleController)

### 2.1 查询车辆列表
**接口地址：** `GET /transport/vehicle/list`  
**权限要求：** `transport:vehicle:list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "licensePlate": "京A12345",     // 车牌号（模糊查询）
  "vehicleType": "油罐车",        // 车辆类型
  "vehicleStatus": 1,            // 车辆状态
  "regionCode": "110000",        // 区域代码
  "params": {
    "beginTime": "2024-01-01",
    "endTime": "2024-01-31"
  }
}
```

### 2.2 获取车辆详情
**接口地址：** `GET /transport/vehicle/{id}`  
**权限要求：** `transport:vehicle:query`

### 2.3 新增车辆
**接口地址：** `POST /transport/vehicle`  
**权限要求：** `transport:vehicle:add`

**请求体：**
```json
{
  "licensePlate": "京A12345",
  "vehicleType": "油罐车",
  "loadCapacity": 20.00,
  "fuelTankCapacity": 300.00,
  "vehicleStatus": 1,
  "purchaseDate": "2023-01-01",
  "annualInspectionDate": "2024-12-31",
  "insuranceExpiryDate": "2024-12-31",
  "regionCode": "110000"
}
```

### 2.4 修改车辆
**接口地址：** `PUT /transport/vehicle`  
**权限要求：** `transport:vehicle:edit`

### 2.5 删除车辆
**接口地址：** `DELETE /transport/vehicle/{ids}`  
**权限要求：** `transport:vehicle:remove`

### 2.6 导出车辆列表
**接口地址：** `POST /transport/vehicle/export`  
**权限要求：** `transport:vehicle:export`

### 2.7 查询可用车辆
**接口地址：** `GET /transport/vehicle/available`

### 2.8 更新车辆状态
**接口地址：** `PUT /transport/vehicle/status/{id}/{status}`  
**权限要求：** `transport:vehicle:edit`

### 2.9 根据车牌号查询车辆
**接口地址：** `GET /transport/vehicle/licensePlate/{licensePlate}`

### 2.10 检查车牌号是否存在
**接口地址：** `GET /transport/vehicle/checkLicensePlate/{licensePlate}`

**请求参数：**
- `id`: 车辆ID（可选，修改时排除自己）

### 2.11 根据区域查询车辆
**接口地址：** `GET /transport/vehicle/region/{regionCode}`

### 2.12 统计各状态车辆数量
**接口地址：** `GET /transport/vehicle/statistics/status`

### 2.13 查询即将到期车辆
**接口地址：** `GET /transport/vehicle/expiring`

**请求参数：**
- `days`: 提前天数（默认30天）

---

## 3. 司机管理 (TransportDriverController)

### 3.1 查询司机列表
**接口地址：** `GET /transport/driver/list`  
**权限要求：** `transport:driver:list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "driverName": "张三",          // 司机姓名（模糊查询）
  "driverPhone": "138",          // 手机号（模糊查询）
  "driverStatus": 1,             // 司机状态
  "licenseType": "A2",           // 驾照类型
  "params": {
    "beginTime": "2024-01-01",
    "endTime": "2024-01-31"
  }
}
```

### 3.2 获取司机详情
**接口地址：** `GET /transport/driver/{id}`  
**权限要求：** `transport:driver:query`

### 3.3 新增司机
**接口地址：** `POST /transport/driver`  
**权限要求：** `transport:driver:add`

**请求体：**
```json
{
  "driverName": "张三",
  "driverPhone": "***********",
  "idCard": "110101199001011234",
  "licenseType": "A2",
  "licenseNumber": "110101199001011234",
  "drivingYears": 5,
  "driverStatus": 1,
  "hireDate": "2023-01-01",
  "emergencyContact": "李四",
  "emergencyPhone": "13800138002"
}
```

### 3.4 修改司机
**接口地址：** `PUT /transport/driver`  
**权限要求：** `transport:driver:edit`

### 3.5 删除司机
**接口地址：** `DELETE /transport/driver/{ids}`  
**权限要求：** `transport:driver:remove`

### 3.6 导出司机列表
**接口地址：** `POST /transport/driver/export`  
**权限要求：** `transport:driver:export`

### 3.7 查询可用司机
**接口地址：** `GET /transport/driver/available`

### 3.8 更新司机状态
**接口地址：** `PUT /transport/driver/status/{id}/{status}`  
**权限要求：** `transport:driver:edit`

### 3.9 根据手机号查询司机
**接口地址：** `GET /transport/driver/phone/{driverPhone}`

### 3.10 检查手机号是否存在
**接口地址：** `GET /transport/driver/checkPhone/{driverPhone}`

### 3.11 检查身份证号是否存在
**接口地址：** `GET /transport/driver/checkIdCard/{idCard}`

### 3.12 统计各状态司机数量
**接口地址：** `GET /transport/driver/statistics/status`

### 3.13 根据驾照类型查询司机
**接口地址：** `GET /transport/driver/licenseType/{licenseType}`

### 3.14 查询经验丰富司机
**接口地址：** `GET /transport/driver/experienced`

**请求参数：**
- `years`: 驾龄年数（默认5年）

---

## 4. 客户管理 (TransportCustomerController)

### 4.1 查询客户列表
**接口地址：** `GET /transport/customer/list`
**权限要求：** `transport:customer:list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "customerName": "XX石油公司",    // 客户名称（模糊查询）
  "customerCode": "CUST001",      // 客户编码（模糊查询）
  "customerStatus": 1,            // 客户状态
  "creditRating": "AAA",          // 信用等级
  "paymentMethod": 1,             // 付款方式
  "params": {
    "beginTime": "2024-01-01",
    "endTime": "2024-01-31"
  }
}
```

### 4.2 获取客户详情
**接口地址：** `GET /transport/customer/{id}`
**权限要求：** `transport:customer:query`

### 4.3 新增客户
**接口地址：** `POST /transport/customer`
**权限要求：** `transport:customer:add`

**请求体：**
```json
{
  "customerName": "XX石油贸易公司",
  "customerCode": "CUST001",
  "companyType": "贸易公司",
  "businessLicense": "91110000123456789X",
  "contactPerson": "张经理",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "provinceName": "北京市",
  "cityName": "北京市",
  "districtName": "朝阳区",
  "detailAddress": "朝阳区XX路XX号",
  "customerStatus": 1,
  "creditRating": "AAA",
  "settlementCycle": 30,
  "paymentMethod": 1,
  "freightDiscount": 95.00,
  "cooperationStartDate": "2024-01-01",
  "mainProducts": "汽油、柴油",
  "deliveryRegions": "华北地区"
}
```

### 4.4 修改客户
**接口地址：** `PUT /transport/customer`
**权限要求：** `transport:customer:edit`

### 4.5 删除客户
**接口地址：** `DELETE /transport/customer/{ids}`
**权限要求：** `transport:customer:remove`

### 4.6 导出客户列表
**接口地址：** `POST /transport/customer/export`
**权限要求：** `transport:customer:export`

### 4.7 根据客户编码查询
**接口地址：** `GET /transport/customer/code/{customerCode}`

### 4.8 检查客户编码是否存在
**接口地址：** `GET /transport/customer/checkCode/{customerCode}`

### 4.9 检查客户名称是否存在
**接口地址：** `GET /transport/customer/checkName/{customerName}`

### 4.10 查询正常合作客户
**接口地址：** `GET /transport/customer/active`

### 4.11 更新客户状态
**接口地址：** `PUT /transport/customer/status/{id}/{status}`
**权限要求：** `transport:customer:edit`

### 4.12 统计各状态客户数量
**接口地址：** `GET /transport/customer/statistics/status`

### 4.13 根据信用等级查询客户
**接口地址：** `GET /transport/customer/creditRating/{creditRating}`

### 4.14 根据付款方式查询客户
**接口地址：** `GET /transport/customer/paymentMethod/{paymentMethod}`

### 4.15 查询VIP客户
**接口地址：** `GET /transport/customer/vip`

---

## 5. 收货方管理 (TransportConsigneeController)

### 5.1 查询收货方列表
**接口地址：** `GET /transport/consignee/list`
**权限要求：** `transport:consignee:list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "consigneeName": "XX加油站",     // 收货方名称（模糊查询）
  "consigneeCode": "CONS001",     // 收货方编码（模糊查询）
  "customerId": 1,                // 客户ID
  "consigneeType": 1,             // 收货方类型
  "isActive": 1,                  // 是否启用
  "params": {
    "beginTime": "2024-01-01",
    "endTime": "2024-01-31"
  }
}
```

### 5.2 获取收货方详情
**接口地址：** `GET /transport/consignee/{id}`
**权限要求：** `transport:consignee:query`

### 5.3 新增收货方
**接口地址：** `POST /transport/consignee`
**权限要求：** `transport:consignee:add`

**请求体：**
```json
{
  "customerId": 1,
  "consigneeName": "XX加油站",
  "consigneeCode": "CONS001",
  "contactPerson": "李站长",
  "contactPhone": "***********",
  "provinceName": "北京市",
  "cityName": "北京市",
  "districtName": "朝阳区",
  "detailAddress": "朝阳区XX路XX号",
  "consigneeType": 1,
  "deliveryRequirements": "工作时间配送，提前1小时通知",
  "isActive": 1
}
```

### 5.4 修改收货方
**接口地址：** `PUT /transport/consignee`
**权限要求：** `transport:consignee:edit`

### 5.5 删除收货方
**接口地址：** `DELETE /transport/consignee/{ids}`
**权限要求：** `transport:consignee:remove`

### 5.6 导出收货方列表
**接口地址：** `POST /transport/consignee/export`
**权限要求：** `transport:consignee:export`

### 5.7 根据客户ID查询收货方
**接口地址：** `GET /transport/consignee/customer/{customerId}`

### 5.8 根据收货方编码查询
**接口地址：** `GET /transport/consignee/code/{consigneeCode}`

### 5.9 检查收货方编码是否存在
**接口地址：** `GET /transport/consignee/checkCode/{consigneeCode}`

### 5.10 查询启用的收货方
**接口地址：** `GET /transport/consignee/active`

### 5.11 更新收货方状态
**接口地址：** `PUT /transport/consignee/status/{id}/{isActive}`
**权限要求：** `transport:consignee:edit`

### 5.12 根据收货方类型查询
**接口地址：** `GET /transport/consignee/type/{consigneeType}`

### 5.13 根据地区查询收货方
**接口地址：** `GET /transport/consignee/region`

**请求参数：**
- `provinceCode`: 省份编码
- `cityCode`: 城市编码（可选）

---

## 6. 装货点管理 (TransportLoadingPointController)

### 6.1 查询装货点列表
**接口地址：** `GET /transport/loadingPoint/list`
**权限要求：** `transport:loadingPoint:list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "pointName": "上海港",          // 装货点名称（模糊查询）
  "pointCode": "LP001",          // 装货点编码（模糊查询）
  "pointType": 1,                // 装货点类型
  "isActive": 1,                 // 是否启用
  "params": {
    "beginTime": "2024-01-01",
    "endTime": "2024-01-31"
  }
}
```

### 6.2 获取装货点详情
**接口地址：** `GET /transport/loadingPoint/{id}`
**权限要求：** `transport:loadingPoint:query`

### 6.3 新增装货点
**接口地址：** `POST /transport/loadingPoint`
**权限要求：** `transport:loadingPoint:add`

**请求体：**
```json
{
  "pointName": "上海港石油码头",
  "pointCode": "LP001",
  "pointType": 1,
  "provinceName": "上海市",
  "cityName": "上海市",
  "districtName": "浦东新区",
  "detailAddress": "浦东新区港口大道1号",
  "contactPerson": "王经理",
  "contactPhone": "***********",
  "operatingHours": "24小时",
  "loadingCapacity": "同时装载10辆车",
  "specialRequirements": "需提前预约",
  "isActive": 1
}
```

### 6.4 修改装货点
**接口地址：** `PUT /transport/loadingPoint`
**权限要求：** `transport:loadingPoint:edit`

### 6.5 删除装货点
**接口地址：** `DELETE /transport/loadingPoint/{ids}`
**权限要求：** `transport:loadingPoint:remove`

### 6.6 导出装货点列表
**接口地址：** `POST /transport/loadingPoint/export`
**权限要求：** `transport:loadingPoint:export`

### 6.7 根据装货点编码查询
**接口地址：** `GET /transport/loadingPoint/code/{pointCode}`

### 6.8 检查装货点编码是否存在
**接口地址：** `GET /transport/loadingPoint/checkCode/{pointCode}`

### 6.9 查询启用的装货点
**接口地址：** `GET /transport/loadingPoint/active`

### 6.10 更新装货点状态
**接口地址：** `PUT /transport/loadingPoint/status/{id}/{isActive}`
**权限要求：** `transport:loadingPoint:edit`

### 6.11 根据装货点类型查询
**接口地址：** `GET /transport/loadingPoint/type/{pointType}`

### 6.12 根据地区查询装货点
**接口地址：** `GET /transport/loadingPoint/region`

**请求参数：**
- `provinceCode`: 省份编码
- `cityCode`: 城市编码（可选）

---

## 7. 运费计算规则管理 (TransportPricingRuleController)

### 7.1 查询运费规则列表
**接口地址：** `GET /transport/pricingRule/list`
**权限要求：** `transport:pricingRule:list`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "ruleName": "通用规则",         // 规则名称（模糊查询）
  "ruleCode": "RULE001",         // 规则编码（模糊查询）
  "pricingType": 1,              // 计费方式
  "customerId": 1,               // 适用客户ID
  "productType": "汽油",          // 适用油品类型
  "ruleStatus": 1,               // 规则状态
  "params": {
    "beginTime": "2024-01-01",
    "endTime": "2024-01-31"
  }
}
```

### 7.2 获取运费规则详情
**接口地址：** `GET /transport/pricingRule/{id}`
**权限要求：** `transport:pricingRule:query`

### 7.3 新增运费规则
**接口地址：** `POST /transport/pricingRule`
**权限要求：** `transport:pricingRule:add`

**请求体：**
```json
{
  "ruleName": "通用按距离计费",
  "ruleCode": "RULE001",
  "pricingType": 1,
  "basePrice": 500.00,
  "unitPrice": 2.50,
  "minPrice": 300.00,
  "maxPrice": 5000.00,
  "customerId": null,
  "routeId": null,
  "productType": null,
  "effectiveDate": "2024-01-01",
  "expiryDate": "2024-12-31",
  "ruleStatus": 1,
  "ruleDesc": "通用按距离计费规则"
}
```

### 7.4 修改运费规则
**接口地址：** `PUT /transport/pricingRule`
**权限要求：** `transport:pricingRule:edit`

### 7.5 删除运费规则
**接口地址：** `DELETE /transport/pricingRule/{ids}`
**权限要求：** `transport:pricingRule:remove`

### 7.6 导出运费规则列表
**接口地址：** `POST /transport/pricingRule/export`
**权限要求：** `transport:pricingRule:export`

### 7.7 查询有效的运费规则
**接口地址：** `GET /transport/pricingRule/active`

### 7.8 计算运输费用
**接口地址：** `POST /transport/pricingRule/calculate`

**请求参数：**
```json
{
  "customerId": 1,               // 客户ID（可选）
  "routeId": 1,                  // 路线ID（可选）
  "productType": "汽油",          // 油品类型（可选）
  "distance": 1200.00,           // 运输距离
  "weight": 10.50,               // 运输重量
  "volume": 12000.00             // 运输体积
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "计算成功",
  "cost": 3500.00
}
```

### 7.9 查找适用的计费规则
**接口地址：** `GET /transport/pricingRule/findRule`

**请求参数：**
- `customerId`: 客户ID（可选）
- `routeId`: 路线ID（可选）
- `productType`: 油品类型（可选）

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "ruleName": "通用按距离计费",
    "pricingType": 1,
    "basePrice": 500.00,
    "unitPrice": 2.50
  }
}
```

---

## 8. 地址管理 (TransportCountriesController)

### 8.1 查询国家列表
**接口地址：** `GET /transport/countries/list`
**权限要求：** `transport:countries:list`

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "code": "CN",
      "name": "中国",
      "provinces": [
        {
          "code": "110000",
          "name": "北京市",
          "countryCode": "CN",
          "cities": [
            {
              "code": "110100",
              "name": "北京市",
              "provinceCode": "110000",
              "districts": [
                {
                  "code": "110101",
                  "name": "东城区",
                  "cityCode": "110100"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 8.2 查询省份列表
**接口地址：** `GET /transport/countries/provinces/{countryCode}`
**权限要求：** `transport:countries:list`

**路径参数：**
- `countryCode`: 国家编码

### 8.3 查询城市列表
**接口地址：** `GET /transport/countries/cities/{provinceCode}`
**权限要求：** `transport:countries:list`

**路径参数：**
- `provinceCode`: 省份编码

### 8.4 查询区县列表
**接口地址：** `GET /transport/countries/districts/{cityCode}`
**权限要求：** `transport:countries:list`

**路径参数：**
- `cityCode`: 城市编码

### 8.5 根据编码查询地址信息

#### 查询国家信息
**接口地址：** `GET /transport/countries/country/{code}`
**权限要求：** `transport:countries:query`

#### 查询省份信息
**接口地址：** `GET /transport/countries/province/{code}`
**权限要求：** `transport:countries:query`

#### 查询城市信息
**接口地址：** `GET /transport/countries/city/{code}`
**权限要求：** `transport:countries:query`

#### 查询区县信息
**接口地址：** `GET /transport/countries/district/{code}`
**权限要求：** `transport:countries:query`

---

## 9. 数据字典

### 8.1 运输单状态 (orderStatus)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 待指派 | 运输单已创建，等待指派车辆和司机 |
| 2 | 已指派 | 已指派车辆和司机，等待前往装货 |
| 3 | 前往装货 | 车辆司机正在前往装货点 |
| 4 | 装货中 | 正在装货点装货 |
| 5 | 运输中 | 装货完成，正在运输途中 |
| 6 | 已送达 | 货物已送达收货方 |
| 7 | 已对账 | 运输完成，已完成对账 |

### 8.2 车辆状态 (vehicleStatus)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 空闲 | 车辆可用，可以接受新的运输任务 |
| 2 | 运输中 | 车辆正在执行运输任务 |
| 3 | 维修中 | 车辆正在维修，暂时不可用 |
| 4 | 报废 | 车辆已报废，不再使用 |

### 8.3 司机状态 (driverStatus)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 在职 | 司机在职，可以接受运输任务 |
| 2 | 休假 | 司机休假中，暂时不可用 |
| 3 | 离职 | 司机已离职 |

### 8.4 客户状态 (customerStatus)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 正常合作 | 客户状态正常，可以接受业务 |
| 2 | 暂停合作 | 暂停与该客户的合作 |
| 3 | 终止合作 | 已终止与该客户的合作关系 |

### 8.5 付款方式 (paymentMethod)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 月结 | 按月结算付款 |
| 2 | 现结 | 现金结算 |
| 3 | 预付 | 预付款方式 |

### 8.6 收货方类型 (consigneeType)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 加油站 | 加油站类型的收货方 |
| 2 | 工厂 | 工厂类型的收货方 |
| 3 | 仓库 | 仓库类型的收货方 |
| 4 | 其他 | 其他类型的收货方 |

### 8.7 装货点类型 (pointType)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 港口 | 港口类型的装货点 |
| 2 | 油库 | 油库类型的装货点 |
| 3 | 炼厂 | 炼厂类型的装货点 |
| 4 | 其他 | 其他类型的装货点 |

### 8.8 计费方式 (pricingType)
| 值 | 描述 | 说明 |
|---|---|---|
| 1 | 按距离 | 根据运输距离计算费用 |
| 2 | 按重量 | 根据货物重量计算费用 |
| 3 | 按体积 | 根据货物体积计算费用 |
| 4 | 固定价格 | 固定价格，不按其他因素计算 |

### 8.9 信用等级 (creditRating)
| 值 | 描述 | 说明 |
|---|---|---|
| AAA | 信用极好 | 最高信用等级 |
| AA | 信用优良 | 较高信用等级 |
| A | 信用良好 | 良好信用等级 |
| B | 信用一般 | 一般信用等级 |
| C | 信用较差 | 较低信用等级 |

---

## 9. 错误码说明

### 9.1 通用错误码
| 错误码 | 错误信息 | 说明 |
|---|---|---|
| 200 | 操作成功 | 请求处理成功 |
| 500 | 操作失败 | 服务器内部错误 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户没有执行该操作的权限 |
| 404 | 资源不存在 | 请求的资源不存在 |

### 9.2 业务错误码
| 错误码 | 错误信息 | 说明 |
|---|---|---|
| 1001 | 运输单不存在 | 指定的运输单ID不存在 |
| 1002 | 运输单状态不允许该操作 | 当前状态下不能执行该操作 |
| 1003 | 车辆不可用 | 车辆状态不是空闲状态 |
| 1004 | 司机不可用 | 司机状态不是在职状态 |
| 1005 | 车牌号已存在 | 车牌号重复 |
| 1006 | 手机号已存在 | 司机手机号重复 |
| 1007 | 身份证号已存在 | 司机身份证号重复 |
| 1008 | 客户编码已存在 | 客户编码重复 |
| 1009 | 客户名称已存在 | 客户名称重复 |
| 1010 | 收货方编码已存在 | 收货方编码重复 |
| 1011 | 装货点编码已存在 | 装货点编码重复 |
| 1012 | 运费计算失败 | 未找到适用的计费规则 |
| 1013 | 状态流转不合法 | 不允许的状态流转 |

---

## 10. 前端开发建议

### 10.1 状态管理
- 运输单状态需要根据当前状态显示不同的操作按钮
- 状态流转按钮应该根据权限和业务规则动态显示
- 建议使用状态机模式管理复杂的状态流转

### 10.2 数据验证
- 前端应该实现基本的数据验证，如必填字段、格式验证等
- 车牌号、手机号、身份证号等应该有格式验证
- 数值类型字段应该有范围验证

### 10.3 用户体验
- 长列表建议使用虚拟滚动或分页加载
- 重要操作（如删除、状态变更）应该有确认提示
- 表单提交时应该有loading状态和防重复提交

### 10.4 权限控制
- 按钮和菜单应该根据用户权限动态显示/隐藏
- 接口调用失败时应该有友好的错误提示
- 建议在路由层面也做权限控制

### 10.5 数据展示
- 状态、类型等枚举值应该显示中文描述而不是数字
- 时间字段应该格式化显示
- 金额字段应该格式化显示（千分位、小数位等）

### 10.6 搜索和筛选
- 支持多条件组合搜索
- 常用搜索条件可以做成快捷筛选
- 搜索结果应该支持排序

### 10.7 导入导出
- 导出功能应该支持选择导出字段
- 导入时应该有数据验证和错误提示
- 大量数据导入应该有进度提示

---

## 11. 接口调用示例

### 11.1 JavaScript/Axios示例
```javascript
// 查询运输单列表
const getTransportOrderList = async (params) => {
  try {
    const response = await axios.get('/transport/order/list', { params });
    return response.data;
  } catch (error) {
    console.error('查询运输单列表失败:', error);
    throw error;
  }
};

// 创建运输单
const createTransportOrder = async (data) => {
  try {
    const response = await axios.post('/transport/order', data);
    return response.data;
  } catch (error) {
    console.error('创建运输单失败:', error);
    throw error;
  }
};

// 指派车辆和司机
const assignVehicleAndDriver = async (orderId, vehicleId, driverId) => {
  try {
    const response = await axios.put(`/transport/order/assign/${orderId}`, null, {
      params: { vehicleId, driverId }
    });
    return response.data;
  } catch (error) {
    console.error('指派失败:', error);
    throw error;
  }
};
```

### 11.2 Vue.js组件示例
```vue
<template>
  <div>
    <!-- 运输单列表 -->
    <el-table :data="orderList" v-loading="loading">
      <el-table-column prop="orderNo" label="运输单号" />
      <el-table-column prop="customerName" label="客户名称" />
      <el-table-column prop="orderStatusText" label="状态" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button
            v-if="row.orderStatus === 1"
            @click="showAssignDialog(row)"
            type="primary"
            size="small">
            指派
          </el-button>
          <el-button
            v-if="row.orderStatus === 3"
            @click="startLoading(row.id)"
            type="success"
            size="small">
            开始装货
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      orderList: [],
      loading: false
    };
  },
  methods: {
    async loadOrderList() {
      this.loading = true;
      try {
        const response = await this.getTransportOrderList({
          pageNum: 1,
          pageSize: 10
        });
        this.orderList = response.rows;
      } catch (error) {
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },

    async startLoading(orderId) {
      try {
        await axios.put(`/transport/order/startLoading/${orderId}`);
        this.$message.success('操作成功');
        this.loadOrderList();
      } catch (error) {
        this.$message.error(error.response?.data?.msg || '操作失败');
      }
    }
  },

  mounted() {
    this.loadOrderList();
  }
};
</script>
```

---

## 12. 总结

本文档提供了运输管理系统所有Controller的完整API接口说明，包括：

1. **7个核心Controller** - 涵盖运输单、车辆、司机、客户、收货方、装货点、运费规则管理
2. **100+个API接口** - 包括CRUD操作、业务查询、状态管理等
3. **完整的数据字典** - 所有枚举值的详细说明
4. **错误码说明** - 便于前端错误处理
5. **开发建议** - 前端开发的最佳实践
6. **代码示例** - 实际的调用示例

前端开发人员可以根据此文档进行接口对接，实现完整的运输管理系统前端功能。
