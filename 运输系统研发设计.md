
# 运输管理系统研发设计文档

## 1. 项目概述

### 1.1 项目背景
开发独立的运输管理系统，专门用于油品运输业务的全流程管理，包括车辆管理、司机管理、运输单管理、任务调度、运价计算和对账管理等核心功能。系统完全独立运行，不与其他系统进行数据交互。

### 1.2 技术架构
- **基础框架**: Spring Boot 2.5.15 + MyBatis + Redis
- **数据库**: MySQL 8.0
- **前端技术**: Vue.js + Element UI
- **缓存策略**: Redis (独立前缀 `transport:`)
- **部署方式**: 独立部署，端口 6877，上下文路径 `/transport`

## 2. 数据库设计规范

### 2.1 统一字段规范
所有业务表统一包含以下基础字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT AUTO_INCREMENT | 唯一标识ID，主键 |
| create_by | VARCHAR(100) | 创建人 |
| create_time | TIMESTAMP | 创建时间，默认当前时间 |
| update_by | VARCHAR(100) | 修改人 |
| update_time | TIMESTAMP | 修改时间，自动更新 |
| is_deleted | TINYINT(1) | 逻辑删除标志，0-未删除，1-已删除 |

### 2.2 表命名规范
- 所有表名以 `transport_` 为前缀
- 使用下划线分隔单词
- 表名使用英文小写

## 3. 功能模块设计

### 3.1 客户管理模块

#### 3.1.1 委托客户档案管理
**数据结构设计**:
```sql
-- 委托客户信息表(运输服务的委托方)
CREATE TABLE transport_customer (
    id INT AUTO_INCREMENT COMMENT '唯一标识ID' PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL COMMENT '委托客户名称',
    customer_code VARCHAR(50) UNIQUE COMMENT '客户编码',
    company_type VARCHAR(100) COMMENT '公司类型(贸易公司/生产企业/经销商等)',
    contact_person VARCHAR(100) COMMENT '主要联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    country_code VARCHAR(10) COMMENT '国家编码',
    country_name VARCHAR(50) COMMENT '国家名称',
    province_code VARCHAR(10) COMMENT '省份编码',
    province_name VARCHAR(50) COMMENT '省份名称',
    city_code VARCHAR(10) COMMENT '城市编码',
    city_name VARCHAR(50) COMMENT '城市名称',
    detail_address VARCHAR(500) COMMENT '详细地址',
    customer_status TINYINT DEFAULT 1 COMMENT '客户状态:1-正常合作,2-暂停合作,3-终止合作',
    credit_rating VARCHAR(10) COMMENT '信用等级(AAA/AA/A/B/C)',
    settlement_cycle INT DEFAULT 30 COMMENT '结算周期(天)',
    payment_method TINYINT DEFAULT 1 COMMENT '付款方式:1-月结,2-现结,3-预付',
    freight_discount DECIMAL(5,2) DEFAULT 100.00 COMMENT '运费折扣(%)',
    cooperation_start_date DATE COMMENT '合作开始日期',
    main_products TEXT COMMENT '主要经营产品',
    delivery_regions TEXT COMMENT '主要配送区域',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(100) COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(100) COMMENT '修改人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    is_deleted TINYINT(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除'
) COMMENT '委托客户信息表' CHARSET = utf8;
```

### 3.2 运价管理模块

#### 3.2.1 运价规则设计
**数据结构设计**:
```sql
-- 运费定价信息表
CREATE TABLE transport_shipping_rates (
    id                    INT AUTO_INCREMENT COMMENT '唯一标识ID' PRIMARY KEY,
    company_id            INT                                  NOT NULL COMMENT '运输公司ID',
    company_name          VARCHAR(255)                         NULL COMMENT '运输公司名称',
    start_location        VARCHAR(255)                         NOT NULL COMMENT '起点位置',
    end_location          VARCHAR(255)                         NOT NULL COMMENT '终点位置',
    end_city_code         VARCHAR(10)                          NOT NULL COMMENT '结束的市编码',
    start_city_code       VARCHAR(10)                          NOT NULL COMMENT '开始的市编码',
    freight_unit          DECIMAL(10, 2)                       NOT NULL COMMENT '运费单价',
    tax_included          DECIMAL(12, 4)                       NOT NULL COMMENT '含税单价',
    minimum_volume        DECIMAL(10, 2)                       NULL COMMENT '最低起送量',
    internal_codes        TEXT                                 NULL COMMENT '客户内部编号集合',
    rate_type             TINYINT    DEFAULT 0                 NOT NULL COMMENT '规则类型 0 默认 1 特定',
    product_type          VARCHAR(50)                          NULL COMMENT '适用油品类型',
    create_by             VARCHAR(100)                         NULL COMMENT '创建人',
    create_time           TIMESTAMP  DEFAULT CURRENT_TIMESTAMP NULL COMMENT '创建时间',
    update_by             VARCHAR(100)                         NULL COMMENT '修改人',
    update_time           TIMESTAMP  DEFAULT CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    is_deleted            TINYINT(1) DEFAULT 0                 NOT NULL COMMENT '逻辑删除标志，0表示未删除，1表示已删除',
    contract_freight_unit DECIMAL(10, 2)                       NOT NULL COMMENT '合同运费单价',
    contract_tax_included DECIMAL(12, 4)                       NOT NULL COMMENT '合同含税单价'
) COMMENT '运费定价信息表' CHARSET = utf8;

CREATE INDEX idx_company_start_end_location ON transport_shipping_rates (company_id, start_location, end_location);
```

-- 其他模块省略 --
